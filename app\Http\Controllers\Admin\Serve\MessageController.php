<?php

namespace App\Http\Controllers\Admin\Serve;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\Message;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    // 获取参会人跟进消息
    public function getAttendeeMessages(string $id)
    {
        return SUCCESS_RESPONSE_ARRAY(Attendee::find($id)->messages()
            ->whereIn('source', [Message::$source_distribution_submit, Message::$source_user_submit, Message::$source_other_submit])
            ->orderBy('id', 'desc')->get());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }

        $content = $request->input('content');
        $message = new Message();
        $message->attendee_id = $attendee_id;
        $message->content = $content;
        $message->creator = $request->user()->real_name;
        // 如果是对接人提交的消息
        if($attendee->user_id == $request->user()->id){
            $message->source = Message::$source_user_submit;
            $message->is_read = 1;
        } else {
            $message->source = Message::$source_other_submit;
            $message->is_read = 2;
        }
        // 提交消息
        $result = $message->save();
        return SUCCESS_RESPONSE_ARRAY(compact('result'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
