<?php

namespace App\Repositories;

use App\Models\Department;
use App\Models\User;
use Illuminate\Http\Request;

class UserRepository
{
    public function listBuilder(Request $request)
    {
        $phone = $request->input('phone');
        $real_name = $request->input('real_name');
        $role_id = $request->input('role_id');
        $state = $request->input('state');
        $username = $request->input('username');
        $department_ids = $request->input('department_ids');

        return User::when($phone, fn($query) => $query->wherePhone($phone))
            ->when($real_name, fn($query) => $query->where('real_name', 'like', '%' . $real_name . '%'))
            ->when($role_id, fn($query) => $query->whereHas('roles', function ($query) use ($role_id) {
                $query->where('role_id', $role_id);
            }))
            ->when($state, fn($query) => $query->whereState($state))
            ->when($username, fn($query) => $query->whereUsername($username))
            ->when($department_ids, function ($query) use ($department_ids) {
                $query->where(function ($query) use ($department_ids) {
                    $query->whereIn('department_one_id', $department_ids)
                        ->orWhereIn('department_two_id', $department_ids);
                });
            });
    }

    // 获取当前登录用户部门关联的会议活动类型
    public function getUserHasEventActivityTypes(Request $request)
    {
        $user = $request->user();
        if($user->hasRole('系统管理员')){
            return [1,2,3,4,5,6];
        }
        $department_one_id = $user->department_one_id;
        $eventActivityTypes = [];
        if ($department_one_id) {
            $activityTypes = Department::find($department_one_id)->activity_types;
            if ($activityTypes) {
                $eventActivityTypes = json_decode($activityTypes);
            }
        }
        return $eventActivityTypes;
    }
}
