<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Mail\CommonMail;
use App\Models\Website\Speech;
use App\Repositories\SpeechRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Traits\ExcelExport;
use Maatwebsite\Excel\Facades\Excel;

class SpeechController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, SpeechRepository $speechRepository)
    {
        // 查找审核已通过的申请列表
        $request->merge(['status' => 1]);
        return $speechRepository->index($request);
    }

    /**
     * Display a listing of the resource.
     */
    public function list(Request $request, SpeechRepository $speechRepository)
    {
        return $speechRepository->listBuilder($request);
    }

    //导出海报联系方式
    public function export(Request $request)
    {
        try {
            //根据状态查询演讲

            $status = $request->input('status');
            $phone = $request->input('phone');

            $builder = Speech::when($status, fn($query) => $query->where('status', $status))
                ->when($phone, fn($query) => $query->where('phone', $phone));

            $list = $builder->orderBy('id', 'desc')->get();

            // 数据转换逻辑
            $dataTransformCallback = function ($row) {
                $row['status'] = Speech::convertStatus($row['status']) ?? '';
                return $row;
            };

            $title = '演讲人列表';
            $header = [
                'id' => '编号',
                'forum_name' => '专题',
                'title' => '演讲主题',
                'organization' => '单位',
                'position' => '职称职务',
                'speaker' => '演讲人',
                'phone' => '演讲人手机号',
                'email' => '演讲人邮箱',
                'status' => '状态',
                'audit_user' => '审核人',
                'audit_remark' => '审核备注',
                'audit_at' => '审核时间',
            ];

            return Excel::download(new ExcelExport($list, $header, $title, $dataTransformCallback), $title . date('YmdHis') . '.xls');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //新增
        $data = filterRequestData('website_speeches');
        $data['status'] = 2;

        $speech = Speech::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('speech'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Speech $speech)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Speech $speech)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Speech $speech)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Speech $speech)
    {
        //
    }

    // 审核
    public function audit(Request $request, string $id)
    {
        $speech = Speech::find($id);
        if (!$speech) {
            return FAIL_RESPONSE_ARRAY('申请不存在');
        }


        $status = $request->input('status');
        //审核备注
        $audit_remark = $request->input('audit_remark');

        $speech->status = $status;
        $speech->audit_remark = $audit_remark;
        $speech->audit_user = $request->user()->real_name;
        $speech->audit_at = date('Y-m-d H:i:s');
        $speech->update();

        $speaker = $speech->speaker;
        $forum_name = $speech->forum_name;
        $title = $speech->title;
        $sms_content = '尊敬的' . $speaker . '，您好！感谢您提交的演讲申请，SEEE演讲题目：' . $forum_name . ':' . $title . ($status==1?' 审核通过':' 未通过审核') . '。审核反馈：'. $audit_remark;
        //发送短信
//        singleSendJianzhouSms(Sign::SHKSY, $speech->phone, $sms_content);
        try {
            $email = $speech->email;
            $message_str = $sms_content;
            $mail = new CommonMail($message_str);
            Mail::mailer('smtp_sh')->send($mail->from('<EMAIL>', '上海市教育考试院')->to($email));
        } catch (\Exception $e) {
            Log::error('发送邮件失败 $e: '.$e);
            return FAIL_RESPONSE_ARRAY("发送邮件失败");
        }

        return SUCCESS_RESPONSE_ARRAY("审核成功");
    }

    //留言
    public function message(Request $request, string $id)
    {
        $speech = Speech::find($id);
        if (!$speech) {
            return FAIL_RESPONSE_ARRAY('申请不存在');
        }
        $message = $request->input('message');
        if (!$message) {
            return FAIL_RESPONSE_ARRAY('留言内容不能为空');
        }
        $speech->message = $message;
        $speech->update();
        //发送短信
        $sms_content = '尊敬的' . $speech->speaker . '，
您好！感谢您提交的演讲申请，请您及时登录注册时提交的邮箱查收组委会的反馈信息，再次感谢您的参与！';
//        singleSendJianzhouSms(Sign::SHKSY, $speech->phone, $sms_content);

        try {
            $email = $speech->email;
            $message_str = $message;
            $mail = new CommonMail($message_str);
            Mail::mailer('smtp_sh')->send($mail->from('<EMAIL>', '上海市教育考试院')->to($email));
//            Mail::send($mail->from('<EMAIL>', '上海市教育考试院')->to($email));
        } catch (\Exception $e) {
            Log::error('发送邮件失败 $e: '.$e);
            return FAIL_RESPONSE_ARRAY("发送邮件失败");
        }

        return SUCCESS_RESPONSE_ARRAY("留言成功");
    }

}
