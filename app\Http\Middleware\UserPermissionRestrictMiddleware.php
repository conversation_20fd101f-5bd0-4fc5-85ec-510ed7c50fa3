<?php

namespace App\Http\Middleware;

use App\Http\Controllers\Admin\System\UserAccessLogController;
use App\Models\Permission;
use App\Providers\TelescopeServiceProvider;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UserPermissionRestrictMiddleware
{
    const  EXPIRE_DATE = 14;

    public function handle(Request $request, Closure $next)
    {
        $userId = $request->user()->id;
        $roleIds = $request->user()->roles()->pluck('id');
        // 禁用账户不能访问
        $state = $request->user()->state;
        if ($state !== 1) {
            return FAIL_RESPONSE_ARRAY('该账户已禁用');
        }
        // 两周没有登录的，重新登录
        $validDate = Carbon::now()->subDays(self::EXPIRE_DATE);
        $last_login_at = $request->user()->last_login_at;
        if ($validDate->gte($last_login_at)) {
            return response()->json([
                'code' => 401,
                'message' => '账户已过期，请重新登录',
                'data' => null,
            ], 401);
        }

        // 权限限制
        // 1.在权限表没有配置的路由名称，放行
        $routeName = $request->route()->getName();
        $permissions = Permission::pluck('name');
        if (!$permissions->contains($routeName)) {
            return $next($request);
        }
        // 2.查找当前登录用户所有可以访问的权限，如果和当前路由名称匹配则放行,否则提示没有权限
//        $userPermissions = [];
//        $request->user()->roles->each(function ($role) use (&$userPermissions) {
//            $userPermissions[] = $role->permissions->pluck('name')->toArray();
//        });
//        $userPermissions = collect(...$userPermissions)->unique();

        $userPermissions = Permission::whereHas('roles', function ($query) use ($roleIds, $userId) {
            $query->whereIn('id', $roleIds)->whereHas('users', fn($query) => $query->where('id', $userId));
        })->pluck('name');

        if (!$userPermissions->contains($routeName)) {
            return FAIL_RESPONSE_ARRAY('没有权限');
        }
        // 这里记录用户访问日志，只记录配置权限的访问日志。
        if (!app()->environment('local')) {
            $permission = Permission::where('name', $routeName)->first();
            UserAccessLogController::store($request, $permission);
        }

        // 3.用户具有权限的放行
        return $next($request);
    }
}
