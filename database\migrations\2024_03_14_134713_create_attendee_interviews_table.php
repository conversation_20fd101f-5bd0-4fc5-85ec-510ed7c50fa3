<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendee_interviews', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人ID(被采访人)');
            $table->tinyInteger('type')->default(2)->comment('采访类型：1:专访 2:普通采访');
            $table->json('time')->nullable()->comment('采访时间');
            $table->string('theme')->default('')->comment('采访主题');
            $table->string('location')->default('')->comment('采访地点');
            $table->string('interviewer', 20)->default('')->comment('采访人');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `attendee_interviews` comment '采访信息表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendee_interviews');
    }
};
