<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $appid
 * @property string $access_token
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken query()
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken whereAccessToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken whereAppid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WechatToken whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class WechatToken extends Model
{
    use HasFactory;

    protected $guarded = [];


    protected $casts = [
        'expires_at' => 'datetime',
    ];

    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }
}
