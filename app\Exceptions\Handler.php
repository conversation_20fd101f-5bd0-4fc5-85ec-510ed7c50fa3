<?php

namespace App\Exceptions;

use Carbon\Carbon;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Routing\Router;
use Illuminate\Validation\ValidationException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $e)
    {
        $render = parent::render($request, $e);
        if (app()->environment('local')) {
            return $render;
//            return response()->json([
//                'code' => $render->getStatusCode(),
//                'message' => $e->getMessage(),
//                'data' => json_decode($render->getContent()),
//            ], $render->getStatusCode());
        } else {
//            \DB::table('stack_exceptions')->insert([
//                'title' => $request->fullUrl(),
//                'message' => $e->getMessage(),
//                'created_at'=>Carbon::now(),
//            ]);
            // 服务器错误
            return response()->json([
                'code' => $render->getStatusCode(),
                'message' => 'server error,please contact the administrator'
            ], $render->getStatusCode());
        }
    }
}
