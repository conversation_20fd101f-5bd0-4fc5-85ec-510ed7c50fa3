<?php

namespace App\Console\Commands;

use App\Models\Attendee;
use App\Models\User;
use App\Notifications\Message;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;
use Str;


class zyfNotify extends Command
{
    /**
     * The name and signature of the console command.
     * 控制台输入  php artisan app:zyf  执行当前类的handle方法
     * @var string
     */
    protected $signature = 'zyf';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试用command';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // ===================================== 数据库通知 ==========================================
        // 用户登录通知
        $user = User::find(1);

//        $user->notify(new Message($user->real_name . '登录了！'));
//        $user->notify(new Message(['name' => $user->real_name, 'content' => '登录操作', 'type' => 2]));

//
//        // 多个用户通知
//        $users = User::whereIn('id', [1, 3])->get();
//        Notification::send($users, new Message("111111111"));
//
//
//        // 参会人通知
//        $attendee = Attendee::find(114);
//        $attendee->notify(new Message('asdf'));
//
//
//        // 获取用户通知信息
//        foreach ($user->notifications as $notification) {
//            dump($notification->data);
//        }
//
        // 获取所有未读消息，并将消息标记为已读
//        foreach ($user->unreadNotifications as $notification) {
//            $notification->markAsRead();
//        }
//
//        // 批量标记为已读
//        $user->unreadNotifications->markAsRead();



        // ============================== 邮件通知 ============================================
        // 比如：用户$user注册成功，发送邮件验证邮箱
        // 生成一个随机字符串，作为验证邮箱的token
//        $token = Str::random(32);
//        $user->remember_token = $token;
//        $user->save();
//        $user->notify(new Message($user));

//        // 发送带模板邮件
        $data = [
            'title' => 'SEEE会议酒店住宿信息',
            'user_name' => $user->real_name,
            'message' => '您预约的酒店信息已安排好，请查看详细信息。',
            'details' => ['酒店名称: 同济君禧大酒店', '酒店地址: 上海杨浦区彰武路50号', '酒店电话: ...'],
        ];
        $user->notify(new Message($data));


//        dd(222);


    }


}

