<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/7
 * Time 9:20
 */

namespace App\Repositories;

use App\Models\Attendee;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AttendeeForumExport implements FromCollection, WithHeadings, WithEvents
{
    use Exportable, RegistersEventListeners;

    protected $searchResults;

    public function __construct(Collection $searchResults)
    {
        $this->searchResults = $searchResults;
    }

    public function collection()
    {
        return $this->searchResults->map(function ($result) {

            try {// 数据转换示例：将 Organization Type 字段从数字转换为文字
                $audit_status = $this->convertAuditStatusType($result['audit_status']);
                $intention = $this->convertIntentionType($result['intention']);
                return [
                    $result['id'],
                    $result['event']['short_title'],
                    $result['attendee']['registrant']['name'],
                    $result['attendee']['name'],
                    RegistrantExport::hidePhoneNumber($result['attendee']['phone']),
                    Attendee::convertIdentityType($result['attendee']['identity']),
                    $result['attendee']['position'],
                    $result['attendee']['organization'],
                    $result['attendee']['organization_code'],
                    $result['attendee']['organization_type'],
                    $result['forum']['name'],
                    $result['attendee']['channel']? $result['attendee']['channel']['name']:'',
                    $result['attendee']['channel']?
                        $result['attendee']['channel']['department']?
                                $result['attendee']['channel']['department']['parent']?
                                    $result['attendee']['channel']['department']['parent']['name']:$result['attendee']['channel']['department']['name']:'':'',
                    $result['attendee']['user']? $result['attendee']['user']['real_name']:'',
                    $result['attendee']['created_at'],
                    Attendee::convertStatus($result['attendee']['status']),
                    $intention,
                    $result['audit_user'],
                    $result['audit_time'],
                    $audit_status,
                    $result['audit_remark'],
                ];
            } catch (\Exception $e) {
                return [];
            }
        });
    }

    public function headings(): array
    {
        // 返回Excel表头
        return [
            '编号',
            '会议标题',
            '报名人',
            '参会人姓名',
            '手机号',
            '身份类别',
            '职称职务',
            '所在单位',
            '单位代码',
            '单位类型',
            '报名论坛',
            '邀约渠道',
            '邀约渠道事业部',
            '对接人',
            '报名时间',
            '报名状态',
            '参加意向',
            '审核人',
            '审核时间',
            '审核状态',
            '审核备注',
        ];
    }

    protected function convertAuditStatusType($status)
    {
        // 1:已通过 2:待审核 3：已驳回
        switch ($status) {
            case 1:
                return '已通过';
            case 2:
                return '待审核';
            case 3:
                return '已驳回';
            default:
                return '无';
        }
    }

    protected function convertIntentionType($status)
    {
        // 1:已通过 2:待审核 3：已驳回
        switch ($status) {
            case 1:
                return '确认来';
            case 2:
                return '待确认';
            case 3:
                return '不参加';
            default:
                return '无';
        }
    }

}
