<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_event_announcements', function (Blueprint $table) {
            $table->id();
            $table->string('title')->default('')->comment('标题');
            //简介 mysql中text不允许设置默认值
            $table->text('content')->nullable()->comment('简介');
            $table->longText('introduction')->nullable()->comment('详情');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `website_event_announcements` comment '官网会议公告表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('website_event_announcements');
    }
};
