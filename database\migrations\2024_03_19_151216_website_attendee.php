<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //
        Schema::create('website_attendees', function (Blueprint $table) {
            $table->id();
            $table->string('code')->nullable(false)->default('')->comment('邀请码');

            $table->string('name')->nullable(false)->default('')->comment('姓名');
            $table->string('phone')->nullable(false)->default('')->comment('电话');
            $table->tinyInteger('gender')->nullable(false)->default(3)->comment(' 1:男 2:女 3未知');
            $table->string('organization')->default('')->comment('单位名称');
            $table->string('position')->default('')->comment('职称职务');
            $table->string('email')->nullable(false)->default('')->comment('邮箱');

            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `website_attendees` comment '官网报名用户表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('website_attendees');
    }
};
