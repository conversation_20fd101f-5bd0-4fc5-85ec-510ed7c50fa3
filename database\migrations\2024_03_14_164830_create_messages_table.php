<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人ID');
            $table->string('content')->default('')->comment('内容');
            $table->tinyInteger('source')->default(0)->comment('消息来源：1对接人提交 2其他商务提交 3总监分配（新增客户）4客户提交 ');
            $table->tinyInteger('is_read')->default(2)->comment('消息是否已读： 1:已读 2:未读');
            $table->string('creator', 20)->default('')->comment('提交人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `messages` comment '消息表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
