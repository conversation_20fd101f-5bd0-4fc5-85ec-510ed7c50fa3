<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $name 渠道名称
 * @property int $department_id 部门ID
 * @property int $status 1:启用 2:禁用
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Department|null $department
 * @method static \Illuminate\Database\Eloquent\Builder|Channel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel query()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereDepartmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel whereUpdater($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Channel withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Channel withoutTrashed()
 * @mixin \Eloquent
 */
class Channel extends Model
{
    use HasFactory, PaginationTrait;

    protected $casts = [
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    protected $guarded = [];

    public function department()
    {
        return $this->hasOne(Department::class,'id', 'department_two_id');
    }


    public function attendees()
    {
        return $this->hasMany(Attendee::class,'channel_id', 'id');
    }
}
