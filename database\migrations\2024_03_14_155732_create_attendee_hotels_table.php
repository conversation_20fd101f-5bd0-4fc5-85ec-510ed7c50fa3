<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendee_hotels', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人ID');
            $table->tinyInteger('type')->nullable()->default(1)->comment('住宿类型： 1:商务选择酒店，客户选择房间数和房型 2：客户选择酒店和房间数，商务选择房型 3：客户自行办理入住');
            $table->unsignedInteger('hotel_id')->nullable()->default(0)->comment('酒店ID');
            $table->date('check_in_date')->nullable()->comment('入住日期');
            $table->unsignedInteger('check_in_days')->nullable()->default(0)->comment('入住几晚');
            $table->unsignedInteger('booking_room_number')->nullable()->default(0)->comment('预定房间数');
            $table->string('room_type')->nullable()->default('')->comment('房型');
            $table->string('room_number')->nullable()->default('')->comment('房号');
            $table->tinyInteger('is_need_pay')->nullable()->default(2)->comment('是否需要支付： 1:需要 2:不需要');
            $table->string('remark')->nullable()->default('')->comment('备注');
            $table->tinyInteger('is_need_hotel')->default(2)->comment('客户选择是否需要住宿： 1:需要 2:不需要');
            //订单id
            $table->unsignedInteger('order_id')->default(0)->comment('订单ID');

            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `attendee_hotels` comment '参会人住宿关系表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendee_hotels');
    }
};
