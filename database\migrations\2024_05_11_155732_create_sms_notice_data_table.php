<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_notice_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('sms_notice_id')->default(0)->comment('短信通知id');
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人ID');
            $table->string('phone')->nullable(false)->default('')->comment('电话');
            $table->tinyInteger('status')->default(2)->comment('发送状态：1:发送成功 2:发送失败');
            $table->string('remark')->default('')->comment('备注');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `sms_notice_records` comment '短信通知记录表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_notice_records');
    }
};
