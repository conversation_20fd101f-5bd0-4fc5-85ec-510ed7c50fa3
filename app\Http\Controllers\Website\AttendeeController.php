<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Website\Attendee;
use App\Models\Website\InvitationCode;
use Illuminate\Http\Request;

class AttendeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //新增Attendee
//        $data = filterRequestData('website_attendees','mysql_website');
//        $attendee = Attendee::forceCreate($data);

        $attendee = new Attendee();
        $attendee->code = $request->code;
        $attendee->name = $request->name;
        $attendee->phone = $request->phone;
        $attendee->gender = $request->gender;
        $attendee->organization = $request->organization;
        $attendee->position = $request->position;
        $attendee->email = $request->email;
        $attendee->save();
        //根据code查询邀请码，更新邀请码状态
        $invitationCode = InvitationCode::where('code', $request->code)->first();
        if ($invitationCode) {
            $invitationCode->status = 1;
            $invitationCode->save();
        }
        return  $SUCCESS_ARRAY = ['code' => 200, 'message' => '操作成功'];;
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
