<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 16:53
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\AttendeeHotel;
use Illuminate\Http\Request;

class AttendeeHotelRepository
{
    public function listBuilder(Request $request)
    {
        // 会议ID
        $event_id = $request->input('event_id');
        // 参会人单位
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $organization_province = $request->input('organization_province');
        $position = $request->input('position');
        // 参会人姓名
        $name = $request->input('name');
        $phone = $request->input('phone');
        // 酒店ID
        $hotel_id = $request->input('hotel_id');
        $room_type = $request->input('room_type');
        // 住宿时间
        $check_in_dates = $request->input('check_in_dates');
        // 是否需要住宿
        $is_need_hotel = $request->input('is_need_hotel');
        // 对接人
        $user_id_arr = $request->input('user_id_arr');
        $user_ids = AttendeeRepository::getFilterUserIds($user_id_arr);
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = RegistrantRepository::getFilterChannelIds($channel_id_arr);
        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $query = AttendeeHotel::leftjoin('hotels', 'attendee_hotels.hotel_id', '=', 'hotels.id')
            ->join('attendees', 'attendee_hotels.attendee_id', '=', 'attendees.id')
            ->join('events', 'attendees.event_id', '=', 'events.id')
            ->leftjoin('users', 'attendees.user_id', '=', 'users.id')
            ->select('attendee_hotels.id', 'attendee_hotels.is_need_hotel', 'attendee_hotels.type', 'attendee_hotels.attendee_id', 'attendee_hotels.check_in_date', 'attendee_hotels.check_in_days', 'attendee_hotels.room_type',
                'attendee_hotels.room_number', 'attendee_hotels.remark', 'attendee_hotels.is_need_pay', 'attendee_hotels.updater', 'attendee_hotels.updated_at', 'attendee_hotels.order_id',
                'hotels.name as hotel_name',
                'attendees.name as attendee_name', 'attendees.phone as attendee_phone', 'attendees.position as attendee_position', 'attendees.organization as attendee_organization',
                'attendees.organization_code as attendee_organization_code','attendees.organization_type as attendee_organization_type', 'attendees.organization_province as attendee_organization_province','attendees.identity as attendee_identity', 'attendees.event_id', 'attendees.gender',
                'events.short_title as event_short_title',
                'users.real_name as user_real_name')
//            ->where('attendee_hotels.is_need_hotel', 1)
            ->where('attendees.status', Attendee::$status_ok)
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('attendees.event_id', $event_id))
            ->when($organization, fn($query) => $query->where('attendees.organization', 'like', "%$organization%"))
            ->when($organization_code, fn($query) => $query->where('attendees.organization_code', 'like', "%$organization_code%"))
            ->when($organization_province, fn($query) => $query->where('attendees.organization_province', 'like', '%' . $organization_province . '%'))
            ->when($position, fn($query) => $query->where('attendees.position', 'like', "%$position%"))
            ->when($name, fn($query) => $query->where('attendees.name', 'like', "%$name%"))
            ->when($phone, fn($query) => $query->where('attendees.phone', "$phone"))
            ->when($user_id_arr, fn($query) => $query->whereIn('attendees.user_id', $user_ids))
            ->when($channel_id_arr, fn($query) => $query->whereIn('attendees.channel_id', $channel_id))
            ->when($hotel_id, fn($query) => $query->where('attendee_hotels.hotel_id', $hotel_id))
            ->when($room_type, fn($query) => $query->where('attendee_hotels.room_type', $room_type))
            ->when($check_in_dates, fn($query) => $query->where('attendee_hotels.check_in_date', '>=', $check_in_dates[0]))
            ->when($check_in_dates, fn($query) => $query->where('attendee_hotels.check_in_date', '<=', $check_in_dates[1]))
//            ->when($is_need_hotel, fn($query) => $query->where('attendee_hotels.is_need_hotel', $is_need_hotel))
            // 是否住宿
            ->when($is_need_hotel, function ($query) use ($is_need_hotel) {
                // 不需要，自行办理入住
                if ($is_need_hotel == 3)
                    return $query->where('attendee_hotels.is_need_hotel', 2)
                        ->where('attendee_hotels.type', 3);
                else
                    return $query->where('attendee_hotels.is_need_hotel', $is_need_hotel);
            });
        $query = RegistrantRepository::getAuthFilterQuery($request, $query);
        return $query;
    }


}
