<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MenusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $systemMenu = Menu::forceCreate([
            'menu_name' => '系统管理',
            'level' => 1,
            'parent_id' => 0,
            'sort' => 1,
            'creator' => '系统管理员',
            'updater' => '系统管理员',
        ]);
        Menu::forceCreate(['menu_name' => '用户管理', 'url' => '/users', 'level' => 2, 'parent_id' => $systemMenu->id, 'sort' => 2, 'creator' => '系统管理员', 'updater' => '系统管理员',]);
        Menu::forceCreate(['menu_name' => '角色管理', 'url' => '/roles', 'level' => 2, 'parent_id' => $systemMenu->id, 'sort' => 3, 'creator' => '系统管理员', 'updater' => '系统管理员',]);
        Menu::forceCreate(['menu_name' => '权限管理', 'url' => '/permissions', 'level' => 2, 'parent_id' => $systemMenu->id, 'sort' => 4, 'creator' => '系统管理员', 'updater' => '系统管理员',]);
        Menu::forceCreate(['menu_name' => '菜单管理', 'url' => '/menus', 'level' => 2, 'parent_id' => $systemMenu->id, 'sort' => 6, 'creator' => '系统管理员', 'updater' => '系统管理员',]);
        Menu::forceCreate(['menu_name' => '部门管理', 'url' => '/departments', 'level' => 2, 'parent_id' => $systemMenu->id, 'sort' => 7, 'creator' => '系统管理员', 'updater' => '系统管理员',]);
        Menu::forceCreate(['menu_name' => '用户访问记录', 'url' => '/user_access_logs', 'level' => 2, 'parent_id' => $systemMenu->id, 'sort' => 8, 'creator' => '系统管理员', 'updater' => '系统管理员',]);
        dump('menus.id:' . $systemMenu->id);

        // 系统管理员所有菜单
        $role = Role::where('name', '系统管理员')->first();
        if ($role) {
            $role->menus()->sync(Menu::pluck('id')->toArray());
        }


    }
}
