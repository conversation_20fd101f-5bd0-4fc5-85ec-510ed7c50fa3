<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('refund_orders', function (Blueprint $table) {
            $table->id();
            //订单id
            $table->unsignedInteger('order_id')->default(0)->comment('订单id');
            //退款单号
            $table->string('refund_no')->default('')->comment('退款单号');
            //退款人id
            $table->unsignedInteger('member_id')->default(0)->comment('退款人id');
            //退款金额
            $table->decimal('amount', 10, 2)->default(0.00)->comment('退款金额');
            //状态
            $table->tinyInteger('status')->default(1)->comment('状态，1:已退款 2:退款中，3：退款驳回');
            //退费凭证
            $table->string('voucher')->nullable()->default('')->comment('退费凭证');
            //退费备注
            $table->string('remark')->nullable()->default('')->comment('退费备注');
            $table->string('creator', 20)->nullable()->default('')->comment('添加人');
            $table->string('updater', 20)->nullable()->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `refund_orders` comment '退费订单表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('refund_orders');
    }
};
