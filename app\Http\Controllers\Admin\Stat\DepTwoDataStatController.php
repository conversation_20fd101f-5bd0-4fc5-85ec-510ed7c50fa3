<?php

namespace App\Http\Controllers\Admin\Stat;

use App\Http\Controllers\Controller;
use App\Repositories\DepTwoStatRepository;
use Illuminate\Http\Request;

class DepTwoDataStatController extends Controller
{

    // 二级部门会议指标数据统计
    public function getTwoDeptEventKpiDataStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepEventKpiDataStat($request);
    }

    // 二级部门论坛指标数据统计
    public function getTwoDepForumKpiDataStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepForumKpiDataStat($request);
    }

    // 二级部门论坛总指标数据统计
    public function getTwoDepForumAllKpiDataStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepForumAllKpiDataStat($request);
    }

    // 二级部门参会人员身份统计
    public function getTwoDepAttendeeIdentityStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepAttendeeIdentityStat($request);
    }

    // 二级部门近七天邀约参会人人数统计
    public function getTwoDepNear7DaysAttendeeStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepNear7DaysAttendeeStat($request);
    }


    // 二级部门下酒店预定数据统计
    public function getTwoDepHotelBookingDataStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepHotelBookingDataStat($request);
    }

    // 二级部门下某个酒店各事业部入住人数统计
    public function getTwoDepHotelCheckInNumStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepHotelCheckInNumStat($request);
    }

    // 获取当前二级部门下的参会人用餐数据
    public function getAttendeeDineListByTwoDepBusiness(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getAttendeeDineListByTwoDepBusiness($request);
    }

    // 二级部门下各商务的客户跟进数据统计
    public function getTwoDepBusinessFollowDataStat(Request $request, DepTwoStatRepository $repository)
    {
        return $repository->getTwoDepBusinessFollowDataStat($request);
    }


}
