<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_posters', function (Blueprint $table) {
            $table->id();
            //word文件地址
            $table->string('word_url')->default('')->comment('word文件地址');
            //作者信息json
            $table->json('author_info')->comment('作者信息json');
            //手机号
            $table->string('phone')->default('')->comment('手机号');
            //状态
            $table->tinyInteger('status')->default(2)->comment('状态 1审核通过 2审核中 3.驳回');
            //审核人
            $table->string('auditor', 20)->nullable()->default('')->comment('审核人');
            //审核备注
            $table->string('audit_remark')->nullable()->default('')->comment('审核备注');
            //审核时间
            $table->dateTime('audit_time')->nullable()->comment('审核时间');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `website_posters` comment '海报表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('website_posters');
    }
};
