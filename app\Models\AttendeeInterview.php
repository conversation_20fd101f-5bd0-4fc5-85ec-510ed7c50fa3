<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AttendeeInterview extends Model
{
    use HasFactory, PaginationTrait;

    protected $guarded = [];
    protected $casts = [
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];
    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }


    // 属于参会人的采访信息
    public function attendee()
    {
        return $this->belongsTo(Attendee::class, 'attendee_id');
    }

    // 重写获取time属性方法
    public function getTimeAttribute($value)
    {
        // 将 time json字符串转换为Carbon数组
        $value = json_decode($value, true);
        return $value;
    }

    // 用餐类型：1:电子餐券 2:自助餐 3:座位就餐 4：包间宴请
    public static function convertType($type)
    {
        switch ($type) {
            case 1:
                return '专访';
            case 2:
                return '普通采访';
            default:
                return '';
        }
    }
}
