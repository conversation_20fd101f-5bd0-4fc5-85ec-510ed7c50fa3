<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotel_bookings', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('hotel_id')->default(0)->comment('酒店ID');
            $table->date('booking_date')->nullable()->comment('预定日期');
            $table->unsignedInteger('person_number')->default(0)->comment('预定住宿人数');
            $table->unsignedInteger('room_number')->default(0)->comment('预定房间数量');
            $table->unsignedInteger('standard_room_number')->default(0)->comment('预定标准间数');
            $table->decimal('standard_room_price', 10, 2)->default(0.00)->comment('预定标准间价格');
            $table->unsignedInteger('large_bed_room_number')->default(0)->comment('预定大床房间数');
            $table->decimal('large_bed_room_price', 10, 2)->default(0.00)->comment('预定大床房间价格');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `hotel_bookings` comment '酒店住宿预定信息表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_booking_infos');
    }
};
