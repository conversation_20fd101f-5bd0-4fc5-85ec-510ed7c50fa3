<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property int $event_id 会议id
 * @property int $member_id 小程序用户id
 * @property string $name 姓名
 * @property string $phone 电话
 * @property string $wechat 微信号
 * @property string $organization 单位名称
 * @property int $channel_id 邀约渠道id
 * @property int $organization_type 单位类型
 * @property int $identity 身份类别
 * @property int $user_id 对接人id
 * @property string $assigner 分配人
 * @property string|null $assign_at 分配时间
 * @property string $creator 添加人
 * @property string $updater 更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attendee> $attendees
 * @property-read int|null $attendees_count
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant query()
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereAssignAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereAssigner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereIdentity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereOrganization($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereOrganizationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereUpdater($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Registrant whereWechat($value)
 * @property-read \App\Models\Event|null $event
 * @mixin \Eloquent
 */
class Registrant extends Model
{
    use HasFactory, PaginationTrait;


    protected $guarded = [];
    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function converIdentityType($status)
    {
        switch ($status) {
            case 1:
                return '公办学校';
            case 2:
                return '民办学校';
            case 3:
                return '国际化学校';
            case 4:
                return '职业教育';
            case 5:
                return '教育主管部门';
            case 6:
                return '演讲嘉宾';
            case 7:
                return '参展学校';
            case 8:
                return '晚宴';
            case 9:
                return '国际化学校1';
            case 10:
                return 'VEIC-职教社';
            case 11:
                return 'VEIC-民协';
            case 12:
                return 'VEIC-易班网';
            case 13:
                return 'VEIC-远播教育';
            case 14:
                return '民办高中研讨会';
            case 15:
                return '民办高中校长高级研究班';
            default:
                return '未知';
        }
    }

    //报名人下有多个参会人
    public function attendees()
    {
        return $this->hasMany(Attendee::class, 'registrant_id');
    }

    //报名人和会议 一对一
    public function event()
    {
        return $this->hasOne(Event::class, 'id', 'event_id');
    }

//    //渠道一对一
//    public function channel()
//    {
//        return $this->hasOne(Channel::class, 'id', 'channel_id');
//    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
