<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotels', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->default(0)->comment('会议ID');
            $table->string('name')->default('')->comment('酒店名称');
            $table->string('address')->default('')->comment('酒店地址');
            $table->string('remark')->default('')->comment('酒店说明（适合什么人群身份的人入住）');
            $table->string('longitude')->default('')->comment('经度');
            $table->string('latitude')->default('')->comment('纬度');
            $table->tinyInteger('is_allow_select')->default(2)->comment('是否让客户自行选择：1:是 2:否');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');

            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `hotels` comment '酒店住宿管理表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotels');
    }
};
