<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Website\EventMaterial;
use Illuminate\Http\Request;

//会议资料
class EventMaterialController extends Controller
{
    //获取资料列表
    public function index(Request $request)
    {
        // 获取年份
        $year = $request->input('year');
        // 查询所有资料
        $builder = EventMaterial::query()
            ->where('year', $year);
        $cnt = $builder->count();
        $list = $builder->pagination()->orderBy('id', 'asc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    public function create()
    {
        // 显示创建 EventMaterial 的表单
    }

    public function store(Request $request)
    {
        // 保存新创建的 EventMaterial，传入标题、上传文件、添加人为当前登录人
        $material = new EventMaterial();
        $material->title = $request->input('title');
        $material->download_url = $request->input('download_url');
        $material->creator = $request->user()->real_name;
        $material->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    public function show($id)
    {
        // 显示特定 EventMaterial 的详细信息
        $material = EventMaterial::find($id);
        return SUCCESS_RESPONSE_ARRAY($material);
    }

    public function edit($id)
    {
        // 显示更新 EventMaterial 的表单
    }

    public function update(Request $request, $id)
    {
        // 更新 EventMaterial，传入标题、上传文件、更新人为当前登录人
        $material = EventMaterial::find($id);
        $material->title = $request->input('title');
        $material->download_url = $request->input('download_url');
        $material->updater = $request->user()->real_name;
        $material->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    public function destroy($id)
    {
        // 删除 EventMaterial
        $material = EventMaterial::find($id);
        $material->delete();
    }

}
