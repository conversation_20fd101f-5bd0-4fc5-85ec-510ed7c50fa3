<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/7
 * Time 9:20
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\Dine;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AttendeeDineExport implements FromCollection, WithTitle, WithStyles, WithHeadings, WithColumnWidths
{
    use Exportable, RegistersEventListeners;

    protected $searchResults;
    public $count;

    public function __construct(Collection $searchResults)
    {
        $this->searchResults = $searchResults;
        $this->count = $searchResults->count();
    }

    public function collection()
    {
        return $this->searchResults->map(function ($result) {

            try {// 数据转换示例：将 Organization Type 字段从数字转换为文字
                return [
                    $result['id'],
                    $result['event_short_title'],
                    $result['attendee_name'],
                    RegistrantExport::hidePhoneNumber($result['attendee_phone']),
                    Attendee::convertIdentityType($result['attendee_identity']),
                    $result['attendee_organization'],
                    $result['attendee_position'],
                    $result['dine_date'],
                    Dine::convertTimeType($result['time_type']),
                    Dine::convertType($result['type']),
                    $result['location'],
                    $result['user_real_name'],
                    Dine::convertCheckInStatus($result['check_in_status']),
                    $result['updater'],
                    $result['updated_at'],
                    $result['is_need_dine'] === 1 ? '需要' : '不需要',
                    $result['specific_time'],
                ];
            } catch (\Exception $e) {
                return [];
            }
        });
    }

    public function headings(): array
    {
        // 返回Excel表头
        return [
            ['参会人就餐名单服务列表'],
            [
                '编号',
                '会议标题',
                '参会人姓名',
                '参会人手机号',
                '身份类别',
                '所在单位',
                '职称职务',
                '就餐日期',
                '早/中/晚类型',
                '就餐类型',
                '就餐地址',
                '对接人',
                '签到状态',
                '最后更新人',
                '最后更新时间',
                '是否需要就餐',
                '具体就餐时间'
            ]
        ];
    }

    // 设置宽度
    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 30,
            'C' => 15,
            'D' => 15,
            'E' => 15,
            'F' => 15,
            'G' => 15,
            'H' => 15,
            'I' => 15,
            'J' => 15,
            'K' => 25,
            'L' => 10,
            'M' => 10,
            'N' => 10,
            'O' => 18,
            'p' => 18,
            'Q' => 18,
        ];
    }

    public function title(): string
    {
        return '参会人服务信息列表';
    }

    //设置单元格样式

    public function styles(Worksheet $sheet)
    {
        // 合并单元格
        $sheet->mergeCells('A1:Q1');
        // 设置字体颜色
        $sheet->getStyle('A1')->getFont()->getColor()->setRGB('000000');
        $sheet->getStyle('A1:Q2')->applyFromArray([
            //设置水平居中
            'alignment' => ['horizontal' => 'center'],
            //设置字体加粗、大小
            'font'      => ['bold' => true, 'size' => 18],
            // 设置单元格背景颜色
            'fill'      => ['fillType' => 'solid', 'startColor' => ['rgb' => 'e9e9eb']],
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);
        $sheet->getStyle('A2:Q2')->applyFromArray([
            //设置字体大小
            'font'      => ['size' => 12],
        ]);

        // 设置其他单元格边框
        $sheet->getStyle('A3:Q' . ($this->count + 2))->applyFromArray([
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);
    }



}
