<?php

namespace App\Models\Website;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $word_url word文件地址
 * @property array $author_info 作者信息json
 * @property string|null $auditor 审核人
 * @property string|null $audit_remark 审核备注
 * @property string|null $audit_time 审核时间
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Poster newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Poster newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Poster pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|Poster query()
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereAuditRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereAuditTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereAuditor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereAuthorInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereUpdater($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Poster whereWordUrl($value)
 * @mixin \Eloquent
 */
class Poster extends Model
{
    use HasFactory, PaginationTrait;

    protected $connection = 'mysql_website';


    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    protected $casts = [
        'author_info' => 'array',
    ];

    // 状态：1:已通过 2:待审核 3:已驳回
    public static function convertStatus($status)
    {
        switch ($status) {
            case 1:
                return '已通过';
            case 2:
                return '待审核';
            default:
                return '已驳回';
        }
    }
}
