<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 16:53
 */

namespace App\Repositories;

use App\Http\Controllers\Applet\MiniAppController;
use EasyWeChat\MiniApp\Application;
use Illuminate\Support\Facades\Log;

class MiniAppRepository
{
    // 发送订阅消息
    public static function sendMiniAppMessage($template_id, $formattedData, $open_id, $page)
    {
        $app = new Application(MiniAppController::APPLET_CONFIG);
        $app->getAccessToken()->refresh();
        $data = [
            'template_id' => $template_id,
            'touser' => $open_id,
            'page' => $page,
            'data' => $formattedData,
            //developer为开发版；trial为体验版；formal为正式版；默认为正式版
            'miniprogram_state' => 'formal',
            'lang' => 'zh_CN'
        ];
        Log::error('微信订阅消息: '. json_encode($formattedData));
        $response = $app->getClient()->postJson('cgi-bin/message/subscribe/send', $data)->toArray(false);
        Log::error("微信订阅消息发送结果：" . json_encode($response));
        return SUCCESS_RESPONSE_ARRAY($response);

    }

}
