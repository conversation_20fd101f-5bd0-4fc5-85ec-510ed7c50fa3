<?php

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\HotelBooking;
use App\Models\Kpi;
use App\Models\Message;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

class BusinessDataStatRepository
{
    // 获取事业部下商务跟进信息统计
    public function getAttendeeHotelStatByBusiness(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $business_user_id = $request->user()->id;

        // 获取需要住宿的参会人数
        $needHotelCnt = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->selectRaw('attendees.id')
            ->where('attendees.event_id', $event_id)
            ->where('attendees.user_id', $business_user_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            ->groupBy('attendees.id')
            ->get()
            ->count();

        // 获取住宿信息填写完整的参会人数
        $hotelInfoFullCnt = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->selectRaw('attendees.id')
            ->where('attendees.event_id', $event_id)
            ->where('attendees.user_id', $business_user_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            // 酒店住宿信息填写不完整的：hotel_id、room_type、room_number 为空的
            ->where(function ($query){
                $query->whereNotNull('attendee_hotels.hotel_id')
                    ->whereNotNull('attendee_hotels.check_in_date')
                    ->whereNotNull('attendee_hotels.check_in_days')
                    ->whereNotNull('attendee_hotels.booking_room_number')
                    ->whereNotNull('attendee_hotels.room_type');
            })
            ->groupBy('attendees.id')
            ->get()
            ->count();

        // 获取住宿信息填写不完整的参会人数
        $hotelInfoNotFullCnt = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->selectRaw('attendees.id')
            ->where('attendees.event_id', $event_id)
            ->where('attendees.user_id', $business_user_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            // 酒店住宿信息填写不完整的：hotel_id、room_type、room_number 为空的
            ->where(function ($query){
                $query->whereNull('attendee_hotels.hotel_id')
                    ->orWhereNull('attendee_hotels.check_in_date')
                    ->orWhereNull('attendee_hotels.check_in_days')
                    ->orWhereNull('attendee_hotels.booking_room_number')
                    ->orWhereNull('attendee_hotels.room_type');
            })
            ->groupBy('attendees.id')
            ->get()
            ->count();

        return SUCCESS_RESPONSE_ARRAY(compact('needHotelCnt', 'hotelInfoFullCnt', 'hotelInfoNotFullCnt'));
    }

    // 商务对接客户住宿信息填写不完整的参会人列表
    public function getAttendeeHotelInfoNotFullListByBusiness(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $business_user_id = $request->user()->id;

        // 获取住宿信息填写不完整的参会人数
        $list = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->selectRaw('attendees.id, attendees.name, attendees.phone, attendees.identity, attendees.organization,
            attendees.organization_type, attendees.position')
            ->where('attendees.event_id', $event_id)
            ->where('attendees.user_id', $business_user_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            // 酒店住宿信息填写不完整的：hotel_id、room_type、room_number 为空的
            ->where(function ($query){
                $query->whereNull('attendee_hotels.hotel_id')
                    ->orWhereNull('attendee_hotels.check_in_date')
                    ->orWhereNull('attendee_hotels.check_in_days')
                    ->orWhereNull('attendee_hotels.booking_room_number')
                    ->orWhereNull('attendee_hotels.room_type');
            })
            ->groupBy('attendees.id')
            ->get();

        return SUCCESS_RESPONSE_ARRAY($list);
    }



    // 获取用餐统计
    public function getAttendeeDineListByBusiness(Request $request)
    {
        $event_id = $request->input('event_id');
        $dine_date = $request->input('dine_date');
        $time_type = $request->input('time_type');
        $type = $request->input('type');
        $check_in_status = $request->input('check_in_status');
        $location = $request->input('location');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $business_user_id = $request->user()->id;

        $list = Attendee::join('attendee_dines', 'attendees.id', '=', 'attendee_dines.attendee_id')
            ->join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
            ->selectRaw('attendees.id, attendees.name, attendees.phone, attendees.identity, attendees.organization,
            attendees.organization_type, attendees.position, dines.type, dines.dine_date, dines.time_type, dines.location, attendee_dines.check_in_status')
            ->where('attendees.event_id', $event_id)
            ->where('attendees.user_id', $business_user_id)
            ->where('attendee_dines.is_need_dine', 1)
            ->when($dine_date, fn($query) => $query->where('dines.dine_date', $dine_date))
            ->when($time_type, fn($query) => $query->where('dines.time_type', $time_type))
            ->when($type, fn($query) => $query->where('dines.type', $type))
            ->when($location, fn($query) => $query->where('dines.location', $location))
            ->when($check_in_status, fn($query) => $query->where('attendee_dines.check_in_status', $check_in_status))
            ->get();

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取商务跟进信息统计
    public function getFollowDataStatByBusiness(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $business_user_id = $request->user()->id;

        // 客户数
        $attendeeCnt = Attendee::where('attendees.event_id', $event_id)
            ->selectRaw('count(*) AS num')
            ->where('attendees.user_id', $business_user_id)
            ->first()
            ->num ?? 0;
        // 跟进信息数
        $messageCnt = Attendee::join('messages', 'attendees.id', '=', 'messages.attendee_id')
            ->selectRaw('count(*) AS num')
            ->where('attendees.event_id', $event_id)
            ->where('attendees.user_id', $business_user_id)
            ->whereIn('messages.source', [Message::$source_user_submit, Message::$source_other_submit])
            ->first()
            ->num ?? 0;
        // 平均跟进信息数
        $avgMessageCnt = $attendeeCnt ? round($messageCnt / $attendeeCnt, 2) : 0;

        return SUCCESS_RESPONSE_ARRAY(compact('attendeeCnt', 'messageCnt', 'avgMessageCnt'));
    }

    // 获取某商务近七天的跟进数
    public function getNear7DaysFollowNumByBusiness(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $business_user_id = $request->user()->id;

        $sql = "WITH DateRange AS (
                SELECT CURDATE() - INTERVAL 6 DAY AS `date`
                UNION ALL
                SELECT CURDATE() - INTERVAL 5 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 4 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 3 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 2 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 1 DAY
                UNION ALL
                SELECT CURDATE()
            ),
            MessageCounts AS (
                SELECT
                        DATE(a.created_at) AS `date`,
                        COUNT(*) AS num
                FROM messages AS a
                JOIN attendees AS b ON a.attendee_id = b.id
                WHERE b.`status`=1 AND b.event_id = " . $event_id . " AND b.user_id = " . $business_user_id . " AND a.source in (2,3)
                GROUP BY DATE(a.created_at)
            )
            SELECT d.`date`, COALESCE(ac.num, 0) AS num
            FROM DateRange d
            LEFT JOIN MessageCounts ac ON d.`date` = ac.`date`
            ORDER BY d.`date`;";
        $res = \DB::select($sql);

        return SUCCESS_RESPONSE_ARRAY($res);
    }
}
