<?php

namespace App\Models\Website;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $title 标题
 * @property string|null $introduction 简介
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement whereIntroduction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventAnnouncement whereUpdater($value)
 * @mixin \Eloquent
 */
class EventAnnouncement extends Model
{
    use HasFactory, PaginationTrait;

    protected $connection = 'mysql_website';

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }
}
