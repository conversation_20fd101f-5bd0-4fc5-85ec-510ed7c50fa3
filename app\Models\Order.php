<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory, PaginationTrait;

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    protected $appends = ['pay_certificate_arr'];

    // Define an accessor for pay_certificate_arr
    public function getPayCertificateArrAttribute()
    {
        return explode(',', $this->pay_certificate);
    }

//    // 订单归属于一个参会人酒店住宿表
//    public function attendeeHotel()
//    {
//        return $this->belongsTo(AttendeeHotel::class, 'order_id');
//    }
}
