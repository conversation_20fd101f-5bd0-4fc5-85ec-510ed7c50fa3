<?php

use App\Http\Controllers\Admin\Data\HotelController;
use App\Http\Controllers\Admin\Event\AttendeeController;
use App\Http\Controllers\Admin\Event\AttendeeForumController;
use App\Http\Controllers\Admin\Event\EventController;
use App\Http\Controllers\Admin\Event\ForumController;
use App\Http\Controllers\Admin\Event\InviteOrganizationController;
use App\Http\Controllers\Admin\Event\RegistrantController;
use App\Http\Controllers\Admin\Order\InvoiceController;
use App\Http\Controllers\Admin\Order\OrderController;
use App\Http\Controllers\Applet\AppletUserController;
use App\Http\Controllers\Applet\EventWayController;
use App\Http\Controllers\Applet\MemberCenterController;
use App\Http\Controllers\Applet\MiniAppController;
use App\Http\Controllers\Applet\MiniAppPayController;
use Illuminate\Support\Facades\Route;


Route::group(['prefix' => 'applet'], function () {
    Route::get('code_to_session', [MiniAppController::class, 'codeToSession']);
    Route::get('get_phone_number', [MiniAppController::class, 'getPhoneNumber']);
    //getQrCode
    Route::post('get_qr_code', [MiniAppController::class, 'getQrCode']);
    Route::post('get_short_url', [MiniAppController::class, 'getShortUrl']);
    //所有已上架会议
    Route::get('events/all', [EventController::class, 'all']);
    //会议详情
    Route::get('events/{id}', [EventController::class, 'showDetail'])->where('id', '[0-9]+');

    // 当前会议下可自行选择的酒店
    Route::get('hotels/{event_id}', [HotelController::class, 'getAllowSelectHotels'])->where('event_id', '[0-9]+');
    // 获取当前酒店的可预订日期
    Route::get('hotel/bookingDates/{hotel_id}', [HotelController::class, 'getHotelBookingDates'])->where('hotel_id', '[0-9]+');

    // 获取所有酒店信息（客户修改酒店信息页：酒店下拉列表）
    Route::get('member/eventWay/getDropHotels', [EventWayController::class, 'getDropHotels'])->name('member.eventWay.getDropHotels');

    // 会议签到
    Route::get('member/eventWay/eventCheckIn', [EventWayController::class, 'eventCheckIn'])->name('member.eventWay.eventCheckIn');
    // 用餐签到
    Route::get('member/eventWay/dineCheckIn', [EventWayController::class, 'dineCheckIn'])->name('member.eventWay.dineCheckIn');

    //微信支付回调
    Route::any('payment_notify', [MiniAppPayController::class, 'paymentNotify']);
    Route::any('refund_notify', [MiniAppPayController::class, 'refundNotify']);

    Route::get('subscribe', [MiniAppController::class, 'sendSubscribeMessage']);
});

// 这里代码会根据用户open_id,自动创建用户
Route::group(['middleware' => ['auth:applet'], 'prefix' => 'applet'], function () {
    Route::get('get_user_info', [AppletUserController::class, 'getUserInfo']);
    Route::post('bind_phone_validate', [AppletUserController::class, 'bindPhoneValidate']);
    Route::post('bind_phone', [AppletUserController::class, 'bindPhone']);

    //报名
    Route::post('enroll', [RegistrantController::class, 'store']);
    Route::get('enrollHotels/{event_id}', [HotelController::class, 'getEnrollHotels'])->where('event_id', '[0-9]+');
    //获取当前会议的报名人信息
    Route::get('registrant_info/{event_id}', [RegistrantController::class, 'getRegistrantInfo'])->where('event_id', '[0-9]+');
//    Route::get('enroll_info', [RegistrantController::class, 'enrollInfo'])->name('events.registrants.show')->where('id', '[0-9]+');
    //修改参会人
    Route::put('attendee/{id}', [AttendeeController::class, 'update'])->name('events.attendee.update')->where("id", "[0-9]+");
    //修改论坛邀请状态
    Route::put('invite_forums/{id}', [AttendeeForumController::class, 'update'])->name('events.invite_forums.update')->where("id", "[0-9]+");
    //微信小程序支付
    Route::post('payUrl', [MiniAppPayController::class, 'payUrl']);
    //转账汇款
    Route::post('transfer', [MiniAppPayController::class, 'transfer']);


    //邀约单位列表
    Route::get('invite_organizations/{event_id}', [InviteOrganizationController::class, 'all'])->where('event_id', '[0-9]+');
    //论坛列表
    Route::get('{event_id}/forums', [ForumController::class, 'index'])->name('events.forums.list')->where('event_id', '[0-9]+');
    Route::get('forums/{id}', [ForumController::class, 'show'])->name('events.forums.show')->where('id', '[0-9]+');


    // 获取顶部提示消息
    Route::get('member/eventWay/topTips', [EventWayController::class, 'getTopTips'])->name('member.eventWay.topTips');

    // 获取用户所有已报名的会议
    Route::get('member/eventWay/checkInEvents', [EventWayController::class, 'getMemberCheckInEvents'])->name('member.eventWay.checkInEvents');
    // 用户参会通道信息
    Route::get('member/eventWay/info', [EventWayController::class, 'getMemberEventWayInfo'])->name('member.eventWay.info');
    // 提交客户更新接站信息
    Route::put('member/eventWay/updatePickup/{id}', [EventWayController::class, 'updatePickup'])->name('member.eventWay.updatePickup')->where('id', '[0-9]+');
    // 提交客户更新送站信息
    Route::put('member/eventWay/updateDropOff/{id}', [EventWayController::class, 'updateDropOff'])->name('member.eventWay.updateDropOff')->where('id', '[0-9]+');
    // 提交客户更新送站信息
    Route::put('member/eventWay/updateHotel/{id}', [EventWayController::class, 'updateHotel'])->name('member.eventWay.updateHotel')->where('id', '[0-9]+');
    // 提交客户更新用餐信息
    Route::post('member/eventWay/updateDine', [EventWayController::class, 'updateDine'])->name('member.eventWay.updateDine');
    // 获取客户酒店订单信息
    Route::get('member/eventWay/getHotelOrderInfo', [EventWayController::class, 'getHotelOrderInfo'])->name('member.eventWay.getHotelOrderInfo');


    // 获取用户当前会议的签到码信息
    Route::get('member/center/checkInCodes', [MemberCenterController::class, 'getMemberCheckInCodes'])->name('member.center.checkInCodes');
    // 获取用户参加的所有会议
    Route::get('member/center/allEvents', [MemberCenterController::class, 'getMemberAllEvents'])->name('member.center.allEvents');
    // 取消报名
    Route::post('member/center/cancelEvent/{id}', [AttendeeController::class, 'cancelEvent'])->name('member.center.cancelEvent')->where('id', '[0-9]+');

    //我的订单列表
    Route::get('member/center/orders/{type}', [OrderController::class, 'getMemberOrders'])->name('member.center.orders')->where('type', '[0-9]+');

    //退款
    Route::post('member/center/refund', [MiniAppPayController::class, 'refund'])->name('member.center.refund');
    //修改退款信息
    Route::put('member/center/updateRefund/{id}', [OrderController::class, 'updateRefund'])->name('member.center.updateRefund')->where('id', '[0-9]+');
    //修改支付凭证
    Route::put('member/center/updatePayCertificate/{id}', [OrderController::class, 'updatePayCertificate'])->name('member.center.updatePayCertificate');

    //开发票
    Route::post('member/center/invoice', [InvoiceController::class, 'store'])->name('member.center.invoice');
    //我的发票列表
    Route::get('member/center/invoices', [InvoiceController::class, 'myInvoiceList'])->name('member.center.invoices');
    //修改发票信息
    Route::put('member/center/updateInvoice/{id}', [InvoiceController::class, 'update'])->name('member.center.updateInvoice')->where('id', '[0-9]+');

    //发送发票
    Route::post('member/center/send_invoice/{id}', [InvoiceController::class, 'sendInvoice'])->name('member.center.sendInvoice')->where('id', '[0-9]+');
});


