<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendee_pick_ups', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人ID');
            $table->timestamp('arrive_time')->nullable()->comment('嘉宾到达时间');
            $table->timestamp('plan_arrive_time')->nullable()->comment('预计到达时间');
            $table->tinyInteger('is_train')->nullable()->default(1)->comment('是否是乘坐火车飞机：1:是 2:不是');
            $table->string('station_info')->nullable()->default('')->comment('航班/高铁站点信息');
            $table->string('place')->nullable()->default('')->comment('接站地点');
            $table->string('to_place')->nullable()->default('')->comment('送达地点');
            $table->string('hotel_contact', 20)->nullable()->default('')->comment('酒店对接人');
            $table->string('standard')->nullable()->default('')->comment('用车标准');
            $table->string('car_one')->nullable()->default('')->comment('接待车辆1');
            $table->string('car_two')->nullable()->default('')->comment('接待车辆2');
            $table->string('person', 20)->nullable()->default('')->comment('接站人');
            $table->string('tel', 20)->nullable()->default('')->comment('接站人联系方式');
            $table->tinyInteger('is_car_following')->nullable()->default(2)->comment('是否跟车：1:需要 2:不需要 ');
            $table->tinyInteger('is_need_pick_up')->default(1)->comment('客户选择是否需要： 1:需要 2:不需要');

            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('更新人');
            $table->timestamps();

        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `attendee_pick_ups` comment '参会人接站关系表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendee_pick_ups');
    }
};
