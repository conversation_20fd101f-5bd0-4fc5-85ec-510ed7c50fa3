<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 16:53
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\AttendeeForums;
use App\Models\Event;
use App\Models\Forum;
use App\Models\Member;
use App\Models\Registrant;
use App\Models\User;
use AppletUrl;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery\Exception;
use Need;
use Sign;

class AttendeeRepository
{
    public function listBuilder(Request $request)
    {
        $event_id = $request->input('event_id');
        $name = $request->input('name');
        // 单位名称
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $position = $request->input('position');
        $organization_type = $request->input('organization_type');
        $organization_province = $request->input('organization_province');
        $identity = $request->input('identity');
        // 报名人
        $registrant_name = $request->input('registrant_name');
        // 是否跟进
        $is_follow = $request->input('is_follow');
        // 是否签到
        $check_in_status = $request->input('check_in_status');
        // 是否就餐
        $is_need_dine = $request->input('is_need_dine');
        // 是否住宿
        $is_need_hotel = $request->input('is_need_hotel');
        // 是否采访
        $is_need_interview = $request->input('is_need_interview');
        // 是否接站
        $is_need_pick_up = $request->input('is_need_pick_up');
        // 是否送站
        $is_need_drop_off = $request->input('is_need_drop_off');
        // 报名状态
        $status = $request->input('status');

        //渠道
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = RegistrantRepository::getFilterChannelIds($channel_id_arr);
        // 对接人
        $user_id_arr = $request->input('user_id_arr');
        $user_ids = $this->getFilterUserIds($user_id_arr);

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        //查出参会人的会议信息
        $query = Attendee::join('events', 'attendees.event_id', '=', 'events.id')
            ->join('registrants', 'attendees.registrant_id', '=', 'registrants.id')
            ->leftJoin('users', 'attendees.user_id', '=', 'users.id')
            ->with(['lastFollowMessage', 'isDine', 'isHotel', 'notHotel', 'isPickUp', 'isDropOff', 'attendeeInterview'])
            ->select('attendees.*', 'events.title as event_title', 'events.short_title as event_short_title',
                'registrants.name as registrant_name', 'registrants.phone as registrant_phone', 'users.real_name')
            ->whereIn('attendees.status', [Attendee::$status_ok, Attendee::$status_cancel, Attendee::$status_audit])
            ->when($status, fn($query) => $query->where('attendees.status', $status))
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('attendees.event_id', $event_id))
            ->when($name, fn($query) => $query->where('attendees.name', 'like', '%' . $name . '%')->orWhere('attendees.phone', $name))
            ->when($registrant_name, fn($query) => $query->where('registrants.name', 'like', '%' . $registrant_name . '%')->orWhere('registrants.phone', $registrant_name))
            ->when($user_id_arr, fn($query) => $query->whereIn('attendees.user_id', $user_ids))
            ->when($channel_id_arr, fn($query) => $query->whereIn('attendees.channel_id', $channel_id))
            ->when($identity, fn($query) => $query->where('attendees.identity', $identity))
            ->when($organization_type, fn($query) => $query->where('attendees.organization_type', 'like', '%' . $organization_type . '%'))
            ->when($check_in_status, fn($query) => $query->where('attendees.check_in_status', $check_in_status))
            ->when($organization, fn($query) => $query->where('attendees.organization', 'like', '%' . $organization . '%'))
            ->when($organization_code, fn($query) => $query->where('attendees.organization_code', 'like', '%' . $organization_code . '%'))
            ->when($organization_province, fn($query) => $query->where('attendees.organization_province', 'like', '%' . $organization_province . '%'))
            ->when($position, fn($query) => $query->where('attendees.position', 'like', '%' . $position . '%'))
            // 是否跟进
            ->when($is_follow, function ($query) use ($is_follow) {
                // 已跟进
                if ($is_follow == 1)
                    return $query->has('lastFollowMessage');
                // 未跟进
                if ($is_follow == 2)
                    return $query->doesntHave('lastFollowMessage');
            })
            // 是否就餐
            ->when($is_need_dine, function ($query) use ($is_need_dine) {
                // 需要
                if ($is_need_dine == 1)
                    return $query->whereHas('isDine');
                // 不需要
                if ($is_need_dine == 2)
                    return $query->whereDoesntHave('isDine');
            })
            // 是否住宿
            ->when($is_need_hotel, function ($query) use ($is_need_hotel) {
                // 需要
                if ($is_need_hotel == 1)
                    return $query->whereHas('isHotel');
                // 不需要
                if ($is_need_hotel == 2)
                    return $query->whereDoesntHave('isHotel');
                // 不需要，自行办理入住
                if ($is_need_hotel == 3)
                    return $query->whereHas('notHotel');
            })
            // 是否采访
            ->when($is_need_interview, function ($query) use ($is_need_interview) {
                // 需要
                if ($is_need_interview == 1)
                    return $query->whereHas('attendeeInterview');
                // 不需要
                if ($is_need_interview == 2)
                    return $query->whereDoesntHave('attendeeInterview');
            })
            // 是否接站
            ->when($is_need_pick_up, function ($query) use ($is_need_pick_up) {
                // 需要
                if ($is_need_pick_up == 1)
                    return $query->whereHas('isPickUp');
                // 不需要
                if ($is_need_pick_up == 2)
                    return $query->whereDoesntHave('isPickUp');
            })
            // 是否送站
            ->when($is_need_drop_off, function ($query) use ($is_need_drop_off) {
                // 需要
                if ($is_need_drop_off == 1)
                    return $query->whereHas('isDropOff');
                // 不需要
                if ($is_need_drop_off == 2)
                    return $query->whereDoesntHave('isDropOff');
            });

        $query = RegistrantRepository::getAuthFilterQuery($request, $query);
        return $query;
    }

    public function attendeeForums(Request $request)
    {
        // 根据会议id、论坛id，参会人姓名，所在单位，手机号，对接人，邀约渠道检索
        $event_id = $request->input('event_id');
        $name = $request->input('name');
        $phone = $request->input('phone');
        $identity = $request->input('identity');
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $organization_type = $request->input('organization_type');
        $position = $request->input('position');
        $status = $request->input('status');
        $intention = $request->input('intention');
        $audit_status = $request->input('audit_status');
        $forum_id = $request->input('forum_id');
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = RegistrantRepository::getFilterChannelIds($channel_id_arr);

//        $user_id = $request->input('user_id');
        $created_time = $request->input('created_time'); // Added created_time filter
        $user_id_arr = $request->input('user_id_arr');
        // 初始化结果数组
        $user_ids = $this->getFilterUserIds($user_id_arr);

        $attendeeForums = AttendeeForums::query()
            ->with('attendee:id,name,phone,identity,organization,organization_code,organization_type,position,status,event_id,user_id,channel_id,registrant_id,created_at',
                'forum:id,name',
                'event:id,short_title',
                'attendee.registrant:id,channel_id,name',
                'attendee.channel:id,name,department_two_id',
                'attendee.channel.department:id,name,parent_id,code',
                'attendee.channel.department.parent:id,name',
                'attendee.user:id,real_name'
            )
            ->when($event_id, fn($query) => $query->whereIn('event_id', $event_id))
            ->when($intention, fn($query) => $query->where('intention', $intention))
            ->when($audit_status, fn($query) => $query->where('audit_status', $audit_status))
            ->when($name, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('name', 'like', '%' . $name . '%')))
            ->when($phone, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('phone', $phone)))
            ->when($identity, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('identity', $identity)))
            ->when($organization, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('organization', 'like', '%' . $organization . '%')))
            ->when($organization_code, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('organization_code', 'like', '%' . $organization_code . '%')))
            ->when($organization_type, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('organization_type', 'like', '%' . $organization_type . '%')))
            ->when($position, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('position', 'like', '%' . $position . '%')))
            ->when($status, fn($query) => $query->whereHas('attendee', fn($query) => $query->where('status', $status)))
            ->when($user_id_arr, fn($query) => $query->whereHas('attendee', fn($query) => $query->whereIn('user_id', $user_ids)))
            ->when($channel_id_arr, fn($query) => $query->whereHas('attendee.channel.department', fn($query) => $query->whereIn('channels.id', $channel_id)))
            ->when($forum_id, fn($query) => $query->whereHas('forum', fn($query) => $query->where('forum_id', $forum_id)))
            ->when($created_time, fn($query) => $query->whereHas('attendee', function ($query) use ($created_time) {
                if (is_array($created_time) && count($created_time) === 2) {
                    $start_date = $created_time[0];
                    $end_date = $created_time[1];
                    // Adjust end_date to include the entire day
                    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));

                    $query->whereBetween('created_at', [$start_date, $end_date]);
                }
            }));
        return $attendeeForums;
    }

    //邀约论坛inviteForums
    public function inviteForums(Request $request)
    {
        $event_id = $request->input('event_id');
        $attendee_ids = $request->input('attendee_ids');
        $forum_ids = $request->input('forum_ids');
        //邀请类型
        $invite_type = $request->input('invite_type');
        $type = 1;
        $intention = $invite_type==1?2:1;
        $audit_status = 0;

        $update_count = 0;

        foreach ($attendee_ids as $attendee_id) {
            foreach ($forum_ids as $forum_id) {
                $attendeeForum = AttendeeForums::where('attendee_id', $attendee_id)
                    ->where('event_id', $event_id)
                    ->where('forum_id', $forum_id)
                    ->first();

                if ($attendeeForum) {
                    if ($attendeeForum->type == 2 && $attendeeForum->audit_status == 3) {
                        AttendeeForums::create([
                            'attendee_id' => $attendee_id,
                            'forum_id' => $forum_id,
                            'event_id' => $event_id, // 假设event_id来自请求输入
                            'type' => $type, // 其他需要的字段可以从请求中获取
                            'intention' => $intention, // 其他需要的字段可以从请求中获取
                            'audit_status' => $audit_status, // 其他需要的字段可以从请求中获取
                            'audit_remark' => '邀请', // 其他需要的字段可以从请求中获取
                        ]);
                    }
                    // 记录已存在，可在此处更新记录的逻辑，如需要
                    // $attendeeForum->update([...]);
                    // $update_count++;
                    /*$audit_status_old = $attendeeForum->audit_status;
                    $attendeeForum->update([
                        'intention' => $audit_status_old==1?1:$intention,
                        'audit_status' => $invite_type==1?1: $audit_status_old,
                        'audit_remark' => '邀请',
                        'audit_time' => now(),
                        'audit_user' => $request->user()->real_name,
                    ]);*/
                    $update_count++;
                } else {
                    // 记录不存在，创建新记录
                    AttendeeForums::create([
                        'attendee_id' => $attendee_id,
                        'forum_id' => $forum_id,
                        'event_id' => $event_id, // 假设event_id来自请求输入
                        'type' => $type, // 其他需要的字段可以从请求中获取
                        'intention' => $intention, // 其他需要的字段可以从请求中获取
                        'audit_status' => $audit_status, // 其他需要的字段可以从请求中获取
                        'audit_remark' => '邀请', // 其他需要的字段可以从请求中获取
                    ]);
                    $update_count++;
                }
            }
        }
        return $update_count;
    }

    //审核
    public function auditForums(Request $request)
    {
        $forum_ids = $request->input('forum_ids');
        $forums = Forum::whereIn('id', $forum_ids)
            ->orderBy('id', 'desc')->get();

        foreach ($forums as $forum) {
            $maxAttendees = $forum->number;
            $count = $forum->attendees()
                ->where('attendees.status', 1)
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('attendee_forums.type', 2)
                            ->where('attendee_forums.audit_status', 1);
                    })->orWhere(function ($query) {
                        $query->where('attendee_forums.type', 1)
                            ->where('attendee_forums.intention', 1);
                    });
                })
                ->count();

            if ($count > $maxAttendees) {
                // 超出最大人数，终止流程并返回
                return -10;
            }
        }

        $ids = $request->input('ids');
        //审核状态
        $audit_status = $request->input('audit_status');
        //审核备注
        $audit_remark = $request->input('audit_remark');
        //更新审核状态、审核备注，审核时间为当期时间
        $update_count = AttendeeForums::query()->whereIn('id', $ids)->update(['audit_status' => $audit_status, 'audit_remark' => $audit_remark, 'audit_user' => $request->user()->real_name, 'audit_time' => now()]);
        if ($update_count !== count($ids)) {
            Log::warning('部分审核未成功，期望更新: ' . count($ids) . ' 实际更新: ' . $update_count);
        }
        Log::info('论坛审核完成，更新记录数：', ['update_count' => $update_count]);
        // 审核成功，发送短信
        if ($update_count>0) {
            $this->sendSmsToAttendees($ids, $audit_status, $audit_remark);
        }
        return $update_count;
    }

    function sendSmsToAttendees($attendeeForumIds, $auditStatus, $auditRemark) {
        $attendeeForums = AttendeeForums::query()->with('forum:id,name,start_time,address', 'attendee:id,name,phone,registrant_id')
            ->whereIn('id', $attendeeForumIds)->get();

        // 配置短信内容常量
        $approvedSms = "您好，您报名的forumName已审核通过，将于startTime在:address举行，点击前往：url";
        $rejectedSms = "您好，您报名的forumName已审核驳回，驳回原因：auditRemark，点击前往查看：url";

        // 根据audit_status选择短信内容
        $smsContent = $auditStatus === 1 ? $approvedSms : $rejectedSms;

        // 组织发送短信的参数
        foreach ($attendeeForums as $attendeeForum) {
            Log::error('论坛审核 getAppletUrlLink开始');
            $params = [
                'forumName' => $attendeeForum->forum->name,
                'startTime' => $attendeeForum->forum->start_time,
                'address' => $attendeeForum->forum->address,
                'url' => getAppletUrlLink(null),
                'auditRemark' => $auditRemark
            ];
            Log::error('论坛审核 getAppletUrlLink结束');

            if (isset($attendeeForum->attendee)) {
                Log::error('论坛审核发送短信: '. json_encode($attendeeForum));
                if (isset($attendeeForum->attendee->phone)) {
                    singleSendJianzhouSms(Sign::SEEE, $attendeeForum->attendee->phone, replaceParams($smsContent, $params));
                }

//                //根据id查询报名人
//                $registrant = Registrant::find($attendeeForum->attendee->registrant_id);
//                if ($registrant->phone) {
//                    //给报名人发送短信
//                    singleSendJianzhouSms(Sign::SEEE, $registrant->phone, replaceParams($smsContent, $params));
//                }
//
                Log::error('微信推送判断: '. isset($attendeeForum->attendee->member_id));
                //推送微信通知
                if (isset($attendeeForum->attendee->member_id)) {
                    $member = Member::find($attendeeForum->attendee->member_id);
                    $template_id = 'Q3kQQ3w9svmQrfR1liZxIzMR3pZUqSeIePPFXqXE7pM';
                    $formattedData = [
                        "thing2" => [
                            "value" => $attendeeForum->forum->name,
                        ],
                        "time3" => [
                            "value" => $attendeeForum->forum->start_time,
                        ],
                        "thing16" => [
                            "value" => $attendeeForum->forum->address
                        ],
                        "phrase1" => [
                            "value" => AttendeeForums::convertAuditStatus($auditStatus),
                        ],
                        "thing5" => [
                            "value" => $auditStatus === 1 ? '您好，您报名的论坛已审核通过' : '您好，您报名的论坛已审核驳回，驳回原因：' . $auditRemark,
                        ]
                    ];
                    $open_id = $member->open_id;
                    $page = 'pages/customer/channel/channel';
                    MiniAppRepository::sendMiniAppMessage($template_id, $formattedData, $open_id, $page);
                }
            }
        }
    }

    //forumsStatistics
    public function forumsStatistics(Request $request)
    {
        //传入forum_ids,统计已通过审核的论坛，格式为论坛名称和已通过的条数
        $forum_ids = $request->input('forum_ids');
        $forums = Forum::leftJoin('attendee_forums', function ($join) use ($forum_ids) {
            $join->on('forums.id', '=', 'attendee_forums.forum_id')
                ->whereIn('forums.id', $forum_ids)
                ->where('attendee_forums.audit_status', 1);
        })
            ->whereIn('forums.id', $forum_ids)
            ->select('forums.id', 'forums.name', DB::raw('COALESCE(count(attendee_forums.id), 0) as total'))
            ->groupBy('forums.id', 'forums.name')
            ->get();

        return $forums;

    }

    //update
    public function update(string $id, Request $request)
    {
        //查询当前参会人
        $attendee = Attendee::find($id);
        //获取member_id
/*        $member_id = $attendee->member_id;
        //获取请求参数
        $phone = $request->input('phone');
        //根据$phone查询是否有其他参会人的phone,如果有返回错误提示
        $attendeeMember = Attendee::query()
            ->where('event_id',  $attendee->event_id)
            ->whereNotIn('status', [2, 4])
            ->where('phone', $attendee['phone'])
            ->where('id', '<>', $id)
            ->first();
        if ($attendeeMember) {
            return FAIL_RESPONSE_ARRAY('参会人手机号:' . $phone . '已报名，请勿重复报名');
        }*/
        //更新参会人信息
        $data = filterRequestData('attendees');
        $data['updater'] = "客户id：" . $request->user()->id . ' 手机号：' . $request->user()->phone;
        $attendee->fill($data)->update();

        // 处理论坛
        $forums =$request->input('forums');
        $this->handleForums($forums, $id, $attendee['event_id']);

        return SUCCESS_RESPONSE_ARRAY("更新成功");
    }

    //报名信息编辑时，校验论坛
/*    private function handleForums($forums, $attendeeId, $eventId)
    {
        // 遍历 $forums，处理新增和删除逻辑
        foreach ($forums as $forum) {
            // 检查记录是否存在
            $existingForum = AttendeeForums::where('event_id', $eventId)
                ->where('forum_id', $forum['id'])
                ->where('attendee_id', $attendeeId)
                ->exists();

            if (!$existingForum) {
                // 获取论坛的最大参会人数
                $formObj = Forum::where('id', $forum['id'])->first();
                //获取number
                $maxAttendees = $formObj->number;

                // 计算当前论坛的参会人数
//                $currentAttendeesCount = AttendeeForums::where('forum_id', $forum['id'])->whereIn('audit_status', [1,2])->count();
                // 计算当前论坛的参会人数
                $currentAttendeesCount = AttendeeForums::where('forum_id', $forum['id'])
                    ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->where('type', 2)
                                ->where('audit_status', 1);
                        })->orWhere(function ($query) {
                            $query->where('type', 1)
                                ->where('intention', 1);
                        });
                    })
                    ->count();

                if ($currentAttendeesCount >= $maxAttendees){
                    throw new Exception($formObj['name'] . '论坛参会人数已超过限制，需修改后进行提交', NUMBER_LIMIT_CODE);
                }

                // 如果记录不存在，新增数据
                AttendeeForums::create([
                    'event_id' => $eventId,
                    'forum_id' => $forum['id'],
                    'attendee_id' => $attendeeId,
                    'audit_status' => $forum['is_audit'] == Need::NEED? 2 : 1,
                    'type' => 2,
                    'intention' => 0,
                ]);
            }
        }

        // 删除多余的数据
        AttendeeForums::where('event_id', $eventId)
            ->where('attendee_id', $attendeeId)
            ->whereNotIn('forum_id', array_column($forums, 'id'))
            ->delete();

    }*/

    private function handleForums($forums, $attendeeId, $eventId)
    {
        // 遍历 $forums，处理新增和删除逻辑
        foreach ($forums as $forum) {
            // 检查记录是否存在
            $existingForum = AttendeeForums::where('event_id', $eventId)
                ->where('forum_id', $forum['id'])
                ->where('attendee_id', $attendeeId)
                ->exists();

            if (!$existingForum) {
                // 如果记录不存在，新增数据
                AttendeeForums::create([
                    'event_id' => $eventId,
                    'forum_id' => $forum['id'],
                    'attendee_id' => $attendeeId,
                    'audit_status' => $forum['is_audit'] == Need::NEED? 2 : 1,
                ]);
            }
        }

        // 获取 $forums 数组中每个对象的 id 属性值组成的数组
        $forumIds = array_column($forums, 'id');
        // 删除多余的数据
        AttendeeForums::where('event_id', $eventId)
            ->where('attendee_id', $attendeeId)
            ->whereNotIn('forum_id', $forumIds)
            ->delete();

    }

    // 获取对接人联动筛选后的用户id
    public static function getFilterUserIds(mixed $user_id_arr)
    {
        // 初始化结果数组
        $department_one_id = [];
        $department_two_id = [];
        $user_id = [];

        // 检查$user_id_arr是否不为空
        if (!empty($user_id_arr)) {
            // 遍历$user_id_arr数组
            foreach ($user_id_arr as $user_id_element) {
                // 分割字符串，得到"."之前和之后的部分
                $parts = explode('.', $user_id_element);
                if (count($parts) == 2) {
                    $prefix = $parts[0];
                    $number = $parts[1];
                    // 根据"."之前的部分决定将数字放入哪个数组
                    if ($prefix == 'one') {
                        $department_one_id[] = $number;
                    } elseif ($prefix == 'two') {
                        $department_two_id[] = $number;
                    } else {
                        $user_id[] = $number;
                    }
                }
            }
        }

        // 查询User表，并根据$department_one_id中的ID获取主键ID
        if (!empty($department_one_id)) {
            $ids_one = User::query()->whereIn('department_one_id', $department_one_id)->pluck('id')->toArray();
            $user_id = array_merge($user_id, $ids_one);
        }

        // 查询User表，并根据$department_two_id中的ID获取主键ID
        if (!empty($department_two_id)) {
            $ids_two = User::query()->whereIn('department_two_id', $department_two_id)->pluck('id')->toArray();
            $user_id = array_merge($user_id, $ids_two);
        }

        // 去重$user_id数组
        $user_ids = array_unique($user_id);
        return $user_ids;
    }

}
