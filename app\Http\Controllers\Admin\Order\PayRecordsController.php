<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Models\PayRecord;
use App\Repositories\PayRecordExport;
use App\Repositories\PayRecordImport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PayRecordsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = $this->getQuery($request);

        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt' ));

    }

    //导入缴费记录
    public function import(Request $request)
    {
        $file = $request->file('file');
        try {
            // 执行导入操作
            $import = new PayRecordImport($request);
            // 执行导入操作
            Excel::import($import, $file);
            // 获取导入结果
            $importResult = (object)[
                'importedData' => $import->getImportedData(),
                'failedData' => $import->getFailedData(),
            ];
            return SUCCESS_RESPONSE_ARRAY($importResult);
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY("导入失败");
        }
    }

    //导出
    public function export(Request $request)
    {
        $query = $this->getQuery($request);
        $list = $query->get();
        return Excel::download(new PayRecordExport($list), '缴费记录.xlsx');


        }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //根据id 查询缴费记录
        $payRecord = PayRecord::find($id);
        $payRecord->status = $request->input('status');
        //更新人
        $payRecord->updater = $request->user()->real_name;
        $payRecord->update();
        return SUCCESS_RESPONSE_ARRAY("修改成功");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Support\HigherOrderWhenProxy
     */
    public function getQuery(Request $request): \Illuminate\Support\HigherOrderWhenProxy|\Illuminate\Database\Eloquent\Builder
    {
        $event_title = $request->input('event_title');
        //缴费人
        $pay_user = $request->input('pay_user');
        $organization = $request->input('organization');
        $phone = $request->input('phone');
        $status = $request->input('status');
        $pay_time = $request->input('pay_time');

        $query = PayRecord::query()
            ->when($event_title, function ($query) use ($event_title) {
                $query->where('event_title', 'like', "%{$event_title}%");
            })
            ->when($pay_user, function ($query) use ($pay_user) {
                $query->where('pay_user', 'like', "%{$pay_user}%");
            })
            ->when($organization, function ($query) use ($organization) {
                $query->where('organization', 'like', "%{$organization}%");
            })
            ->when($phone, function ($query) use ($phone) {
                $query->where('phone', 'like', "%{$phone}%");
            })
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->when($pay_time, function ($query) use ($pay_time) {
                if (is_array($pay_time) && count($pay_time) === 2) {
                    $start_date = $pay_time[0];
                    $end_date = $pay_time[1];
                    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));
                    $query->whereBetween('pay_time', [$start_date, $end_date]);
                }
            });
        return $query;
    }
}
