<?php

namespace App\Console\Commands;

use App\Models\Attendee;
use App\Models\AttendeeDropOff;
use App\Models\AttendeeHotel;
use App\Models\AttendeePickUp;
use App\Models\Event;
use App\Repositories\NoticeRepository;
use AppletUrl;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Sign;

class SmsSendEveryDay10Command extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sms-send-every-day10-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建每天10点定时短信发送任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 给需要接站但接站信息未填写全的参会人发送短信
        $this->sendNeedPickUpSms();

        // 给需要送站但送站信息未填写全的参会人发送短信
        $this->sendNeedDropOffSms();

        // 给需要住宿但住宿信息未填写全的参会人发送短信
        $this->sendNeedHotelSms();

        return 0;
    }

    // 给需要接站但接站信息未填写全的参会人发送短信
    function sendNeedPickUpSms()
    {
        // 计算updated_at日期和当前时间的日期差值，如果天数等于1或等于3或等于7
        $dateRanges = [
            Carbon::now()->subDay()->toDateString(),
            Carbon::now()->subDays(3)->toDateString(),
            Carbon::now()->subDays(7)->toDateString(),
        ];

        //  查询参会人接站表attendee_pick_ups，获取未填写接站信息的参会人信息
        $query = AttendeePickUp::join('attendees', 'attendees.id', '=', 'attendee_pick_ups.attendee_id')
            ->join('events', 'events.id', '=', 'attendees.event_id')
            ->select('attendees.phone', 'attendees.event_id', 'events.title as event_title')
            ->where('attendee_pick_ups.is_need_pick_up', 1)
            ->where('attendee_pick_ups.is_train', 1)
            ->where('attendees.status', Attendee::$status_ok)
            ->where('events.is_on', 1)
            // 查找未开始的会议
            ->where('events.start_time', '>', now());
        // 判断attendee_pick_ups的station_info，place，plan_arrive_time字段是否为空
        $query->where(function ($query) {
            $query->where('attendee_pick_ups.station_info', '=', '')
                ->orWhere('attendee_pick_ups.place', '=', '')
                ->orWhereNull('attendee_pick_ups.plan_arrive_time');
        });
        // 查找一天前或三天前或七天前保存的数据
        $query->where(function ($query) use ($dateRanges) {
            foreach ($dateRanges as $dateRange) {
                $query->orWhereRaw('DATE(attendee_pick_ups.updated_at) = ?', $dateRange);
            }
        });

        // 获取符合条件数据的event_id数组
        $commonQuery = $query->pluck('event_id')->toArray();
        if (!empty($commonQuery)) {
            // 使用array_unique在查询阶段就避免重复，而不是在后续处理中
            $uniqueEventIds = array_unique($commonQuery);
            foreach ($uniqueEventIds as $event_id) {
                $event = Event::find($event_id);
                $phones = $query->where('attendees.event_id', $event_id)->pluck('phone')->toArray();
                $smsContent = "您好，您报名的title，需要接站信息尚未填写，点击前往填写：url";
                $params = [
                    'title' => $event->title,
                    'url' => getAppletUrlLink(AppletUrl::EVENT_WAY_URL) . '?id=' . $event_id
                ];
                // 发送短信
                batchSendJianzhouSms(Sign::SEEE, $phones, replaceParams($smsContent, $params));
            }
        }

        // 测试定时任务消息
        NoticeRepository::insertSmsSendLog('上午10点定时任务消息1', ['18611134083'], date('Y-m-d H:i:s') . "给需要接站但接站信息未填写全的参会人发送短信，共有" . $query->count() . "条数据", "");
    }


    // 给需要送站但送站信息未填写全的参会人发送短信
    function sendNeedDropOffSms()
    {
        // 计算updated_at日期和当前时间的日期差值，如果天数等于1或等于3或等于7
        $dateRanges = [
            Carbon::now()->subDay()->toDateString(),
            Carbon::now()->subDays(3)->toDateString(),
            Carbon::now()->subDays(7)->toDateString(),
        ];

        //  查询参会人接站表attendee_pick_ups，获取未填写接站信息的参会人信息
        $query = AttendeeDropOff::join('attendees', 'attendees.id', '=', 'attendee_drop_offs.attendee_id')
            ->join('events', 'events.id', '=', 'attendees.event_id')
            ->select('attendees.phone', 'attendees.event_id', 'events.title as event_title')
            ->where('attendee_drop_offs.is_need_drop_off', 1)
            ->where('attendee_drop_offs.is_train', 1)
            ->where('attendees.status', Attendee::$status_ok)
            ->where('events.is_on', 1)
            // 查找未结束的会议
            ->where('events.end_time', '<', now());
        // 判断attendee_drop_offs的station_info，place，plan_arrive_time字段是否为空
        $query->where(function ($query) {
            $query->where('attendee_drop_offs.station_info', '=', '')
                ->orWhere('attendee_drop_offs.place', '=', '')
                ->orWhereNull('attendee_drop_offs.plan_go_time');
        });
        // 查找一天前或三天前或七天前保存的数据
        $query->where(function ($query) use ($dateRanges) {
            foreach ($dateRanges as $dateRange) {
                $query->orWhereRaw('DATE(attendee_drop_offs.updated_at) = ?', $dateRange);
            }
        });

        // 获取符合条件数据的event_id数组
        $commonQuery = $query->pluck('event_id')->toArray();
        if (!empty($commonQuery)) {
            // 使用array_unique在查询阶段就避免重复，而不是在后续处理中
            $uniqueEventIds = array_unique($commonQuery);
            foreach ($uniqueEventIds as $event_id) {
                $event = Event::find($event_id);
                $phones = $query->where('attendees.event_id', $event_id)->pluck('phone')->toArray();
                $smsContent = "您好，您报名的title，需要送站信息尚未填写，点击前往填写：url";
                $params = [
                    'title' => $event->title,
                    'url' => getAppletUrlLink(AppletUrl::EVENT_WAY_URL) . '?id=' . $event_id
                ];
                // 发送短信
                batchSendJianzhouSms(Sign::SEEE, $phones, replaceParams($smsContent, $params));
            }
        }

        // 测试定时任务消息
        NoticeRepository::insertSmsSendLog('上午10点定时任务消息2', ['18611134083'], date('Y-m-d H:i:s') . "给需要送站但送站信息未填写全的参会人发送短信，共有" . $query->count() . "条数据", "");
    }


    // 给需要住宿但住宿信息未填写全的参会人发送短信
    function sendNeedHotelSms()
    {
        // 计算updated_at日期和当前时间的日期差值，如果天数等于1或等于3或等于7
        $dateRanges = [
            Carbon::now()->subDay()->toDateString(),
            Carbon::now()->subDays(3)->toDateString(),
            Carbon::now()->subDays(7)->toDateString(),
        ];

        //  查询参会人接站表attendee_pick_ups，获取未填写接站信息的参会人信息
        $query = AttendeeHotel::join('attendees', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->join('events', 'events.id', '=', 'attendees.event_id')
            ->select('attendees.phone', 'attendees.event_id', 'events.title as event_title')
            ->where('attendee_hotels.is_need_hotel', 1)
            ->where('attendees.status', Attendee::$status_ok)
            ->where('events.is_on', 1)
            // 查找未结束的会议
            ->where('events.end_time', '>', now());
        // 判断attendee_hotels的station_info，place，plan_arrive_time字段是否为空
        $query->where(function ($query) {
            $query->where('attendee_hotels.type', '=', 1)
                ->where(function ($query) {
                $query->whereNull('attendee_hotels.check_in_date')
                    ->orWhereNull('attendee_hotels.check_in_days')
                    ->orWhereNull('attendee_hotels.booking_room_number')
                    ->orWhereNull('attendee_hotels.room_type');
            });
        })->orWhere(function ($query) {
            $query->where('attendee_hotels.type', '=', 2)
                ->where(function ($query) {
                    $query->whereNull('attendee_hotels.check_in_date')
                        ->orWhereNull('attendee_hotels.check_in_days')
                        ->orWhereNull('attendee_hotels.hotel_id');
                });
        });

        // 查找一天前或三天前或七天前保存的数据
        $query->where(function ($query) use ($dateRanges) {
            foreach ($dateRanges as $dateRange) {
                $query->orWhereRaw('DATE(attendee_hotels.updated_at) = ?', $dateRange);
            }
        });

        // 获取符合条件数据的event_id数组
        $commonQuery = $query->pluck('event_id')->toArray();
        if (!empty($commonQuery)) {
            // 使用array_unique在查询阶段就避免重复，而不是在后续处理中
            $uniqueEventIds = array_unique($commonQuery);
            foreach ($uniqueEventIds as $event_id) {
                $event = Event::find($event_id);
                $phones = $query->where('attendees.event_id', $event_id)->pluck('phone')->toArray();
                $smsContent = "您好，您报名的title，相关酒店住宿信息尚未填写，点击前往填写：url";
                $params = [
                    'title' => $event->title,
                    'url' => getAppletUrlLink(AppletUrl::EVENT_WAY_URL) . '?id=' . $event_id
                ];
                // 发送短信
                batchSendJianzhouSms(Sign::SEEE, $phones, replaceParams($smsContent, $params));
            }
        }

        // 测试定时任务消息
        NoticeRepository::insertSmsSendLog('上午10点定时任务消息3', ['18611134083'], date('Y-m-d H:i:s') . "给需要住宿但住宿信息未填写全的参会人发送短信，共有" . $query->count() . "条数据", "");
    }

}
