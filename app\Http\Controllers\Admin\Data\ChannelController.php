<?php

namespace App\Http\Controllers\Admin\Data;

use App\Http\Controllers\Controller;
use App\Models\Channel;
use App\Repositories\ChannelRepository;
use Illuminate\Http\Request;

class ChannelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, ChannelRepository $channelRepository)
    {
        return $channelRepository->listBuilder($request);
    }

    // 邀约渠道下拉列表
    public function dropList()
    {
        $list = Channel::get(['id', 'name', 'status']);
        return SUCCESS_RESPONSE_ARRAY($list);
    }

    //渠道下拉列表 分级部门
    public
    function dropChannelList(Request $request, ChannelRepository $channelRepository)
    {
        $data = $channelRepository->dropChannelList($request);

        return SUCCESS_RESPONSE_ARRAY($data);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 新增渠道
        $data = filterRequestData('channels');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $departments = $request->input('departments');
        if (count($departments) == 2) {
            $data['department_one_id'] = $departments[0];
            $data['department_two_id'] = $departments[1];
        } else {
            return FAIL_RESPONSE_ARRAY('部门不能为空');
        }

        $channel = Channel::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('channel'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // 更新渠道
        $channel = Channel::find($id);
        if (!$channel) {
            return FAIL_RESPONSE_ARRAY('渠道不存在');
        }

        $data = filterRequestData('channels');
        $data['updater'] = $request->user()->real_name;
        $departments = $request->input('departments');
        if (count($departments) == 2) {
            $data['department_one_id'] = $departments[0];
            $data['department_two_id'] = $departments[1];
        } else {
            return FAIL_RESPONSE_ARRAY('部门不能为空');
        }

        $channel->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY(compact('channel'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    // 改变渠道状态
    public function changeStatus(Request $request, string $id)
    {
        $channel = Channel::find($id);
        if (!$channel) {
            return FAIL_RESPONSE_ARRAY('渠道不存在');
        }
        $channel->update([
            'status' => $request->input('status'),
            'updater' => $request->user()->real_name,
        ]);
        return SUCCESS_RESPONSE_ARRAY(compact('channel'));
    }
}
