<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 *
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Kpi newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Kpi newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Kpi query()
 * @mixin \Eloquent
 */
class Kpi extends Model
{
    use SoftDeletes, HasFactory, PaginationTrait;

    protected $casts = [
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function event()
    {
        return $this->hasOne(Event::class,'id', 'event_id');
    }

    public function forum()
    {
        return $this->hasOne(Forum::class,'id', 'forum_id');
    }

    public function department()
    {
        return $this->hasOne(Department::class,'id', 'department_two_id');
    }
}
