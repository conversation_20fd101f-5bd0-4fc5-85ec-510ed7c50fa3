<?php

namespace App\Http\Controllers\Admin\Data;

use App\Http\Controllers\Controller;
use App\Models\AttendeeHotel;
use App\Models\Hotel;
use App\Models\HotelBooking;
use App\Repositories\UserRepository;
use Cassandra\Map;
use Illuminate\Http\Request;

class HotelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $event_id = $request->input('event_id');
        $hotel_name = $request->input('name');

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $builder = Hotel::join('events', 'events.id', '=', 'hotels.event_id')
            ->select('hotels.*', 'events.title as event_title', 'events.short_title as event_short_title')
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('event_id', $event_id))
            ->when($hotel_name, fn($query) => $query->where('name', 'like', '%' . $hotel_name . '%'));
        $cnt = $builder->count();
        $list = $builder->pagination()->orderBy('id', 'desc')->get();

        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 获取当前会议下的酒店信息
    public function getHotelsByEventId(string $event_id)
    {
        // 当前会议下的酒店信息
        $hotels = Hotel::select('id', 'name', 'address', 'longitude', 'latitude', 'remark')
            ->where('event_id', $event_id)
            ->get();
        return SUCCESS_RESPONSE_ARRAY($hotels);
    }
    public function getHotelsByEventIds(Request $request)
    {
        // 会议ID
        $event_id = $request->input('event_id');
        if (!$event_id) {
            return SUCCESS_RESPONSE_ARRAY([]);
        }
        // 当前会议下的酒店信息
        $hotels = Hotel::select('id', 'name', 'address', 'longitude', 'latitude', 'remark')
            ->whereIn('event_id', $event_id)
            ->get();
        return SUCCESS_RESPONSE_ARRAY($hotels);
    }

    // 获取当前会议下的酒店信息
    public function getAllowSelectHotels(string $event_id)
    {
        // 当前会议下的酒店信息
        $hotels = Hotel::select('id', 'name', 'address', 'longitude', 'latitude')
            ->where('event_id', $event_id)
            ->where('is_allow_select', 1)
            ->get();
        return SUCCESS_RESPONSE_ARRAY($hotels);
    }

    // 报名页获取参会人预定酒店信息
    public function getEnrollHotels(string $event_id)
    {
        // 当前会议下的酒店信息
        $hotels = Hotel::select('id', 'name', 'address', 'longitude', 'latitude')
            ->where('event_id', $event_id)
            ->where('is_enroll_show', 1)
            ->get()->each(function ($hotel){
                $data = [];
                $booking_dates = HotelBooking::query()->where('hotel_id', $hotel->id)->pluck('booking_date')->toArray();
                foreach ($booking_dates as &$booking_date){
                    $booking_date = date('Y-m-d', strtotime($booking_date));
                    $roomsInfo = $this->getHotelRoomTypeNumber($booking_date, $hotel->id);

                    $booking = new \stdClass();
                    $booking->booking_date = $booking_date;
                    $booking->rooms_info = $roomsInfo;
                    $data[] = $booking;
                }
                $hotel->booking_dates = $data;
            });
        return SUCCESS_RESPONSE_ARRAY($hotels);
    }

    // 获取当前酒店的可预订日期
    public function getHotelBookingDates(string $hotel_id)
    {
        $hotel = Hotel::find($hotel_id);
        if (!$hotel) {
            return FAIL_RESPONSE_ARRAY('酒店不存在');
        }
        $data = [];
        $booking_dates = HotelBooking::query()->where('hotel_id', $hotel_id)->pluck('booking_date')->toArray();
        foreach ($booking_dates as &$booking_date){
            $booking_date = date('Y-m-d', strtotime($booking_date));
            $roomsInfo = $this->getHotelRoomTypeNumber($booking_date, $hotel_id);

            $booking = new \stdClass();
            $booking->booking_date = $booking_date;
            $booking->rooms_info = $roomsInfo;
            $data[] = $booking;
        }

        return SUCCESS_RESPONSE_ARRAY($data);
    }

    // 获取当前酒店的可预订房间类型的数量和剩余数量
    public function getHotelRoomTypeNumber(string $booking_date, string $hotel_id)
    {
        $room_types = HotelBooking::query()->selectRaw('sum(standard_room_number) as standard_room_number,sum(large_bed_room_number) as large_bed_room_number,
        max(standard_room_price) as standard_room_price, max(large_bed_room_price) as large_bed_room_price')
            ->where('hotel_id', $hotel_id)
            ->where('booking_date', '=', $booking_date)
            ->first();

        $data = [];

        if($room_types){
            $room = new \stdClass();
            $room->type = 1;
            $room->type_name = Hotel::convertRoomType(1);
            $room->number = $room_types->standard_room_number ?? 0;
            $room->room_price = $room_types->standard_room_price ?? 0;
            $booking = AttendeeHotel::query()->selectRaw('sum(booking_room_number) as booking_room_number')
                ->where('is_need_hotel', 1)
                ->where('hotel_id', $hotel_id)
                ->where('check_in_date', '=', $booking_date)
                ->where('room_type', '=', 1)
                ->first();
            if($booking){
                $room->surplus_number = $room_types->standard_room_number - $booking->booking_room_number;
            } else {
                $room->surplus_number = $room_types->standard_room_number;
            }
            $data[] = $room;

            $room = new \stdClass();
            $room->type = 2;
            $room->type_name = Hotel::convertRoomType(2);
            $room->number = $room_types->large_bed_room_number ?? 0;
            $room->room_price = $room_types->large_bed_room_price ?? 0;
            $booking = AttendeeHotel::query()->selectRaw('sum(booking_room_number) as booking_room_number')
                ->where('is_need_hotel', 1)
                ->where('hotel_id', $hotel_id)
                ->where('check_in_date', '=', $booking_date)
                ->where('room_type', '=', 2)
                ->first();
            if($booking){
                $room->surplus_number = $room_types->large_bed_room_number - $booking->booking_room_number;
            } else {
                $room->surplus_number = $room_types->large_bed_room_number;
            }
            $data[] = $room;
        }

        return $data;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 新增酒店
        $data = filterRequestData('hotels');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $hotel = Hotel::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('hotel'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // 修改酒店
        $hotel = Hotel::find($id);
        if (!$hotel) {
            return FAIL_RESPONSE_ARRAY('酒店不存在');
        }
        $data = filterRequestData('hotels');
        $data['updater'] = $request->user()->real_name;
        $hotel->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY(compact('hotel'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
