<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;


/**
 *
 *
 * @property int $id
 * @property string $title 会议标题
 * @property string|null $start_time 会议开始时间
 * @property string|null $end_time 会议结束时间
 * @property string $address 会议地点
 * @property string $longitude 经度
 * @property string $latitude 纬度
 * @property string $tags 会议标签
 * @property string $price 会议价格
 * @property string $cover 会议封面
 * @property string|null $description 会议描述
 * @property string|null $structure 组织结构
 * @property string|null $guide 大会导览
 * @property string|null $schedule 议程安排
 * @property string|null $guest 嘉宾介绍
 * @property string $succeeded_tip 报名成功后的提示文案
 * @property int $is_on 是否上架，1·上架，2·下架
 * @property int $is_audit_organization 邀约单位报名审核，1·审核，2·不审核
 * @property int $state 会议状态,1·未开始，2·进行中，3·已结束
 * @property string $questionnaire 会后问卷链接地址
 * @property string|null $creator 添加人
 * @property string|null $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Forum> $forums
 * @property-read int|null $forums_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Hotel> $hotels
 * @property-read int|null $hotels_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PaidContent> $paidContents
 * @property-read int|null $paid_contents_count
 * @method static \Illuminate\Database\Eloquent\Builder|Event newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Event newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Event onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Event pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|Event query()
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereCover($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereGuest($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereGuide($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereIsOn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereQuestionnaire($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereSchedule($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereStructure($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereSucceededTip($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event whereUpdater($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Event withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Event withoutTrashed()
 * @mixin \Eloquent
 */
class Event extends Model
{
    use SoftDeletes, HasFactory, PaginationTrait;

    protected $casts = [
        'start_time' => 'datetime:Y-m-d H:i:s',
        'end_time' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    protected $guarded = [];

    /**
     * 获取事件状态
     *
     * @return string
     */
    public function getStatusAttribute()
    {
        $now = Carbon::now();

        if ($now->lt($this->start_time)) {
            return 1; // 未开始
        } elseif ($now->gte($this->start_time) && $now->lt($this->end_time)) {
            return 2; // 进行中
        } else {
            return 3; // 已结束
        }
    }

    // 定义一个访问器，处理 cover 字段的前缀添加逻辑
    public function getCoverAttribute($value)
    {
        return config('filesystems.disks.sftp.domain') . $value;
    }

    public function setCoverAttribute($value)
    {
        // 假设 config('filesystems.disks.sftp.domain') 返回的是前缀
        $prefix = config('filesystems.disks.sftp.domain');

        // 检查 $value 是否以前缀开头
        if (strpos($value, $prefix) === 0) {
            // 如果是，则去掉前缀
            $value = substr($value, strlen($prefix));
        }

        // 设置 cover 属性
        $this->attributes['cover'] = $value;
    }

    //一个会议可以有多个论坛
    public function forums(): HasMany
    {
        return $this->hasMany(Forum::class, 'event_id')->orderBy('start_time', 'asc');
    }

    //一个会议可以有多个付费内容
    public function paidContents(): HasMany
    {
        return $this->hasMany(PaidContent::class, 'event_id');
    }

    // 一个会议有多个酒店
    public function hotels(): HasMany
    {
        return $this->hasMany(Hotel::class, 'event_id');
    }

    public function inviteOrganizations(): HasMany
    {
        return $this->hasMany(InviteOrganization::class, 'event_id');
    }
}
