<?php

namespace App\Http\Controllers\Admin\Event;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\Registrant;
use App\Repositories\RegistrantAuditExport;
use App\Repositories\RegistrantRepository;
use App\Repositories\RegistrantExport;
use AuditStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\Exception;
use function Laravel\Prompts\error;

class RegistrantController extends Controller
{
    /**
     * Display a listing of the resource.
     * //查询所有结果，获取所有的number的和和attendees_count的和
     * $collection = $query->get();
     * $total_number = $collection->sum('attendees');
     */
    public function index(Request $request, RegistrantRepository $registrantRepository)
    {
        //设置request的audit_status为1
        $request->merge(['audit_status' => [AuditStatus::PASS]]);
        $query = $registrantRepository->auditListBuilder($request,1);

        // 获取所有的报名人记录用于统计 attendees 的总数量
        $allRegistrants = $query->get();
        // 统计 total_number（所有符合条件的attendees的总数）
        $total_number = $allRegistrants->reduce(function($carry, $registrant) {
            // 获取当前报名人下的所有参会人
            $attendeesCount = Attendee::where('registrant_id', $registrant->id)
                ->where('batch', $registrant->batch)
                ->count(); // 只统计数量
            return $carry + $attendeesCount;
        }, 0);

        $list = $query->pagination()->get();
        // 处理查询结果
        $list = $list->map(function($registrant) {
            // 获取每个报名人下对应批次的参会人
            $attendees = Attendee::where('registrant_id', $registrant->id)
                ->where('batch', $registrant->batch)
                ->with('channel:id,name,department_two_id',
                    'user:id,real_name',
                    'channel.department:id,name,parent_id,code',
                    'channel.department.parent:id,name')
                ->get();

            // 追加参会人信息到报名人对象中
            $registrant->setRelation('attendees', $attendees);
            return $registrant;
        });
        $cnt = $query->paginate()->total(); // Get the total count of paginated items
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt','total_number'));
    }

    //非邀约单位审核列表
    public function auditList(Request $request, RegistrantRepository $registrantRepository)
    {
//        $request->merge(['is_audit_organization' => 2]);
        //设置request的audit_status为1和2
//        $request->merge(['audit_status' => [AuditStatus::PASS,AuditStatus::WAIT,AuditStatus::REJECT]]);
        $query = $registrantRepository->auditListBuilder($request, 2);
        $list = $query->pagination()->get();
        // 处理查询结果
        $list = $list->map(function($registrant) {
            // 获取每个报名人下对应批次的参会人
            $attendees = Attendee::where('registrant_id', $registrant->id)
                ->where('batch', $registrant->batch)
                ->with('channel:id,name,department_two_id',
                    'user:id,real_name',
                    'channel.department:id,name,parent_id,code',
                    'channel.department.parent:id,name')
                ->get();

            // 追加参会人信息到报名人对象中
            $registrant->setRelation('attendees', $attendees);
            return $registrant;
        });

        $cnt = $query->paginate()->total(); // Get the total count of paginated items
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    //审核报名人
    public function auditRegistrant(Request $request, RegistrantRepository $registrantRepository)
    {
        $update_count = $registrantRepository->auditRegistrant($request);
        return $update_count > 0 ? SUCCESS_RESPONSE_ARRAY() : FAIL_RESPONSE_ARRAY("更新失败");
    }

    //导出
    public function export(Request $request, RegistrantRepository $registrantRepository)
    {
        //设置request的audit_status为1
        $request->merge(['audit_status' => [AuditStatus::PASS]]);
        $query = $registrantRepository->registrantExportBuilder($request,1);
        $list = $query->get();

        // 处理查询结果
        $results = $list->map(function($registrant) {
            // 获取每个报名人下对应批次的参会人
            $attendees = Attendee::where('registrant_id', $registrant->id)
                ->where('batch', $registrant->batch)
                ->with('forums', 'channel:id,name,department_two_id', 'channel.department:id,name,parent_id,code', 'channel.department.parent:id,name')
                ->get()
                ->map(function ($attendee) {
                    $attendee->forums_names = $attendee->forums->pluck('name')->toArray();
                    return $attendee;
                });

            // 追加参会人信息到报名人对象中
            $registrant->attendees = $attendees;
            return $registrant;
        });
        // 创建导出实例
        $export = new RegistrantExport($results);
        // 导出数据到 Excel 文件
        return Excel::download($export, 'search_results.xlsx');
    }

    //审核列表导出
    public function auditListExport(Request $request, RegistrantRepository $registrantRepository)
    {
        //设置request的audit_status为1和2
        $request->merge(['audit_status' => [AuditStatus::PASS,AuditStatus::WAIT,AuditStatus::REJECT]]);
        $query = $registrantRepository->registrantExportBuilder($request,2);
        $list = $query->get();
        // 处理查询结果
        $results = $list->map(function($registrant) {
            // 获取每个报名人下对应批次的参会人
            $attendees = Attendee::where('registrant_id', $registrant->id)
                ->where('batch', $registrant->batch)
                ->with('forums', 'channel:id,name,department_two_id', 'channel.department:id,name,parent_id,code', 'channel.department.parent:id,name')
                ->get()
                ->map(function ($attendee) {
                    $attendee->forums_names = $attendee->forums->pluck('name')->toArray();
                    return $attendee;
                });

            // 追加参会人信息到报名人对象中
            $registrant->attendees = $attendees;
            return $registrant;
        });
        // 创建导出实例
        $export = new RegistrantAuditExport($results);
        // 导出数据到 Excel 文件
        return Excel::download($export, 'registrant_results.xlsx');
    }

    //对接人下拉列表
    public
    function dropUserList(Request $request, RegistrantRepository $registrantRepository)
    {
        $data = $registrantRepository->dropUserList($request);

        return SUCCESS_RESPONSE_ARRAY($data);
    }

    //分配对接人assignUser，传入ids和对接人user_id
    public
    function assignUser(Request $request, RegistrantRepository $registrantRepository)
    {
        $update_count = $registrantRepository->assignUser($request);
        return $update_count > 0 ? SUCCESS_RESPONSE_ARRAY() : FAIL_RESPONSE_ARRAY("更新失败");
    }

    /**
     * Show the form for creating a new resource.
     */
    public
    function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public
    function store(Request $request, RegistrantRepository $registrantRepository)
    {
        //新增报名人
        return $registrantRepository->store($request);
//        return $insert_count ? SUCCESS_RESPONSE_ARRAY() : FAIL_RESPONSE_ARRAY("报名失败");
    }

    //enroll_info
    /*public
    function enrollInfo(Request $request, RegistrantRepository $registrantRepository)
    {
        $data = $registrantRepository->enrollInfo($request);
        return SUCCESS_RESPONSE_ARRAY($data);
    }*/

    /**
     * Display the specified resource.
     */
    public
    function show(string $id,string $batch)
    {
        $data = Registrant::query()
            ->with([
                'attendees' => function ($query) use ($batch) {
                    $query->when($batch, function ($query, $batch) {
                        return $query->where('batch', $batch);
                    })->with('forums')->get()->map(function ($attendee) {
                        $attendee->forums_names = $attendee->forums->pluck('name')->toArray();
                        return $attendee;
                    });
                },
                'attendees.channel:id,name,department_two_id',
                'attendees.channel.department:id,name,parent_id,code',
                'attendees.channel.department.parent:id,name',
                'attendees.user:id,real_name'
            ])->find($id);
        return SUCCESS_RESPONSE_ARRAY($data);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public
    function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public
    function update(Request $request, string $id)
    {
        //更新报名人
        $registrant = Registrant::find($id);
        $data = filterRequestData('registrants');
        $data['updater'] = $request->user()->real_name;
        $registrant->fill($data)->save();
        //查询该报名人下的参会人
        $attendees = Attendee::where('registrant_id', $id)->get();
        /**
         * 在参会人身份类别，单位类型为空时，报名人的身份类别，和单位类型填写以后，也同时给参会人副相同值；
         * 在参会人身份类别有数据时，修改报名人身份类别不要修改参会人的身份类别；
         * 在参会人单位类型有数据时，修改报名人单位类型同步修改 参会人的单位类型；
         *
         * 伪代码：如果参会人的身份类别为0时，将报名人身份类别给这个参会人；将参会人的单位类型修改为报名人的单位类型
         */
        $attendees->each(function ($attendee) use ($registrant) {
            $needsUpdate = false;
            if ($attendee->organization_type != $registrant->organization_type) {
                $attendee->organization_type = $registrant->organization_type;
                $needsUpdate = true;
            }
            if ($attendee->identity == 0) {
                $attendee->identity = $registrant->identity;
                $needsUpdate = true;
            }
            if ($needsUpdate) {
                $attendee->update();
            }
        });
        return SUCCESS_RESPONSE_ARRAY($registrant);
    }

    //更新参会人信息
    public
    function updateAttendee(Request $request, $id)
    {
        $attendee = Attendee::find($id);
        $phone = $request->input('phone');
        //如果输入的phone和$attendee的phone不相同，则校验手机号是否重复
        if ($phone != $attendee->phone) {
            $model = Attendee::query()->where('event_id', $attendee->event_id)->where('status', '!=', 2)->where('phone', $phone)->first();
            if ($model) {
                throw new Exception('参会人手机号：' . $phone . '，已成功报名，报名人：' . $model->name . '，请勿重复报名', REPEAT_DATA_CODE);
            }
        }
        $data = filterRequestData('attendees');
        $data['updater'] = $request->user()->real_name;
        $attendee->fill($data)->save();
        return SUCCESS_RESPONSE_ARRAY($attendee);
    }

    /**
     * Remove the specified resource from storage.
     */
    public
    function destroy(string $id)
    {
        //
    }

    //根据会议和当前member_id查询报名人信息
    public
    function getRegistrantInfo(Request $request, string $event_id, RegistrantRepository $registrantRepository)
    {
        $data = $registrantRepository->getRegistrantInfo($request,$event_id);
        return SUCCESS_RESPONSE_ARRAY($data);
    }
}
