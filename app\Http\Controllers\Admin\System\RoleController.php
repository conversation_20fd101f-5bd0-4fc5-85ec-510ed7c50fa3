<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use App\Models\Role;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    //
    /**
     *  角色下拉列表
     * @return array
     */
    public function dropRoles(): array
    {
        //
        return SUCCESS_RESPONSE_ARRAY(Role::select(['id', 'name'])->orderBy('id', 'desc')->get());
    }

    /**
     *  角色列表
     * @param Request $request
     * @return array
     */
    public function list(Request $request): array
    {
        $page = request('page', 1);
        $page_size = request('page_size', 10);
        $skip = ($page - 1) * $page_size;
        $role_id = $request->input('role_id');
        $builder = Role::when($role_id, fn($query) => $query->where('id', $role_id));
        $cnt = $builder->count();
        $list = $builder->take($page_size)->skip($skip)->orderBy('id', 'desc')->with(['users:id,real_name'])->get()->each(function ($item) {
            $item->user_names = $item->users->pluck('real_name')->join(',');
        });
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    public function store(Request $request)
    {
        $rst = \Spatie\Permission\Models\Role::create([
            'name' => $request->input('name'),
            'remark' => $request->input('remark'),
            'creator' => $request->user()->real_name,
            'updater' => $request->user()->real_name,
        ]);
        return SUCCESS_RESPONSE_ARRAY($rst);
    }

    public function update(Request $request, $id)
    {
        $rst = \Spatie\Permission\Models\Role::find($id)->fill([
            'name' => $request->input('name'),
            'remark' => $request->input('remark'),
            'updater' => $request->user()->real_name,
        ])->save();
        return SUCCESS_RESPONSE_ARRAY($rst);
    }

    public function bindPermissions(Request $request, $id)
    {
//        $selectField = "SUBSTRING_INDEX(name, '.', 1)                              AS level1,
//       SUBSTRING_INDEX(SUBSTRING_INDEX(name, '.', 2), '.', -1)    AS level2,
//       SUBSTRING(name, LENGTH(SUBSTRING_INDEX(name, '.', 2)) + 2) AS level3,
//       name,display_name,id";

        $role = \Spatie\Permission\Models\Role::find($id);
        $selectField = "SUBSTRING_INDEX(name, '.', 1)                              AS level1,
       SUBSTRING_INDEX(SUBSTRING_INDEX(name, '.', 2), '.', -1)    AS level2,
       name,display_name,id";
        $permissions = Permission::selectRaw($selectField)->get();
        $levels1 = $permissions->pluck('level1')->unique();
        $result = [];
        foreach ($levels1 as $level1Item) {

            $level2 = $permissions->where('level1', $level1Item)->pluck('level2')->unique()
                ->map(fn($item) => ['value' => $item, 'label' => Role::LEVEL2[$item] ?? $item])->values()->toArray();


            foreach ($level2 as $index => $level2Item) {
                $level3 = $permissions->where('level2', $level2Item['value'])->where('level1', $level1Item)
                    ->map(fn($item) => ['value' => $item['id'], 'label' => $item['display_name']])->values()->toArray();
                $level2[$index]['children'] = $level3;
            }

            $result[] = ['label' => Role::LEVEL1[$level1Item] ?? $level1Item, 'value' => $level1Item, 'children' => $level2];

        }
        $checked = $role->permissions->pluck('id');
        return SUCCESS_RESPONSE_ARRAY(compact('checked', 'result'));
    }

    public function bindPermissionsAction(Request $request, $id)
    {
        $role = \Spatie\Permission\Models\Role::find($id);
        $permissionIds = collect($request->input('nodes'))->pluck('value')->toArray();
        $role->permissions()->sync($permissionIds);
        return SUCCESS_RESPONSE_ARRAY();

    }

    public function bindMenus(Request $request, $id)
    {
        $role = Role::find($id);
        $menus = Menu::selectRaw("id as value,menu_name as label,level,parent_id")->orderBy('sort')->orderBy('id')->get();
        $result = $menus->where('level', 1)->each(function ($item) use ($menus) {
            $item->children = $menus->where('parent_id', $item->value)->values();
        })->values();
        $checked = $role->menus->where('parent_id', '!=', 0)->pluck('id');
        // 如果拥有首页一级菜单（id 为 38），则添加
        if($role->menus->pluck('id')->contains(38)){
            $checked = $checked->push(38);
        }

        return SUCCESS_RESPONSE_ARRAY(compact('result', 'checked'));

    }

    public function bindMenusAction(Request $request, $id)
    {
        $role = Role::find($id);
        $menuIds = collect($request->input('nodes'))->pluck('value')->toArray();
        $parentIds = Menu::whereIn('id', $menuIds)->pluck('parent_id')->unique()->toArray();
        $role->menus()->sync([...$menuIds, ...$parentIds]);
        return SUCCESS_RESPONSE_ARRAY();
    }


}
