<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_speeches', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('forum_name')->default(0)->comment('论坛名称');
            $table->string('title')->default('')->comment('演讲主题');
            $table->string('speaker', 20)->default('')->comment('演讲人');
            $table->string('phone', 20)->default('')->comment('手机号');
            $table->tinyInteger('gender')->default(0)->comment('性别 0未知 1男 2女');
            $table->string('organization')->default('')->comment('单位');
            $table->string('position')->default('')->comment('职称职务');
            $table->string('file_url')->default('')->comment('演讲稿文件地址');
            $table->tinyInteger('status')->default(1)->comment('状态 1未审核 2已通过 3未通过');
            $table->string('audit_user', 20)->default('')->comment('审核人');
            $table->timestamp('audit_at')->nullable()->comment('审核时间');
            $table->string('audit_remark')->default('')->comment('审核备注');
            $table->string('message')->default('')->comment('留言');

            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `website_speeches` comment '演讲申请表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('speeches');
    }
};
