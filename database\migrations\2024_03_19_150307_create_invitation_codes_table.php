<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_invitation_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('邀请码');
            $table->tinyInteger('status')->default(2)->comment('状态 1:已使用 2:未使用');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `website_invitation_codes` comment '邀请码表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('website_invitation_codes');
    }
};
