<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 16:53
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\AttendeeInterview;
use Illuminate\Http\Request;

class AttendeeInterviewsRepository
{
    public function listBuilder(Request $request)
    {
        // 会议ID
        $event_id = $request->input('event_id');
        // 参会人单位
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $position = $request->input('position');
        // 参会人姓名
        $name = $request->input('name');
        $phone = $request->input('phone');
        // 采访类型
        $type = $request->input('type');
        // 采访人
        $interviewer = $request->input('interviewer');
        // 对接人
        $user_id_arr = $request->input('user_id_arr');
        $user_ids = AttendeeRepository::getFilterUserIds($user_id_arr);
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = RegistrantRepository::getFilterChannelIds($channel_id_arr);

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $query = AttendeeInterview::join('attendees', 'attendee_interviews.attendee_id', '=', 'attendees.id')
            ->join('events', 'attendees.event_id', '=', 'events.id')
            ->leftjoin('users', 'attendees.user_id', '=', 'users.id')
            ->select('attendee_interviews.attendee_id', 'attendee_interviews.type', 'attendee_interviews.time', 'attendee_interviews.theme', 'attendee_interviews.location', 'attendee_interviews.interviewer', 'attendee_interviews.updater', 'attendee_interviews.updated_at',
                'attendees.name as attendee_name', 'attendees.phone as attendee_phone', 'attendees.position as attendee_position', 'attendees.organization as attendee_organization', 'attendees.organization_code as attendee_organization_code', 'attendees.organization_type as attendee_organization_type', 'attendees.identity as attendee_identity',
                'events.short_title as event_short_title',
                'users.real_name as user_real_name')
            ->where('attendees.status', Attendee::$status_ok)
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('attendees.event_id', $event_id))
            ->when($organization, fn($query) => $query->where('attendees.organization', 'like', "%$organization%"))
            ->when($organization_code, fn($query) => $query->where('attendees.organization_code', 'like', "%$organization_code%"))
            ->when($position, fn($query) => $query->where('attendees.position', 'like', "%$position%"))
            ->when($name, fn($query) => $query->where('attendees.name', 'like', "%$name%"))
            ->when($phone, fn($query) => $query->where('attendees.phone', "$phone"))
            ->when($user_id_arr, fn($query) => $query->whereIn('attendees.user_id', $user_ids))
            ->when($channel_id_arr, fn($query) => $query->whereIn('attendees.channel_id', $channel_id))
            ->when($type, fn($query) => $query->where('attendee_interviews.type', $type))
            ->when($interviewer, fn($query) => $query->where('attendee_interviews.interviewer', 'like', "%$interviewer%"));
        $query = RegistrantRepository::getAuthFilterQuery($request, $query);
        return $query;
    }


}
