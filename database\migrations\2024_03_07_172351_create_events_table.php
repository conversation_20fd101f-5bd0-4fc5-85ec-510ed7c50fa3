<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('title')->default('')->comment('会议标题');
            $table->string('short_title')->default('')->comment('会议简称');
            $table->timestamp('start_time')->nullable()->comment('会议开始时间');
            $table->timestamp('end_time')->nullable()->comment('会议结束时间');
            $table->string('address')->nullable()->default('')->comment('会议地点');
            $table->string('longitude')->nullable()->default('')->comment('经度');
            $table->string('latitude')->nullable()->default('')->comment('纬度');
            $table->string('tags')->nullable()->default('')->comment('会议标签');
            $table->decimal('price', 10, 2)->nullable(true)->comment('会议价格');
            $table->string('cover')->default('')->comment('会议封面');
            $table->longText('description')->nullable()->comment('会议描述');
            $table->longText('structure')->nullable()->comment('组织结构');
            $table->longText('guide')->nullable()->comment('大会导览');
            $table->longText('schedule')->nullable()->comment('议程安排');
            $table->longText('guest')->nullable()->comment('嘉宾介绍');
            $table->string('succeeded_tip')->nullable()->default('')->comment('报名成功后的提示文案');
            //会务手册链接
            $table->string('manual_url')->nullable()->default('')->comment('会务手册链接');
            //会议修改截止时间
            $table->timestamp('modify_deadline')->nullable()->comment('会议修改截止时间');
            //会议活动类型 1.SEEE考试院 2.IEIC 国际创新大会 3.CITD大会 4.校长培训计划
            $table->tinyInteger('activity_type')->nullable()->default(0)->comment('会议活动类型，1·SEEE考试院 2·IEIC 国际创新大会 3·CITD大会 4·校长培训计划');
            //邀约单位报名是否需要审核
            $table->tinyInteger('is_audit_organization')->nullable()->default(2)->comment('邀约单位报名是否需要审核，1·需要，2·不需要');
            $table->tinyInteger('type')->nullable()->default(1)->comment('会议类型，1·开放型，2·限制型');
            $table->tinyInteger('is_on')->nullable()->default(2)->comment('是否上架，1·上架，2·下架');
            $table->tinyInteger('status')->nullable()->default(0)->comment('会议状态,1·未开始，2·进行中，3·已结束');
            $table->string('questionnaire')->nullable(false)->default('')->comment('会后问卷链接地址');
            $table->string('creator', 20)->nullable()->comment('添加人');
            $table->string('updater', 20)->nullable()->comment('最后更新人');
            $table->softDeletes();
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `events` comment '会议表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conferences');
    }
};
