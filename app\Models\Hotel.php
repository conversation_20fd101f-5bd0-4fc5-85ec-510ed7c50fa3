<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\HotelBooking> $bookingInfos
 * @property-read int|null $booking_infos_count
 * @property-read \App\Models\Event|null $event
 * @method static \Illuminate\Database\Eloquent\Builder|Hotel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Hotel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Hotel query()
 * @mixin \Eloquent
 */
class Hotel extends Model
{
    use HasFactory, PaginationTrait;

    protected $casts = [
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    protected $guarded = [];

    // 一个酒店对应一个会议
    public function event()
    {
        return $this->hasOne(Event::class, 'id', 'event_id');
    }

    // 一个酒店下有多个酒店预定信息
    public function bookingInfos()
    {
        return $this->hasMany(HotelBooking::class);
    }

    public static function convertRoomType($type)
    {
        switch ($type) {
            case 1:
                return '标间';
            case 2:
                return '大床房';
            default:
                return '';
        }
    }
}
