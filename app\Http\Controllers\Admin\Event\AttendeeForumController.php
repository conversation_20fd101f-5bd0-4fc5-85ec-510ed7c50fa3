<?php

namespace App\Http\Controllers\Admin\Event;

use App\Http\Controllers\Controller;
use App\Models\AttendeeForums;
use App\Models\Forum;
use App\Repositories\AttendeeForumExport;
use App\Repositories\AttendeeRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\Exception;

class AttendeeForumController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, AttendeeRepository $attendeeRepository)
    {
        //查询参会人报名了的论坛
        $query = $attendeeRepository->attendeeForums($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        // 将论坛名字作为参会人员的一个属性，并以数组的形式存储
        /*$list->each(function ($attendee) {
            $forum_names = $attendee->forums->pluck('name')->toArray();
            $attendee->forum_names = $forum_names;
        });*/
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    //导出
    public function export(Request $request, AttendeeRepository $attendeeRepository)
    {
        try {
            $query = $attendeeRepository->attendeeForums($request);
            $list = $query->orderBy('id', 'desc')->get();// 创建导出实例
            $export = new AttendeeForumExport($list);// 导出数据到 Excel 文件
            return Excel::download($export, 'search_results.xlsx');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    //审核，传入ids和审核状态，审核备注
    public function auditForums(Request $request, AttendeeRepository $attendeeRepository)
    {
        $update_count = $attendeeRepository->auditForums($request);
        if ($update_count == -10) {
            return FAIL_RESPONSE_ARRAY("超过论坛人数上限，请查看论坛列表已成功报名人数");
        }
        return $update_count>0?SUCCESS_RESPONSE_ARRAY():FAIL_RESPONSE_ARRAY("更新失败");
    }

    public function inviteForums(Request $request, AttendeeRepository $attendeeRepository)
    {
        $update_count = $attendeeRepository->inviteForums($request);
        return $update_count>0?SUCCESS_RESPONSE_ARRAY():FAIL_RESPONSE_ARRAY("邀约失败");
    }

    //传入forumIds
    public function forumsStatistics(Request $request, AttendeeRepository $attendeeRepository)
    {
        //统一已通过审核的论坛，格式为论坛名称和已通过的条数
        $forums_statistics = $attendeeRepository->forumsStatistics($request);
        return SUCCESS_RESPONSE_ARRAY($forums_statistics);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
        $attendeeForum = AttendeeForums::find($id);
        $intention = $request->input('intention');

        if ($intention == 1) {
            // 获取论坛的最大参会人数
            $maxAttendees = Forum::where('id', $attendeeForum['forum_id'])->value('number');

            // 计算当前论坛的参会人数
//            $currentAttendeesCount = AttendeeForums::where('forum_id',  $attendeeForum['forum_id'])->whereIn('audit_status', [1,2])->count();
            $currentAttendeesCount = AttendeeForums::where('forum_id', $attendeeForum['forum_id'])
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('type', 2)
                            ->where('audit_status', 1);
                    })->orWhere(function ($query) {
                        $query->where('type', 1)
                            ->where('intention', 1);
                    });
                })
                ->count();

            if ($currentAttendeesCount >= $maxAttendees){
                return ERROR_RESPONSE_ARRAY('当前论坛已超过人数限制，请联系主办方工作人员', NUMBER_LIMIT_CODE);
            }
        }

        $attendeeForum->intention = $intention;
//        if ($intention == 1) {
//            $attendeeForum ->audit_status = 1;
//        }
        $attendeeForum ->update();
        return SUCCESS_RESPONSE_ARRAY('设置成功');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
