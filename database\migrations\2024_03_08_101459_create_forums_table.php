<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forums', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->nullable(false)->default(0)->comment('会议id');
            $table->string('name')->default('')->comment('论坛名称');
            $table->string('cover')->default('')->comment('论坛封面');
            $table->timestamp('start_time')->nullable()->comment('论坛开始时间');
            $table->timestamp('end_time')->nullable()->comment('论坛结束时间');
            $table->string('address')->default('')->comment('论坛地点');
            $table->tinyInteger('is_enroll')->default(2)->comment('是否可报名，1:可报名，2:不可报名');
            $table->tinyInteger('is_audit')->default(1)->comment('是否需要审核，1:需要，2:不需要');
            $table->tinyInteger('is_on')->default(2)->comment('是否上架，1:上架，2:下架');
            $table->longText('description')->nullable()->comment('论坛描述');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `forums` comment '论坛表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forums');
    }
};
