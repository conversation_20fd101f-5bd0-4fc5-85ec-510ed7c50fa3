<?php

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\AttendeeDine;
use App\Models\AttendeeDropOff;
use App\Models\AttendeeForums;
use App\Models\AttendeeHotel;
use App\Models\AttendeeInterview;
use App\Models\AttendeePickUp;
use App\Models\Dine;
use App\Models\Event;
use App\Models\Hotel;
use App\Models\Message;
use App\Models\Order;
use App\Models\Registrant;
use Carbon\Carbon;
use Illuminate\Http\Request;
use phpDocumentor\Reflection\Types\String_;

class EventWayRepository
{

    // 获取用户参加的所有会议
    public function getMemberCheckInEvents(Request $request)
    {
        $member_id = $request->user()->id;

        // 获取当前用户作为参会人参加的所有会议
        $events1 = Event::join('attendees', 'events.id', '=', 'attendees.event_id')
            ->select('events.id', 'events.title' , 'events.start_time' , 'events.end_time', 'events.address', 'events.longitude', 'events.latitude', 'events.status', 'events.manual_url', 'events.questionnaire', 'events.modify_deadline')
            ->where('attendees.member_id', $member_id)
            ->whereIn('attendees.status',  [1, 3]); // 查找已报名的

        // 获取当前用户作为报名人参加的所有会议
        $events2 = Event::join('registrants', 'events.id', '=', 'registrants.event_id')
            ->join('attendees', 'registrants.id', '=', 'attendees.registrant_id')
            ->select('events.id', 'events.title' , 'events.start_time' , 'events.end_time', 'events.address', 'events.longitude', 'events.latitude', 'events.status', 'events.manual_url', 'events.questionnaire', 'events.modify_deadline')
            ->where('registrants.member_id', $member_id)
            ->whereIn('attendees.status',  [1, 3]); // 查找已报名的

        // 不知道当前用户是报名人还是参会人，所以需要查报名人和参会人的数据
        $events = $events1->union($events2)->get();
        // 对$events去重
        $events = $events->unique('id');

        return SUCCESS_RESPONSE_ARRAY($events);
    }

    // 获取用户当前会议的参会通道信息
    public function getMemberEventWayInfo(Request $request)
    {
        $memberId = $request->user()->id;
        // 会议ID
        $eventId = $request->input('event_id');
        if(empty($eventId)){
            return FAIL_RESPONSE_ARRAY('缺少必要参数');
        }

        // 会议进行状态：1:未开始 2:进行中 3:已结束
        $eventStatus = $request->input('event_status');
        if(empty($eventStatus)){
            $eventStatus = Event::find($eventId)->status;
        }

        // 获取当前用户身份类型
        $memberType = $this->getMemberType($memberId, $eventId);
        if($memberType == 4){ // 当前人既不是报名人，也不是参会人（尚未参会）
            return FAIL_RESPONSE_ARRAY('用户未参加当前会议');
        }

        if ($eventStatus == 1) { // 如果会议未开始
            return $this->getMemberEventWayInfo1($memberType, $memberId, $eventId);
        }
        elseif ($eventStatus == 2) { // 如果会议进行中
            return $this->getMemberEventWayInfo2($memberType, $memberId, $eventId);
        }
        elseif ($eventStatus == 3) { // 如果会议已结束
            return $this->getMemberEventWayInfo3($memberType, $memberId, $eventId);
        }

        return FAIL_RESPONSE_ARRAY('会议进展状态有误');
    }

    // 获取用户当前会议的参会通道开始前信息
    public function getMemberEventWayInfo1($memberType, $memberId, $eventId)
    {
        $attendees = [];
        $registrant = null;
        if($memberType == 1) { // 如果只是参会人
            // 通过当前用户ID，获取参会人信息
            $attendee = Attendee::query()->select('id', 'registrant_id', 'status')
                ->where('member_id', $memberId)
                ->where('event_id', $eventId)
                ->first();

            // 通过参会人的报名人ID，获取报名人信息
            $registrant = Registrant::query()->select('id', 'name', 'phone', 'organization')
                ->where('id', $attendee->registrant_id)
                ->first();

            $attendees[] = $this->getAttendeeInfoAtEventBefore($attendee->id, $attendee->status);
        } elseif($memberType == 2 || $memberType == 3) { // 如果只是报名人 或者 即是报名人也是参会人
            // 通过当前用户ID，获取报名人信息
            $registrant = Registrant::query()->select('id', 'name', 'phone', 'organization')
                ->where('event_id', $eventId)
                ->where('member_id', $memberId)
                ->first();

            // 获取当前报名人下的参会人信息
            $list = Attendee::query()->select('id', 'status')
                ->where('event_id', $eventId)
                ->where('registrant_id', $registrant->id)
                ->get();
            // 循环list，获取参会人信息
            foreach ($list as $item){
                // 将参会人信息添加到attendees中
                $attendees[] = $this->getAttendeeInfoAtEventBefore($item->id, $item->status);
            }
        }

        $user = null;
        if (!empty($attendees)){
            // 查找对接人商务信息
            $user = $attendees[0]->user;
        }
        return SUCCESS_RESPONSE_ARRAY(compact('registrant', 'attendees', 'user'));
    }

    // 获取用户当前会议的参会通道进行中信息
    public function getMemberEventWayInfo2($memberType, $memberId, $eventId)
    {
        if($memberType == 1 || $memberType == 3) { // 如果只是参会人 或者 即是报名人也是参会人
            // 通过当前用户ID，获取参会人信息
            $attendee = Attendee::query()->select('id', 'registrant_id', 'name', 'phone', 'status', 'check_in_status', 'user_id')
                ->with('user:id,real_name,phone')
                ->where('member_id', $memberId)
                ->where('event_id', $eventId)
                ->first();
            $attendees[] = $this->getAttendeeInfoAtEventing($attendee, $memberType);
        } elseif($memberType == 2) { // 如果只是报名人
            // 通过当前用户ID，获取报名人信息
            $registrant = Registrant::query()->select('id', 'name', 'phone', 'organization')
                ->where('event_id', $eventId)
                ->where('member_id', $memberId)
                ->first();

            // 获取当前报名人下的参会人的会场签到信息
            $attendees = Attendee::query()->select('id', 'name', 'phone', 'status', 'check_in_status', 'user_id')
                ->with('user:id,real_name,phone')
                ->where('event_id', $eventId)
                ->where('registrant_id', $registrant->id)
                ->get()
            ->each(function ($item) use ($memberType) {
                $this->getAttendeeInfoAtEventing($item, $memberType);
            });
        }

        $user = null;
        if (!empty($attendees)){
            // 查找对接人商务信息
            $user = $attendees[0]->user;
        }
        return SUCCESS_RESPONSE_ARRAY(compact('memberType', 'attendees', 'user'));
    }

    // 获取用户当前会议的参会通道结束后信息
    public function getMemberEventWayInfo3($memberType, $memberId, $eventId)
    {
        $attendee = null;
        if($memberType == 1 || $memberType == 3) { // 如果只是参会人 或 即是报名人+参会人
            // 通过当前用户ID，获取参会人信息
            $attendee = Attendee::query()->select('id', 'registrant_id', 'name', 'phone', 'check_in_status', 'user_id')
                ->with('user:id,real_name,phone')
                ->where('member_id', $memberId)
                ->where('event_id', $eventId)
                ->first();
        } elseif($memberType == 2) { // 如果只是报名人
            // 通过当前用户ID，获取报名人信息
            $registrant = Registrant::query()->select('id', 'name', 'phone', 'organization')
                ->where('event_id', $eventId)
                ->where('member_id', $memberId)
                ->first();
            $attendee = Attendee::query()->select('id', 'registrant_id', 'name', 'phone', 'check_in_status', 'user_id')
                ->with('user:id,real_name,phone')
                ->where('registrant_id', $registrant->id)
                ->where('event_id', $eventId)
                ->first();
        }

        $url = Event::find($eventId)->questionnaire;
        $user = null;
        if (!empty($attendee)){
            // 查找对接人商务信息
            $user = $attendee->user;
        }
        return SUCCESS_RESPONSE_ARRAY(compact('url','user'));
    }

    // 获取单个参会人会议开始前信息1
    private function getAttendeeInfoAtEventBefore($attendeeId, $status)
    {
        if($status==Attendee::$status_cancel){
            // 报名状态为已取消的参会人，不展示就餐、接送、住宿、采访信息
            return Attendee::query()->select('id', 'event_id', 'member_id', 'registrant_id', 'user_id', 'name', 'phone', 'organization', 'position', 'gender', 'status')
                ->with('forums:id,name,is_audit,start_time,end_time,address')
                ->where('id', $attendeeId)
                ->first();
        } else {
            // 获取参会人信息
            $attendee = Attendee::query()->select('id', 'event_id', 'member_id', 'registrant_id', 'user_id', 'name', 'phone', 'organization', 'position', 'gender', 'status')
                ->where('id', $attendeeId)
                ->with('forums:id,name,is_audit,start_time,end_time,address')
                ->with('attendeeDines:id,dine_date,time_type,type,location,specific_time')
                ->with(['attendeeHotels:id,attendee_id,hotel_id,order_id,type,check_in_date,check_in_days,room_type,booking_room_number,remark,is_need_hotel,is_need_pay', 'attendeeHotels.hotel:id,name,address,longitude,latitude,is_allow_select'])
                ->with('attendeeHotelOrders:id,attendee_id,order_no,amount,type,pay_certificate,pay_status,fee_info')
                ->with(['attendeePickUps' => function($query) {
                    // 获取仅是高铁飞机的接送信息
                    $query->select('id', 'attendee_id', 'plan_arrive_time', 'is_train', 'station_info', 'place', 'to_place', 'is_need_pick_up', 'person', 'tel', 'car_one', 'car_two')
                        ->where('is_train', 1);
                }])
                ->with(['attendeeDropOffs' => function($query) {
                    // 获取仅是高铁飞机的送送信息
                    $query->select('id', 'attendee_id', 'plan_go_time', 'is_train', 'station_info', 'place', 'to_place', 'is_need_drop_off', 'person', 'tel', 'car_one', 'car_two', 'driver', 'driver_tel', 'driver_arrive_time')
                        ->where('is_train', 1);
                }])
                ->with('attendeeInterview:id,attendee_id,type,time,theme,location,interviewer')
                ->with('user:id,real_name,phone')
                ->first();

            if ($attendee) {
                // 对 forums 进行分类
                $forums = $attendee->forums->groupBy(function($forum) {
                    return $forum->pivot->type;
                });

                // 添加分类后的 forums
                $attendee->invite_forums = $forums->get(1, collect());
                $attendee->enroll_forums = $forums->get(2, collect());
                unset($attendee->forums);
            }
            return $attendee;
        }
    }

    // 获取单个参会人会议进行中信息2
    private function getAttendeeInfoAtEventing($attendee, $memberType)
    {
        if($attendee->status==Attendee::$status_ok){
            if(($memberType == 1 || $memberType == 3) && $attendee->check_in_status !== 1){
                $qrCodeStr = encryptDecrypt(false,'event:' . $attendee->id);
                // 创建会议签到二维码
                $attendee->qrCodeUrl = createQrCode($qrCodeStr);
            }

            // 根据参会人ID，获取参会人用餐信息
            $dines = AttendeeDine::join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
                ->select('attendee_dines.id', 'attendee_dines.is_need_dine', 'attendee_dines.check_in_status',
                    'dines.dine_date', 'dines.specific_time', 'dines.time_type', 'dines.type', 'dines.location')
                ->where('attendee_dines.attendee_id', $attendee->id)
                ->orderBy('dines.dine_date')
                ->orderBy('dines.time_type')
                ->get();
            // 循环dines，获取用餐信息
            foreach ($dines as $item){
                if(($memberType == 1 || $memberType == 3) && $item->check_in_status !== 1){
                    $qrCodeStr = encryptDecrypt(false,'dine:01='.$attendee->id.'&02='.$item->id);
                    // 创建用餐签到二维码
                    $item->qrCodeUrl = createQrCode($qrCodeStr);
                }
            }
            $attendee->dines = $dines;

            // 根据参会人ID，获取参会人采访信息
            $interview = AttendeeInterview::select('id', 'type', 'time', 'theme', 'location', 'interviewer')
                ->where('attendee_id', $attendee->id)
                ->first();
            $attendee->interview = $interview;

            // 根据参会人ID，获取参会人会议进行中论坛信息
            $forums = AttendeeForums::join('forums', 'attendee_forums.forum_id', '=', 'forums.id')
                ->select('attendee_forums.id', 'forums.name', 'forums.start_time', 'forums.end_time', 'forums.address', 'attendee_forums.intention', 'attendee_forums.type')
                ->where('attendee_forums.attendee_id', $attendee->id)
                ->where('attendee_forums.audit_status', 1)
                ->orderBy('forums.start_time')
                ->get();
            $attendee->forums = $forums;

            // 根据参会人ID，获取参会人会议进行中接站信息
            $pickUps = AttendeePickUp::query()
                ->select('id', 'attendee_id', 'plan_arrive_time', 'is_train', 'station_info', 'place', 'to_place', 'is_need_pick_up', 'person', 'tel', 'car_one', 'car_two')
                ->where('attendee_id', $attendee->id)
                ->where('is_train', 2)
                ->get();
            $attendee->pickUps = $pickUps;

            // 根据参会人ID，获取参会人会议进行中送站信息
            $dropOffs = AttendeeDropOff::query()
                ->select('id', 'attendee_id', 'plan_go_time', 'is_train', 'station_info', 'place', 'to_place', 'is_need_drop_off', 'person', 'tel', 'car_one', 'car_two', 'driver', 'driver_tel', 'driver_arrive_time')
                ->where('attendee_id', $attendee->id)
                ->where('is_train', 2)
                ->get();
            $attendee->dropOffs = $dropOffs;
        }

        return $attendee;
    }

    // 根据memberId、EventID获取用户身份类型
    private function getMemberType($memberId, $eventId)
    {
        // 查找报名人信息
        $registrant = Registrant::query()
            ->where('member_id', $memberId)
            ->where('event_id', $eventId)
            ->first();

        // 查找参会人消息
        $attendee = Attendee::query()
            ->where('member_id', $memberId)
            ->where('event_id', $eventId)
            ->first();

        if($registrant && $attendee){
            return 3; // 即是报名人，又是参会人
        }
        elseif ($registrant) {
            return 2; // 报名人
        }
        elseif ($attendee) {
            return 1; // 参会人
        }
        else {
            return 4; // 当前人既不是报名人，也不是参会人（尚未参会）
        }
    }

    // 更新接站信息
    public function updatePickup(Request $request, string $id)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        // 修改接站信息
        $attendeePickUp = AttendeePickUp::find($id);
        if (!$attendeePickUp) {
            return FAIL_RESPONSE_ARRAY('接站信息不存在');
        }
        $old = clone $attendeePickUp;

        $data = filterRequestData('attendee_pick_ups');
        $creator = "客户id：" . $request->user()->id . ' 手机号：' . $request->user()->phone;
        $data['updater'] = $creator;
        $result = $attendeePickUp->fill($data);

        // 获取修改内容（必须在更新前获取，否则isDirty失效，会获取到空值）
        $editContent = $this->getEditContent($old, $result);

        $result = $result->update();
        if(!$result){
            return FAIL_RESPONSE_ARRAY('更新接站信息失败');
        }

        if(!empty($editContent)){
            // 将$editContent 的item 拼接成一个字符串
            $content = implode('，', $editContent);
            // 提交客户更新接站信息消息
            $this->submitMessage($attendee_id, '客户更新了接站信息：' . $content, $creator);
        }

        return SUCCESS_RESPONSE_ARRAY("更新接站信息成功");
    }

    // 更新送站信息
    public function updateDropOff(Request $request, string $id)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        // 修改送站信息
        $attendeeDropOff = AttendeeDropOff::find($id);
        if (!$attendeeDropOff) {
            return FAIL_RESPONSE_ARRAY('送站信息不存在');
        }
        $old = clone $attendeeDropOff;

        $data = filterRequestData('attendee_drop_offs');
        $creator = "客户id：" . $request->user()->id . ' 手机号：' . $request->user()->phone;
        $data['updater'] = $creator;
        $result = $attendeeDropOff->fill($data);

        // 获取修改内容（必须在更新前获取，否则isDirty失效，会获取到空值）
        $editContent = $this->getEditContent($old, $result);

        $result = $result->update();
        if(!$result){
            return FAIL_RESPONSE_ARRAY('更新送站信息失败');
        }

        if(!empty($editContent)){
            // 将$editContent 的item 拼接成一个字符串
            $content = implode('，', $editContent);
            // 提交客户更新接站信息消息
            $this->submitMessage($attendee_id, '客户更新了送站信息：' . $content, $creator);
        }

        return SUCCESS_RESPONSE_ARRAY("更新送站信息成功");
    }

    // 更新酒店住宿信息
    public function updateHotel(Request $request, int $id)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        // 修改送站信息
        $attendeeHotel = AttendeeHotel::find($id);
        if (!$attendeeHotel) {
            return FAIL_RESPONSE_ARRAY('酒店信息不存在');
        }
        $old = clone $attendeeHotel;

        $data = filterRequestData('attendee_hotels');
        $creator = "客户：" . $request->user()->id;
        $data['updater'] = $creator;
        $result = $attendeeHotel->forceFill($data);

        // 获取修改内容（必须在更新前获取，否则isDirty失效，会获取到空值）
        $editContent = $this->getEditContent($old, $result);

        $result = $result->save();
        if(!$result){
            return FAIL_RESPONSE_ARRAY('更新酒店住宿信息失败');
        }

        if(!empty($editContent)){
            // 将$editContent 的item 拼接成一个字符串
            $content = implode('，', $editContent);

//            $attendee->notify(new Message('客户更新了酒店住宿信息：' . $content));
            // 提交客户更新接站信息消息
            $this->submitMessage($attendee_id, '客户更新了酒店住宿信息：' . $content, $creator);
        }

        return SUCCESS_RESPONSE_ARRAY("更新酒店住宿信息成功");
    }

    // 更新酒店住宿信息
    public function updateDine(Request $request)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }

        $dines = $request->input('dines');
        foreach ($dines as $dine) {
            // 修改用餐信息
            $attendeeDine = AttendeeDine::find($dine["id"]);
            if (!$attendeeDine) {
                return FAIL_RESPONSE_ARRAY('用餐信息不存在');
            }
            $old = clone $attendeeDine;

            $attendeeDine->is_need_dine = $dine["is_need_dine"];

            // 获取修改内容（必须在更新前获取，否则isDirty失效，会获取到空值）
            $editContent = $this->getEditContent($old, $attendeeDine);

            $updater = "客户id：" . $request->user()->id . ' 手机号：' . $request->user()->phone;
            $attendeeDine->updater = $updater;
            $result = $attendeeDine->update();

            if(!$result){
                return FAIL_RESPONSE_ARRAY('更新用餐信息失败');
            }

            if(!empty($editContent)){
                // 将$editContent 的item 拼接成一个字符串
                $content = implode('，', $editContent);
                // 提交客户更新消息
                $this->submitMessage($attendee_id, '客户更新了用餐信息：' . $content, $updater);
            }
        }

        return SUCCESS_RESPONSE_ARRAY("更新用餐信息成功");
    }

    // 提交客户消息
    public function submitMessage($attendee_id, $content, $creator)
    {
        $message = new Message();
        $message->attendee_id = $attendee_id;
        $message->content = $content;
        $message->creator = $creator;;
        $message->source = Message::$source_customer_submit;
        $message->is_read = 2;

        // 提交消息
        return $message->save();
    }

    public function getDropHotels(Request $request)
    {
        $event_id = $request->input('event_id');
        $type = $request->input('type');
        // 方案二：客户选择酒店和房间数，商务选择房型（可选择的酒店是允许自行选择的酒店）
        if($type == 2){
            $hotels = Hotel::select('id', 'name', 'address', 'longitude', 'latitude')
                ->where('event_id', $event_id)
                ->where('is_allow_select', 1)
                ->get();
        } else {
            $hotels = Hotel::select('id', 'name', 'address', 'longitude', 'latitude')
                ->where('event_id', $event_id)
                ->get();
        }

        return SUCCESS_RESPONSE_ARRAY($hotels);
    }

    // 获取酒店订单待支付详情页信息
    public function getHotelOrderInfo(Request $request)
    {
        $order_id = $request->input('order_id');

        $order = Order::join('attendee_hotels', 'attendee_hotels.order_id', '=', 'orders.id')
            ->join('attendees', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->join('events', 'events.id', '=', 'attendees.event_id')
            ->where('orders.id', $order_id)
            ->select('orders.*', 'attendees.name as attendee_name', 'events.title as event_title', 'events.address as event_address')
            ->first();

        return SUCCESS_RESPONSE_ARRAY($order);
    }

    public function eventCheckIn(Request $request)
    {
//        $attendee_id = decryptNumber($request->input('attendee_id'));
//        $event_id = decryptNumber($request->input('event_id'));
//        $phone = decryptNumber($request->input('phone'));
        $attendee_id = $request->input('attendee_id');
        $event_id = $request->input('event_id');
        $phone = $request->input('phone');

        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }

        if($attendee->event_id != $event_id){
            return FAIL_RESPONSE_ARRAY('参会人签到会议错误');
        }

        if($attendee->phone != $phone){
            return FAIL_RESPONSE_ARRAY('参会人签到手机号错误');
        }

        $attendee->check_in_status = 1;
        $attendee->check_in_time = now();
        $attendee->check_in_type = 1; // 签到类型：1:扫码仪 2:现场签到 3:纸质签到
        $result = $attendee->update();
        if(!$result){
            return FAIL_RESPONSE_ARRAY('会场扫码签到失败');
        }

        return SUCCESS_RESPONSE_ARRAY("会场扫码签到成功");
    }

    public function dineCheckIn(Request $request)
    {
//        $attendee_id = decryptNumber($request->input('attendee_id'));
//        $dine_id = decryptNumber($request->input('dine_id'));
//        $phone = decryptNumber($request->input('phone'));
        $attendee_id = $request->input('attendee_id');
        $dine_id = $request->input('dine_id');
        $phone = $request->input('phone');

        $attendeeDine = AttendeeDine::find($dine_id);
        if (!$attendeeDine) {
            return FAIL_RESPONSE_ARRAY('参会人用餐信息不存在');
        }

        if($attendeeDine->attendee_id != $attendee_id){
            return FAIL_RESPONSE_ARRAY('签到用餐参会人ID不匹配');
        }

        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }

        if($attendee->phone != $phone){
            return FAIL_RESPONSE_ARRAY('参会人签到手机号错误');
        }

        $attendeeDine->check_in_status = 1;
        $attendeeDine->updater = "客户签到";
        $result = $attendeeDine->update();
        if(!$result){
            return FAIL_RESPONSE_ARRAY('用餐签到失败');
        }

        return SUCCESS_RESPONSE_ARRAY("用餐签到成功");
    }


    // 获取用户当前会议的签到码信息
    public function getMemberCheckInCodes(Request $request)
    {
        $memberId = $request->user()->id;
        // 会议ID
        $eventId = $request->input('event_id');
        if(empty($eventId)){
            return FAIL_RESPONSE_ARRAY('缺少必要参数');
        }

        // 查找参会人信息
        $attendee = Attendee::query()->select('id', 'registrant_id', 'name', 'phone', 'status', 'check_in_status')
            ->where('member_id', $memberId)
            ->where('event_id', $eventId)
            ->first();
        if(!$attendee){ // 当前人既不是报名人，也不是参会人（尚未参会）
            return FAIL_RESPONSE_ARRAY('用户未参加当前会议');
        }

//        // 会议进行状态：1:未开始 2:进行中 3:已结束
//        $eventStatus = Event::find($eventId)->status;
//        if($eventStatus != 2){
//            return FAIL_RESPONSE_ARRAY('会议未开始或已结束');
//        }

        if($attendee->status==Attendee::$status_ok) {
            if ($attendee->check_in_status !== 1) {
                $qrCodeUrl = HOST . '/api/applet/member/eventWay/eventCheckIn?phone=' . encryptNumber($attendee->phone)
                    . '&attendee_id=' . encryptNumber($attendee->id) . '&event_id=' . encryptNumber($eventId);
//            $qrCodeUrl = HOST . '/api/applet/member/eventWay/eventCheckIn?phone='.$attendee->phone
//                .'&attendee_id='.$attendee->id.'&event_id='.$eventId;
                // 创建会议签到二维码
                $attendee->qrCodeUrl = createQrCode($qrCodeUrl);
            }

            // 根据参会人ID，获取参会人用餐信息
            $dines = AttendeeDine::join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
                ->select('attendee_dines.id', 'attendee_dines.is_need_dine', 'attendee_dines.check_in_status',
                    'dines.dine_date', 'dines.specific_time', 'dines.time_type', 'dines.type', 'dines.location')
                ->where('attendee_dines.attendee_id', $attendee->id)
                ->get();
            // 循环dines，获取用餐信息
            foreach ($dines as $item) {
                if ($item->check_in_status !== 1) {
                    $qrCodeUrl = HOST . '/api/applet/member/eventWay/dineCheckIn?phone=' . encryptNumber($attendee->phone)
                        . '&attendee_id=' . encryptNumber($attendee->id) . '&dine_id=' . encryptNumber($item->id);
//                $qrCodeUrl = HOST . '/api/applet/member/eventWay/dineCheckIn?phone='.$attendee->phone
//                    .'&attendee_id='.$attendee->id.'&dine_id='.$item->id;
                    // 创建用餐签到二维码
                    $item->qrCodeUrl = createQrCode($qrCodeUrl);
                }
            }
            $attendee->dines = $dines;
        }

        return SUCCESS_RESPONSE_ARRAY($attendee);
    }

    // 顶部消息提醒
    public function getTopTips(Request $request)
    {
        $memberId = $request->user()->id;

        $message = [];

        // 作为报名人获取报名会议ID
        $registrants = Registrant::query()->select('id', 'event_id')
            ->where('member_id', $memberId)
            ->get();
        if($registrants){
            foreach ($registrants as $registrant){
                $eventId = $registrant->event_id;
                $event = Event::find($eventId);
                // 会议进行状态：1:未开始 2:进行中 3:已结束
                if($event->status < 3){
                    // 查找当前报名人下的参会人
                    $attendees = Attendee::query()->select('id', 'name', 'event_id')
                        ->where('registrant_id', $registrant->id)
                        ->where('event_id', $eventId)
                        ->where('status', 1)
                        ->get();
                    foreach ($attendees as $attendee){
                        $this->getAttendeeTopMessage($attendee,$message);
                    }
                }
            }

        } else {
            // 作为参会人获取报名会议ID
            $attendees = Attendee::query()->select('id', 'name', 'event_id')
                ->where('member_id', $memberId)
                ->where('status', 1)
                ->get();
            foreach ($attendees as $attendee){
                $eventId = $attendee->event_id;
                $event = Event::find($eventId);
                // 会议进行状态：1:未开始 2:进行中 3:已结束
                if($event->status < 3){
                    $this->getAttendeeTopMessage($attendee, $message);
                }
            }
        }

        return SUCCESS_RESPONSE_ARRAY($message);
    }

    // 查找当前参会人填写信息不完整的消息
    private function getAttendeeTopMessage($attendee, &$message)
    {
        // 查找当前参会人的送站信息
        $dropOffs = AttendeeDropOff::query()->select('id')
            ->where('attendee_id', $attendee->id)
            ->where('is_need_drop_off', 1)
            ->where('is_train', 1)
            // 送站信息填写不完整的：station_info、place、to_place 为空的
            ->where(function ($query){
                $query->whereNull('plan_go_time')
                    ->orWhere('station_info', '')
                    ->orWhere('place', '')
                    ->orWhere('to_place', '');
            })
            ->get();
        if(!$dropOffs->isEmpty()){
            // 创建送站信息填写不完整的消息对象，添加到消息数组中
            $message[] = [
                'type' => 'dropOff',
                'event_id' => $attendee->event_id,
                'attendee_id' => $attendee->id,
                'attendee_name' => $attendee->name,
                'message' => '送站信息填写不完整',
            ];
        }

        // 查找当前参会人的接站信息
        $pickUps = AttendeePickUp::query()->select('id')
            ->where('attendee_id', $attendee->id)
            ->where('is_need_pick_up', 1)
            ->where('is_train', 1)
            // 接站信息填写不完整的：station_info、place、to_place 为空的
            ->where(function ($query){
                $query->whereNull('plan_arrive_time')
                    ->orWhere('station_info', '')
                    ->orWhere('place', '')
                    ->orWhere('to_place', '');
            })
            ->get();
        if(!$pickUps->isEmpty()){
            // 创建接站信息填写不完整的消息对象，添加到消息数组中
            $message[] = [
                'type' => 'pickUp',
                'event_id' => $attendee->event_id,
                'attendee_id' => $attendee->id,
                'attendee_name' => $attendee->name,
                'message' => '接站信息填写不完整',
            ];
        }

        // 查找当前参会人的酒店住宿信息
        $hotels = AttendeeHotel::query()->select('id')
            ->where('attendee_id', $attendee->id)
            ->where('is_need_hotel', 1)
            // 酒店住宿信息填写不完整的：hotel_id、room_type、room_number 为空的
            ->where(function ($query){
                $query->whereNull('hotel_id')
                    ->orWhereNull('check_in_date')
                    ->orWhereNull('check_in_days')
                    ->orWhereNull('booking_room_number')
                    ->orWhereNull('room_type');
            })->get();
        if(!$hotels->isEmpty()){
            // 创建酒店住宿信息填写不完整的消息对象，添加到消息数组中
            $message[] = [
                'type' => 'hotel',
                'event_id' => $attendee->event_id,
                'attendee_id' => $attendee->id,
                'attendee_name' => $attendee->name,
                'message' => '酒店住宿信息填写不完整',
            ];
        }

        // 查找当前参会人的邀请论坛信息
        $forums = AttendeeForums::query()->select('id')
            ->where('attendee_id', $attendee->id)
            ->where('type', 1)
            ->where('intention', 2)
            ->get();
        if(!$forums->isEmpty()){
            $message[] = [
                'type' => 'forum',
                'event_id' => $attendee->event_id,
                'attendee_id' => $attendee->id,
                'attendee_name' => $attendee->name,
                'message' => '邀请论坛参加意向未填写',
            ];
        }
    }

    private function getEditContent($old, $result)
    {
        $editContent = [];
        if($result->isDirty()){
            $dirtyArray = $result->getDirty();
            foreach ($dirtyArray as $key => $value){
                switch ($key) {
                    // 接站更新信息
                    case 'is_need_pick_up':
                        $editContent[] = '是否需要接站：【' . $old->is_need_pick_up==1 ? '需要' : '不需要' . '】->【' . ($value==1 ? '需要' : '不需要') . '】';
                        break;
                    case 'plan_arrive_time':
                        $editContent[] = '预计到达时间：【' . $old->plan_arrive_time . '】->【' . $value . '】';
                        break;
                    case 'place':
                        $editContent[] = '接站地点：【' . $old->place . '】->【' . $value . '】';
                        break;
                    case 'station_info':
                        $editContent[] = '航班/高铁/地点信息：【' . $old->station_info . '】->【' . $value . '】';
                        break;

                    // 送站更新信息
                    case 'is_need_drop_off':
                        $editContent[] = '是否需要送站：【' . $old->is_need_drop_off==1 ? '需要' : '不需要' . '】->【' . ($value==1 ? '需要' : '不需要') . '】';
                        break;
                    case 'plan_go_time':
                        $editContent[] = '预计出发时间：【' . $old->plan_go_time . '】->【' . $value . '】';
                        break;

                    // 酒店住宿更新信息
                    case 'is_need_hotel':
                        $editContent[] = '是否需要住宿：【' . $old->is_need_hotel==1 ? '需要' : '不需要' . '】->【' . ($value==1 ? '需要' : '不需要') . '】';
                        break;
                    case 'hotel_id':
                        $hotel1 = Hotel::find($old->hotel_id);
                        $hotel2 = Hotel::find($value);
                        $editContent[] = '酒店：【' . ($hotel1?$hotel1->name : '') . '】->【' . ($hotel2?$hotel2->name : '') . '】';
                        break;
                    case 'check_in_date':
                        $editContent[] = '入住日期：【' . $old->check_in_date . '】->【' . $value . '】';
                        break;
                    case 'check_in_days':
                        $editContent[] = '入住几晚：【' . $old->check_in_days . '】->【' . $value . '】';
                        break;
                    case 'booking_room_number':
                        $editContent[] = '预定房间数：【' . $old->booking_room_number . '】->【' . $value . '】';
                        break;
                    case 'room_type':
                        $editContent[] = '房型：【' . $old->room_type . '】->【' . $value . '】';
                        break;
                    case 'remark':
                        $editContent[] = '备注：【' . $old->remark . '】->【' . $value . '】';
                        break;

                    // 餐饮更新信息
                    case 'is_need_dine':
                        $dine = Dine::find($old->dine_id);
                        $editContent[] = Carbon::parse($dine->dine_date)->format('Y-m-d') . '的' . Dine::convertTimeType($dine->time_type) . '餐，是否需要用餐：【' . ($old->is_need_dine==1 ? '需要' : '不需要') . '】->【' . ($value==1 ? '需要' : '不需要') . '】';
                        break;

                    default:
                        $value1 = $old->$key;
                        if(is_array($value1)){
                            $value1 = json_encode($value1);
                        }
                        if(is_array($value)){
                            $value = json_encode($value);
                        }

                        $editContent[] = "{$key}：【{$value1}】->【{$value}】";
                        break;
                }
            }
        }

        return $editContent;
    }

}
