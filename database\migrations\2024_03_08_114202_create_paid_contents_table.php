<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paid_contents', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->nullable(false)->default(0)->comment('会议id');
            $table->string('title')->default('')->comment('付费内容标题');
            $table->string('cover')->default('')->comment('付费内容封面');
            $table->string('introduction', 500)->default('')->comment('付费内容介绍');
            $table->decimal('price', 10, 2)->default(0.00)->comment('付费内容价格');
            $table->tinyInteger('is_on')->default(2)->comment('内容状态（1:上架 2:下架）');
//            $table->tinyInteger('type')->default(0)->comment('付费内容类型(1:上传视频 2:填写视频VideoID)');
            //付费内容视频地址或Vid todo 只走保利威（服务器带宽空间考虑）
            $table->string('video_url')->nullable(false)->default('')->comment('视频VideoID');
            $table->string('creator', 20)->nullable()->comment('添加人');
            $table->string('updater', 20)->nullable()->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `paid_contents` comment '付费内容表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paid_contents');
    }
};
