<?php

namespace App\Http\Controllers\Admin\Stat;

use App\Http\Controllers\Controller;
use App\Repositories\DepAllStatRepository;
use Illuminate\Http\Request;

class DepAllDataStatController extends Controller
{

    // 各事业部会议指标数据统计
    public function getAllDeptEventKpiDataStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepEventKpiDataStat($request);
    }

    // 各事业部论坛指标数据统计
    public function getAllDepForumKpiDataStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepForumKpiDataStat($request);
    }

    // 各事业部论坛总指标数据统计
    public function getAllDepForumAllKpiDataStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepForumAllKpiDataStat($request);
    }

    // 各事业部参会人员身份统计
    public function getAllDepAttendeeIdentityStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepAttendeeIdentityStat($request);
    }

    // 各事业部近七天邀约参会人人数统计
    public function getAllDepNear7DaysAttendeeStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepNear7DaysAttendeeStat($request);
    }


    // 各事业部酒店预定数据统计
    public function getAllDepHotelBookingDataStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepHotelBookingDataStat($request);
    }

    // 各事业部某个酒店各事业部入住人数统计
    public function getAllDepHotelCheckInNumStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepHotelCheckInNumStat($request);
    }



    // 各事业部用餐数据统计
    public function getAllDepDineDataStat(Request $request, DepAllStatRepository $repository)
    {
        return $repository->getAllDepDineDataStat($request);
    }
}
