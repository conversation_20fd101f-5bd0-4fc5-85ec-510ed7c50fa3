<?php

use App\Http\Controllers\Admin\Data\ChannelController;
use App\Http\Controllers\Admin\Data\DepartmentController;
use App\Http\Controllers\Admin\Data\DineController;
use App\Http\Controllers\Admin\Data\HotelBookingController;
use App\Http\Controllers\Admin\Data\HotelController;
use App\Http\Controllers\Admin\Data\KpiController;
use App\Http\Controllers\Admin\Event\AttendeeController;
use App\Http\Controllers\Admin\IndexController;
use App\Http\Controllers\Admin\Serve\AttendeeDineController;
use App\Http\Controllers\Admin\Serve\AttendeeDropOffController;
use App\Http\Controllers\Admin\Serve\AttendeeHotelController;
use App\Http\Controllers\Admin\Serve\AttendeeInterviewController;
use App\Http\Controllers\Admin\Serve\AttendeePickUpController;
use App\Http\Controllers\Admin\Serve\MessageController;
use App\Http\Controllers\Admin\Serve\NoticeController;
use App\Http\Controllers\Admin\System\LoginController;
use App\Http\Controllers\Admin\System\MenuController;
use App\Http\Controllers\Admin\System\PermissionController;
use App\Http\Controllers\Admin\System\RoleController;
use App\Http\Controllers\Admin\System\UserAccessLogController;
use App\Http\Controllers\Admin\System\UserController;
use Illuminate\Support\Facades\Route;

Route::post('system/login', [LoginController::class, 'login']);
Route::get('login_fail', [LoginController::class, 'loginFail'])->name('login');

Route::group(['middleware' => ['auth:api', 'user_permission_restrict']], function () {
    Route::group(['prefix' => 'index'], function () {
        // 首页
        Route::get('todoAssignList', [IndexController::class, 'getTodoAssignList'])->name('index.todoAssign.list');
        Route::get('newAssignAttendeeList', [IndexController::class, 'getBusinessNewAssignAttendeeList'])->name('index.newAssignAttendee.list');
        Route::get('attendeeEditInfoList', [IndexController::class, 'getAttendeeEditInfoList'])->name('index.attendeeEditInfo.list');
    });

    Route::group(['prefix' => 'system'], function () {
        //用户模块
        Route::post('users/list', [UserController::class, 'list'])->name('system.users.list'); //用户列表
        Route::post('users', [UserController::class, 'store'])->name('system.users.store');// 用户添加
        Route::put('users/{id}', [UserController::class, 'update'])->name('system.users.update');// 用户更新
        Route::put('users/reset_password/{id}', [UserController::class, 'resetPassword'])->name('system.users.reset_password');// 重置密码
        Route::put('users/change_state/{id}', [UserController::class, 'changeState'])->name('system.users.change_state');// 修改用户状态
        Route::put('users/change_password/{id}', [UserController::class, 'changePassword'])->name('system.users.change_password');// 作废
        Route::put('users/simple_update/{id}', [UserController::class, 'simpleUpdate'])->name('system.users.simple_update');
        Route::put('users/logout/{id}', [UserController::class, 'logout'])->name('system.users.logout');

        //角色模块
        Route::get('roles/drop_roles', [RoleController::class, 'dropRoles'])->name('system.roles.drop_roles');
        Route::post('roles/list', [RoleController::class, 'list'])->name('system.roles.list');
        Route::post('roles', [RoleController::class, 'store'])->name('system.roles.store');
        Route::put('roles/{id}', [RoleController::class, 'update'])->name('system.roles.update');
        Route::get('roles/bind_permissions/{id}', [RoleController::class, 'bindPermissions'])->name('system.roles.bind_permissions');
        Route::post('roles/bind_permissions/{id}', [RoleController::class, 'bindPermissionsAction'])->name('system.roles.bind_permissions_action');

        Route::get('roles/bind_menus/{id}', [RoleController::class, 'bindMenus'])->name('system.roles.bind_menus');
        Route::post('roles/bind_menus_action/{id}', [RoleController::class, 'bindMenusAction'])->name('system.roles.bind_menus_action');

        // 菜单模块
        Route::get('menus/user_side_bar', [MenuController::class, 'userSideBar'])->name('system.menus.user_side_bar');
        Route::post('menus/list', [MenuController::class, 'list'])->name('system.menus.list');
        Route::post('menus', [MenuController::class, 'store'])->name('system.menus.store');//
        Route::put('menus/{id}', [MenuController::class, 'update'])->name('system.menus.update');//
        Route::get('menus/category_one', [MenuController::class, 'categoryOne'])->name('system.menus.category_one');//
        Route::get('menus', [MenuController::class, 'index'])->name('menus.index'); //暂时无用

        // 权限模块
        Route::get('permissions/drop_permissions', [PermissionController::class, 'dropPermissions'])->name('system.permissions.drop_permissions');
        Route::post('permissions/list', [PermissionController::class, 'list'])->name('system.permissions.list');
        Route::post('permissions', [PermissionController::class, 'store'])->name('system.permissions.store');//
        Route::put('permissions/{id}', [PermissionController::class, 'update'])->name('system.permissions.update');//
        Route::get('user_all_permissions', [PermissionController::class, 'userAllPermissions'])->name('system.permissions.user_all_permissions');//

        // 用户访问日志
        Route::post('user_access_logs/list', [UserAccessLogController::class, 'list'])->name('system.user_access_logs.list');

        //
        Route::get('department/list', [DepartmentController::class, 'list'])->name('system.department.list');

    });

    // 基础数据管理
    Route::group(['prefix' => 'data'], function () {
        // 部门管理
        Route::get('dep/list', [DepartmentController::class, 'list'])->name('data.dep.list');
        Route::post('dep', [DepartmentController::class, 'store'])->name('data.dep.store');
        Route::put('dep/{id}', [DepartmentController::class, 'update'])->name('data.dep.update');
        Route::put('dep/changeStatus/{id}', [DepartmentController::class, 'changeStatus'])->name('data.dep.changeStatus');
        Route::get('dep/oneDrop', [DepartmentController::class, 'oneDepartmentsDrop'])->name('data.dep.oneDrop');
        Route::get('dep/twoDropByParentId/{id}', [DepartmentController::class, 'getDepartmentByParentId'])->name('data.dep.twoDropByParentId');

        // 邀约渠道管理
        Route::get('channels/list', [ChannelController::class, 'index'])->name('data.channels.list');
        Route::post('channels', [ChannelController::class, 'store'])->name('data.channels.store');
        Route::put('channels/{id}', [ChannelController::class, 'update'])->name('data.channels.update');
        Route::put('channels/changeStatus/{id}', [ChannelController::class, 'changeStatus'])->name('data.channels.changeStatus');
        Route::get('channels/dropList', [ChannelController::class, 'dropList'])->name('data.channels.dropList');

        // 指标管理
        Route::get('kpis/list', [KpiController::class, 'index'])->name('data.kpis.list');
        Route::post('kpis', [KpiController::class, 'store'])->name('data.kpis.store');
        Route::put('kpis/{id}', [KpiController::class, 'update'])->name('data.kpis.update');
        Route::delete('kpis/{id}', [KpiController::class, 'destroy'])->name('data.kpis.destroy');

        // 用餐管理
        Route::get('dine/list', [DineController::class, 'index'])->name('data.dine.list');
        Route::get('dine/dropDineDates', [DineController::class, 'dropDineDatesByEventId'])->name('data.dine.dropDineDates');
        Route::get('dine/dropDineLocations', [DineController::class, 'dropDineLocationsByEventId'])->name('data.dine.dropDineLocations');
        Route::post('dine', [DineController::class, 'store'])->name('data.dine.store');
        Route::put('dine/{id}', [DineController::class, 'update'])->name('data.dine.update');
        Route::delete('dine/{id}', [DineController::class, 'destroy'])->name('data.dine.destroy');

        // 酒店管理
        Route::get('hotel/list', [HotelController::class, 'index'])->name('data.hotel.list');
        Route::post('hotel', [HotelController::class, 'store'])->name('data.hotel.store');
        Route::put('hotel/{id}', [HotelController::class, 'update'])->name('data.hotel.update');
        // 当前会议的酒店
        Route::get('hotelsByEventId/{event_id}', [HotelController::class, 'getHotelsByEventId'])->name('serve.event.hotels');
        Route::get('hotelsByEventIds', [HotelController::class, 'getHotelsByEventIds'])->name('serve.event.hotelsByEvents');
        Route::get('hotel/bookingDates/{id}', [HotelController::class, 'getHotelBookingDates'])->name('data.hotel.bookingDates');

        // 酒店预定管理
        Route::get('booking/list', [HotelBookingController::class, 'index'])->name('data.hotel.booking.list');
        Route::post('booking', [HotelBookingController::class, 'store'])->name('data.hotel.booking.store');
        Route::put('booking/{id}', [HotelBookingController::class, 'update'])->name('data.hotel.booking.update');
    });


    //会议管理
    require base_path('routes/event.php');
    require base_path('routes/order.php');
    require base_path('routes/stat.php');


    // 服务管理
    Route::group(['prefix' => 'serve'], function () {
        // 参会人服务对接列表
        Route::post('attendee/list', [AttendeeController::class, 'index'])->name('serve.attendee.list');
        Route::post('attendee/export', [AttendeeController::class, 'export'])->name('serve.attendee.export');
        Route::get('attendee/{id}', [AttendeeController::class, 'show'])->name('serve.attendee.show')->where('id', '[0-9]+');
        Route::put('attendee/{id}', [AttendeeController::class, 'update'])->name('serve.attendee.update')->where("id", "[0-9]+");

        // 参会人跟进信息
        Route::get('attendee/{id}/messages', [MessageController::class, 'getAttendeeMessages'])->name('serve.attendee.messages')->where('id', '[0-9]+');
        // 添加参会人跟进信息
        Route::post('attendee/messages', [MessageController::class, 'store'])->name('serve.attendee.messages.store');

        // 参会人就餐信息（参会人已选的就餐为选中状态）
        Route::get('attendee/{id}/dines', [AttendeeDineController::class, 'attendeeDines'])->name('serve.attendee.dines')->where('id', '[0-9]+');
        // 当前参会人当前会议下所有就餐信息（含当前参会人选择的会议为选中状态）：设置单个参会人就餐信息使用
        Route::get('attendee/{id}/eventAttendeeDines', [AttendeeDineController::class, 'eventAttendeeDines'])->name('serve.event.allDines')->where('id', '[0-9]+');
        // 设置参会人就餐信息
        Route::put('attendee/{id}/setAttendeeDine', [AttendeeDineController::class, 'setAttendeeDine'])->name('serve.attendee.setAttendeeDine')->where('id', '[0-9]+');
        // 当前会议下所有就餐信息：批量设置参会人就餐信息使用
        Route::get('event/{id}/dines', [AttendeeDineController::class, 'eventDines'])->name('serve.event.dines')->where('id', '[0-9]+');
        // 批量设置参会人就餐信息
        Route::put('attendee/setMoreAttendeeDine', [AttendeeDineController::class, 'setMoreAttendeeDine'])->name('serve.attendee.setMoreAttendeeDine');
        // 删除参会人就餐信息
        Route::delete('attendee/dines/{id}', [AttendeeDineController::class, 'destroy'])->name('serve.attendee.dines.destroy')->where('id', '[0-9]+');
        // 设置参会人是否需要就餐
        Route::put('attendee/setIsNeedDine/{id}', [AttendeeDineController::class, 'setIsNeedDine'])->name('serve.attendee.dines.setIsNeedDine')->where('id', '[0-9]+');

        // 参会人接站信息
        Route::get('attendee/{id}/pickUps', [AttendeePickUpController::class, 'getAttendeePickUps'])->name('serve.attendee.pickUps')->where('id', '[0-9]+');
        // 添加参会人接站信息
        Route::post('attendee/pickUps', [AttendeePickUpController::class, 'store'])->name('serve.attendee.pickUps.store');
        // 接站人列表
        Route::get('attendee/pickUpPersons', [AttendeePickUpController::class, 'getAttendeePickUpPerson'])->name('serve.attendee.getAttendeePickUpPerson');
        // 修改参会人接站信息
        Route::put('attendee/pickUps/{id}', [AttendeePickUpController::class, 'update'])->name('serve.attendee.pickUps.update')->where('id', '[0-9]+');
        // 删除参会人接站信息
        Route::delete('attendee/pickUps/{id}', [AttendeePickUpController::class, 'destroy'])->name('serve.attendee.pickUps.destroy')->where('id', '[0-9]+');
        // 批量添加参会人接站信息
        Route::post('attendee/setMoreAttendeePickUp', [AttendeePickUpController::class, 'setMoreAttendeePickUp'])->name('serve.attendee.pickUps.setMoreAttendeePickUp');
        // 设置参会人是否需要接站
        Route::put('attendee/setIsNeedPickUp/{id}', [AttendeePickUpController::class, 'setIsNeedPickUp'])->name('serve.attendee.pickUps.setIsNeedPickUp')->where('id', '[0-9]+');

        // 参会人送站信息
        Route::get('attendee/{id}/dropOffs', [AttendeeDropOffController::class, 'getAttendeeDropOffs'])->name('serve.attendee.dropOffs')->where('id', '[0-9]+');
        // 添加参会人送站信息
        Route::post('attendee/dropOffs', [AttendeeDropOffController::class, 'store'])->name('serve.attendee.dropOffs.store');
        // 修改参会人送站信息
        Route::put('attendee/dropOffs/{id}', [AttendeeDropOffController::class, 'update'])->name('serve.attendee.dropOffs.update')->where('id', '[0-9]+');
        // 删除参会人送站信息
        Route::delete('attendee/dropOffs/{id}', [AttendeeDropOffController::class, 'destroy'])->name('serve.attendee.dropOffs.destroy')->where('id', '[0-9]+');
        // 批量添加参会人送站信息
        Route::post('attendee/setMoreAttendeeDropOff', [AttendeeDropOffController::class, 'setMoreAttendeeDropOff'])->name('serve.attendee.dropOffs.setMoreAttendeeDropOff');
        // 设置参会人是否需要送站
        Route::put('attendee/setIsNeedDropOff/{id}', [AttendeeDropOffController::class, 'setIsNeedDropOff'])->name('serve.attendee.dropOffs.setIsNeedDropOff')->where('id', '[0-9]+');

        // 参会人酒店住宿信息
        Route::get('attendee/{id}/hotels', [AttendeeHotelController::class, 'getAttendeeHotels'])->name('serve.attendee.hotels');
        // 添加参会人酒店住宿信息
        Route::post('attendee/hotels', [AttendeeHotelController::class, 'store'])->name('serve.attendee.hotels.store');
        // 修改参会人酒店住宿信息
        Route::put('attendee/hotels/{id}', [AttendeeHotelController::class, 'update'])->name('serve.attendee.hotels.update')->where('id', '[0-9]+');
        // 删除参会人酒店住宿信息
        Route::delete('attendee/hotels/{id}', [AttendeeHotelController::class, 'destroy'])->name('serve.attendee.hotels.destroy')->where('id', '[0-9]+');
        // 批量添加参会人酒店住宿信息
        Route::post('attendee/setMoreAttendeeHotel', [AttendeeHotelController::class, 'setMoreAttendeeHotel'])->name('serve.attendee.dropOffs.setMoreAttendeeHotel');
        // 设置参会人是否需要住宿
        Route::put('attendee/setIsNeedHotel/{id}', [AttendeeHotelController::class, 'setIsNeedHotel'])->name('serve.attendee.hotels.setIsNeedHotel')->where('id', '[0-9]+');

        // 添加参会人采访信息
        Route::post('attendee/interviews', [AttendeeInterviewController::class, 'save'])->name('serve.attendee.interviews.save');

        // 参会人就餐信息列表
        Route::get('dines', [AttendeeDineController::class, 'index'])->name('serve.dines.list');
        Route::post('dines/export', [AttendeeDineController::class, 'exportExcelStyle'])->name('serve.dines.list.export');
        Route::put('dines/setMoreIsNeedDine', [AttendeeDineController::class, 'setMoreIsNeedDine'])->name('serve.dines.setMoreIsNeedDine');
        // 参会人住宿信息列表
        Route::get('hotels', [AttendeeHotelController::class, 'index'])->name('serve.hotels.list');
        Route::post('hotels/export', [AttendeeHotelController::class, 'export'])->name('serve.hotels.list.export');
        Route::put('hotels/setMoreIsNeedHotel', [AttendeeHotelController::class, 'setMoreIsNeedHotel'])->name('serve.hotels.setMoreIsNeedHotel');
        Route::put('hotels/setIsNeedPay/{id}', [AttendeeHotelController::class, 'setIsNeedPay'])->name('serve.hotels.setIsNeedPay')->where('id', '[0-9]+');
        Route::put('hotels/setRoomNum/{id}', [AttendeeHotelController::class, 'setRoomNum'])->name('serve.hotels.setRoomNum')->where('id', '[0-9]+');
        // 参会人接站信息列表
        Route::get('pickUps', [AttendeePickUpController::class, 'index'])->name('serve.pickUps.list');
        Route::post('pickUps/export', [AttendeePickUpController::class, 'export'])->name('serve.pickUps.list.export');
        Route::put('pickUps/setMoreIsNeedPickUp', [AttendeePickUpController::class, 'setMoreIsNeedPickUp'])->name('serve.pickUps.setMoreIsNeedPickUp');
        Route::put('pickUps/setMorePickUpPerson', [AttendeePickUpController::class, 'setMorePickUpPerson'])->name('serve.pickUps.setMorePickUpPerson');
        // 参会人送站信息列表
        Route::get('dropOffs', [AttendeeDropOffController::class, 'index'])->name('serve.dropOffs.list');
        Route::post('dropOffs/export', [AttendeeDropOffController::class, 'export'])->name('serve.dropOffs.list.export');
        Route::put('dropOffs/setMoreIsNeedDropOff', [AttendeeDropOffController::class, 'setMoreIsNeedDropOff'])->name('serve.dropOffs.setMoreIsNeedDropOff');
        Route::put('dropOffs/setMoreDropOffPerson', [AttendeeDropOffController::class, 'setMoreDropOffPerson'])->name('serve.dropOffs.setMoreDropOffPerson');
        // 参会人采访信息列表
        Route::get('interviews', [AttendeeInterviewController::class, 'index'])->name('serve.interviews.list');
        Route::post('interviews/export', [AttendeeInterviewController::class, 'export'])->name('serve.interviews.list.export');

        // 短信通知列表
        Route::get('notices/smsList', [NoticeController::class, 'smsList'])->name('serve.notices.smsList');
        Route::post('notices/getSendNum', [NoticeController::class, 'getSendNum'])->name('serve.notices.getSendNum');
        Route::post('notices/smsStore', [NoticeController::class, 'smsStore'])->name('serve.notices.smsStore');

    });

});
