<?php

namespace App\Http\Controllers\Admin\Serve;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\AttendeeHotel;
use App\Repositories\AttendeeHotelExport;
use App\Repositories\AttendeeHotelRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class AttendeeHotelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, AttendeeHotelRepository $repository)
    {
        $query = $repository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('attendee_hotels.id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 导出
    public function export(Request $request, AttendeeHotelRepository $repository)
    {
        try {
            $list = $repository->listBuilder($request)->orderBy('attendee_hotels.id', 'desc')->get();
            // 导出是否有权限查看手机号
            $is_view_phone = $request->user()->is_view_phone == 1;
            $export = new AttendeeHotelExport($list, $is_view_phone);// 导出数据到 Excel 文件
            return Excel::download($export, '参会人酒店住宿名单服务列表.xlsx');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    public function getAttendeeHotels(string $id)
    {
        // 当前参会人下的酒店信息
        $hotels = AttendeeHotel::where('attendee_id', $id)->orderBy('check_in_days', 'asc')->with('hotel')->get();
        return SUCCESS_RESPONSE_ARRAY($hotels);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        $data = filterRequestData('attendee_hotels');
        // 判断当前参会人是否有当前入住日期的酒店信息
        $attendeeHotel = AttendeeHotel::where('attendee_id', $attendee_id)->where('check_in_date', $data['check_in_date'])->with('attendee')->first();
        if ($attendeeHotel) {
            return FAIL_RESPONSE_ARRAY("参会人【" . $attendeeHotel->attendee->name . '】已存在当前入住日期的酒店信息');
        }
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $attendeeHotel = AttendeeHotel::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('attendeeHotel'));
    }

    // 批量创建酒店信息

    public function setMoreAttendeeHotel(Request $request)
    {
        $attendeeIds = $request->input('attendeeIds');
        if (empty($attendeeIds)) {
            return FAIL_RESPONSE_ARRAY('未选择参会人');
        }

        foreach ($attendeeIds as $attendee_id) {
            $attendee = Attendee::find($attendee_id);
            if (!$attendee) {
                return FAIL_RESPONSE_ARRAY('参会人不存在');
            }

            // 新增
            $data = filterRequestData('attendee_hotels');
            // 判断当前参会人是否有当前入住日期的酒店信息
            $attendeeHotel = AttendeeHotel::where('attendee_id', $attendee_id)->where('check_in_date', $data['check_in_date'])->with('attendee')->first();
            if ($attendeeHotel) {
                return FAIL_RESPONSE_ARRAY("参会人【" . $attendeeHotel->attendee->name . '】已存在当前入住日期的酒店信息');
            }

            $data['attendee_id'] = $attendee_id;
            $data['creator'] = $request->user()->real_name;
            $data['updater'] = $request->user()->real_name;

            AttendeeHotel::forceCreate($data);
        }

        return SUCCESS_RESPONSE_ARRAY('批量设置成功');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
        $attendeeHotel = AttendeeHotel::find($id);
        if (!$attendeeHotel) {
            return FAIL_RESPONSE_ARRAY('酒店住宿信息不存在');
        }
        $data = filterRequestData('attendee_hotels');
        $data['updater'] = $request->user()->real_name;
        $attendeeHotel->forceFill($data)->save();
        return SUCCESS_RESPONSE_ARRAY(compact('attendeeHotel'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
        $attendeeHotel = AttendeeHotel::find($id);
        if (!$attendeeHotel) {
            return FAIL_RESPONSE_ARRAY('酒店住宿信息不存在');
        }
        $attendeeHotel->delete();
        return SUCCESS_RESPONSE_ARRAY("删除成功");
    }

    // 修改是否需要支付
    public function setIsNeedPay(Request $request, string $id)
    {
        $attendeeHotel = AttendeeHotel::find($id);
        if (!$attendeeHotel) {
            return FAIL_RESPONSE_ARRAY('酒店住宿信息不存在');
        }
        $attendeeHotel->is_need_pay = $attendeeHotel->is_need_pay == 1 ? 2 : 1;
        $attendeeHotel->updater = $request->user()->real_name;
        $attendeeHotel->update();
        return SUCCESS_RESPONSE_ARRAY("修改成功");
    }

    public function setRoomNum(Request $request, string $id)
    {
        $attendeeHotel = AttendeeHotel::find($id);
        if (!$attendeeHotel) {
            return FAIL_RESPONSE_ARRAY('酒店住宿信息不存在');
        }
        $attendeeHotel->room_number = $request->input('room_number');
        $attendeeHotel->updater = $request->user()->real_name;
        $attendeeHotel->update();
        return SUCCESS_RESPONSE_ARRAY("修改成功");
    }

    // 修改是否需要住宿
    public function setIsNeedHotel(Request $request, string $id)
    {
        $attendeeHotel = AttendeeHotel::find($id);
        if (!$attendeeHotel) {
            return FAIL_RESPONSE_ARRAY('酒店住宿信息不存在');
        }
        $attendeeHotel->is_need_hotel = $attendeeHotel->is_need_hotel == 1 ? 2 : 1;
        $attendeeHotel->updater = $request->user()->real_name;
        $attendeeHotel->update();
        return SUCCESS_RESPONSE_ARRAY("修改成功");
    }

    // 批量修改是否需要住宿
    public function setMoreIsNeedHotel(Request $request)
    {
        $attendeeHotelIds = $request->input('attendeeHotelIds');
        $is_need_hotel = $request->input('is_need_hotel');
        if (empty($attendeeHotelIds)) {
            return FAIL_RESPONSE_ARRAY('未选择酒店住宿信息');
        }
        // 循环$dineIds，修改是否需要就餐
        foreach ($attendeeHotelIds as $attendeeHotelId) {
            $attendeeHotel= AttendeeHotel::find($attendeeHotelId);
            if (!$attendeeHotel) {
                return FAIL_RESPONSE_ARRAY('酒店住宿信息不存在，attendeeDropOffId为：' . $attendeeHotelId);
            }
            $attendeeHotel->is_need_hotel = $is_need_hotel;
            $attendeeHotel->updater = $request->user()->real_name;
            $attendeeHotel->update();
        }

        return SUCCESS_RESPONSE_ARRAY("批量设置成功");
    }
}
