<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendee_dines', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人ID');
            $table->unsignedInteger('dine_id')->default(0)->comment('用餐ID');
            $table->tinyInteger('is_need_dine')->default(2)->comment('客户选择是否需要： 1:需要 2:不需要');
            $table->tinyInteger('check_in_status')->default(2)->comment('签到状态：1:已签到 2:未签到 ');
            $table->timestamp('check_in_time')->nullable()->comment('签到时间');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('更新人');

            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `attendee_dines` comment '参会人用餐关系表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendee_dines');
    }
};
