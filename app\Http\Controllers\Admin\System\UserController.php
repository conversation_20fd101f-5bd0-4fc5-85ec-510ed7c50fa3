<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;
use App\Models\Admin\PasswordHistory;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Traits\PasswordTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class UserController extends Controller
{
    use PasswordTrait;

    /**
     *  用户列表
     * @param Request $request
     * @param UserRepository $userRepository
     * @return array
     */
    public function list(Request $request, UserRepository $userRepository): array
    {
        $query = $userRepository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')
            ->with(['roles:id,name', 'department_one:id,name', 'department_two:id,name'])
            ->get()->each(function ($item) {
                $item->role_names = $item->roles->pluck('name')->join(',');
                $item->department_one_name = $item->department_one->name ?? '';
                $item->department_two_name = $item->department_two->name ?? '';
            });
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    /**
     *  保存用户
     * @param Request $request
     * @return array
     */
    public function store(Request $request): array
    {
        //
        $data = filterRequestData('users');
        $data['updater'] = $request->user()->real_name;
        $data['creator'] = $request->user()->real_name;
        $data['api_token'] = bcrypt(time());
        $password = $this->generatePassword();
        $data['password'] = bcrypt($password);

        $departments = $request->input('departments');
        if ($departments) {
            $data['department_one_id'] = $departments[0];
            // 判断$departments的长度
            if( count($departments) > 1){
                $data['department_two_id'] = $departments[1];
            }
        }
        $user = User::forceCreate($data);
        // 保存中间表 角色
        $user->roles()->sync($request->input('roles'));
        return SUCCESS_RESPONSE_ARRAY($password);
    }

    /**
     * 更新用户
     * @param Request $request
     * @param string $id
     * @return array
     */
    public function update(Request $request, string $id): array
    {
        //
        $user = User::find($id);
        $data = filterRequestData('users');
        $data['updater'] = $request->user()->real_name;
        $departments = $request->input('departments');
        if ($departments) {
            $data['department_one_id'] = $departments[0];
            // 判断$departments的长度
            if( count($departments) > 1){
                $data['department_two_id'] = $departments[1];
            }
        }
        $rst = $user->fill($data)->save();
        $user->roles()->sync($request->input('roles'));
        return SUCCESS_RESPONSE_ARRAY($rst);
    }

    // 重置密码
    public function resetPassword(Request $request, string $id): array
    {
        //
        $user = User::find($id);
        $password = $this->generatePassword();
        $user->password = bcrypt($password);
        $user->api_token = bcrypt(time());
        $user->updater = $request->user()->real_name;
        $user->save();
        return SUCCESS_RESPONSE_ARRAY($password);
    }

    function modifyPassword(Request $request)
    {
        $data = $request->all();
        $user = User::find($request->input('id'));

        // 1.检查旧密码是否匹配
        if (!Hash::check($data['source_password'], $user->password)) {
            return FAIL_RESPONSE_ARRAY('用户原密码错误');
        }

        // 2.两次新密码是否一致
        $validator = Validator::make($data, [
            'password' => 'confirmed',
        ], [
            'password.confirmed' => '确认密码不匹配',
        ]);

        if ($validator->fails()) {
            return FAIL_RESPONSE_ARRAY($validator->errors()->first());
        }
        // 3. 密码至少8位,必须包含大写字母、小写字母、数字
        $validator = Validator::make($data, [
            'password' => 'required|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
        ], [
            'password.regex' => '密码至少8位,必须包含大写字母、小写字母、数字',
            'password.required' => '密码至少8位,必须包含大写字母、小写字母、数字',
            'password.min' => '密码至少8位,必须包含大写字母、小写字母、数字',
        ]);
        if ($validator->fails()) {
            return FAIL_RESPONSE_ARRAY($validator->errors()->first());
        }

        // 5.这个密码在最近三次使用过，不能使用
        $passList = PasswordHistory::where('user_id', $user->id)->orderBy('id', 'desc')->limit(3)->pluck('password');
        if ($passList->contains(md5($data['password']))) {
            return FAIL_RESPONSE_ARRAY('这个密码在最近三次使用过，不能使用');
        }

        // 6.添加重置密码记录.
        $history = [
            'user_id' => $user->id,
            'real_name' => $user->real_name,
            'password' => md5($data['password']),
        ];
        PasswordHistory::create($history);

        $user->fill([
            'api_token' => bcrypt(time()),
            'password' => bcrypt($data['password'])])->save();
        return SUCCESS_ARRAY;
    }

    /**
     *  修改用户状态
     * @param Request $request
     * @param $id
     * @return array
     */
    public function changeState(Request $request, $id): array
    {
        $state = $request->input('state');
        $user = User::find($id);
        $user->state = $state;
        $user->api_token = bcrypt(time());
        $user->updater = $request->user()->real_name;
        $rst = $user->save();
        return SUCCESS_RESPONSE_ARRAY($rst);

    }

    /**
     *  修改用户密码
     * @param Request $request
     * @param $id
     * @return array
     */
    public function changePassword(Request $request, $id): array
    {
        $user = $request->user();
        $oldPass = $request->input('old_pass');
        if (!Hash::check($oldPass, $user->password)) {
            return FAIL_RESPONSE_ARRAY('旧密码错误');
        }
        $password = $request->input('password');
        $passwordAgain = $request->input('password_again');
        if ($password !== $passwordAgain) {
            return FAIL_RESPONSE_ARRAY('新旧密码不一致');
        }
        $user->password = $password;
        $user->api_token = bcrypt(time());
        $rst = $user->save();

        return SUCCESS_RESPONSE_ARRAY($rst);
    }

    /**
     *  只更新用户表
     * @param Request $request
     * @param $id
     * @return array
     */
    public function simpleUpdate(Request $request, $id): array
    {
        $user = User::find($id);
        $data = filterRequestData('users');
        $data['updater'] = $request->user()->real_name;
        $rst = $user->fill($data)->save();
        return SUCCESS_RESPONSE_ARRAY($user);
    }

    // 测试邮箱验证激活
//    public function checkEmail(Request $request, $token)
//    {
//        $user = User::where('remember_token', $token)->first();
//        if ($user){
//            $user->state = 1;
//            $user->save();
//            return SUCCESS_RESPONSE_ARRAY($user);
//        } else {
//            return FAIL_RESPONSE_ARRAY("token错误");
//        }
//    }

    /**
     *  注销
     * @param Request $request
     * @param $id
     * @return array
     */
    public function logout(Request $request, $id): array
    {
        $user = User::find($id);
        $user->api_token = bcrypt(time());
        $rst = $user->save();
        return SUCCESS_RESPONSE_ARRAY($rst);
    }
}
