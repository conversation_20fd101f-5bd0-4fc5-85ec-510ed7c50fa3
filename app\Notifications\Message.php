<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class Message extends Notification implements ShouldQueue
{
    use Queueable;
    public $notification;

    /**
     * Create a new notification instance.
     */
    public function __construct($notification)
    {
        //
        $this->notification = $notification;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
//        return (new MailMessage)
//            ->subject('邮件通知')
//            ->greeting('您好！')
//            ->line("文本通知内容：" . $this->notification )
//            ->line('Thank you for using our application!');

//        return (new MailMessage)
////            ->error()
//            ->subject('注册成功，激活账户')
//            ->greeting('您好！')
//            ->line($this->notification->real_name . '您已注册成功，请点击下方链接激活账户：')
//            ->action('验证邮箱', url('api/check_email/' . $this->notification->remember_token))
//            ->line('Thank you for using our application!');

//        // 发送带模板邮件
        return (new MailMessage)
            ->subject('SEEE会议酒店住宿信息')
            ->view(
            'emails.userNotification', ['notification' => $this->notification]
        );
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable)
    {
        return $this->notification;
    }

}
