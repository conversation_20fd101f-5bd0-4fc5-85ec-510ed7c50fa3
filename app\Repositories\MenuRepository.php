<?php

namespace App\Repositories;

use App\Models\Menu;
use Carbon\Carbon;
use Illuminate\Http\Request;

class MenuRepository
{
    public function listBuilder(Request $request)
    {
        $menu_name = $request->input('menu_name');
        $updated_ats = $request->input('updated_ats');


        return Menu::when($menu_name, fn($query) => $query->whereMenuName($menu_name))
            ->when($updated_ats, fn($query) => $query->whereBetween('updated_at', searchBetweenDate($updated_ats)));
    }
}
