<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property int $event_id 会议ID
 * @property string|null $dine_date 用餐日期
 * @property int $time_type 早中晚类型：1:早 2:中 3:晚
 * @property int $type 用餐类型：1:电子餐券 2:自助餐 3:座位就餐 4：包间宴请
 * @property string $location 用餐地点
 * @property string $identity 用餐人身份
 * @property string $specific_time 具体用餐时间
 * @property int $is_allow_select 是否让客户自行选择：1:是 2:否
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Event|null $event
 * @method static \Illuminate\Database\Eloquent\Builder|Dine newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Dine newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Dine pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|Dine query()
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereIdentity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereIsAllowSelect($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereSpecificTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereTimeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dine whereUpdater($value)
 * @mixin \Eloquent
 */
class Dine extends Model
{
    use HasFactory, PaginationTrait;

    protected $casts = [
        'dine_date' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    protected $guarded = [];

    // 一个用餐对应一个会议
    public function event()
    {
        return $this->hasOne(Event::class, 'id', 'event_id');
    }

    public static function convertTimeType($type)
    {
        switch ($type) {
            case 1:
                return '早';
            case 2:
                return '中';
            case 3:
                return '晚';
            default:
                return '';
        }
    }

    // 用餐类型：1:电子餐券 2:自助餐 3:座位就餐 4：包间宴请
    public static function convertType($type)
    {
        switch ($type) {
            case 1:
                return '电子餐券';
            case 2:
                return '自助餐';
            case 3:
                return '座位就餐';
            case 4:
                return '包间宴请';
            default:
                return '';
        }
    }

    // 签到状态：1:未签到 2:已签到 3:已取消
    public static function convertCheckInStatus($status)
    {
        switch ($status) {
            case 1:
                return '已签到';
            case 2:
                return '未签到';
            default:
                return '';
        }
    }
}
