<?php

namespace App\YuanBo\Sms;

use App\YuanBo\Contracts\Sms\SmsSender;

class SmsManager
{
    protected $app;

    private $sender = null;

    public function __construct($app)
    {
        $this->app = $app;
    }

    public function setSender($sender)
    {
        $this->sender = $this->app->make($sender);
        return $this;
    }

    public function __call($name, $arguments)
    {
        return $this->sender->{$name}(...$arguments);
    }
}
