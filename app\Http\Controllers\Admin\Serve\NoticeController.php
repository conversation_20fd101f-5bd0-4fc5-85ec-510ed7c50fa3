<?php

namespace App\Http\Controllers\Admin\Serve;

use App\Http\Controllers\Controller;
use App\Models\Channel;
use App\Models\SmsSendLog;
use App\Models\SmsNotice;
use App\Repositories\NoticeRepository;
use Illuminate\Http\Request;

class NoticeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    // 获取短信通知列表
    public function smsList(Request $request, NoticeRepository $repository)
    {
        $query = $repository->smsListBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('sms_notices.id', 'desc')->get();
        // 循环list
        $list->map(function ($item) {
            $item->filter = json_decode($item->filter_json);
            // 判断$item->filter中是否有channel_id属性
            if(isset($item->filter->channel_id)){
                $channel = Channel::find($item->filter->channel_id);
                $item->filter->channel_name = $channel->name;
            }
        });
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 获取短信通知列表
    public function getSendNum(Request $request, NoticeRepository $repository)
    {
        $event_id = $request->input('event_id');
//        $event_status = $request->input('event_status');
//        $identity_type = $request->input('identity_type');
//        $organization = $request->input('organization');
//        $channel_id = $request->input('channel_id');
//        $is_need_pick_up = $request->input('is_need_pick_up');
//        $is_need_drop_off = $request->input('is_need_drop_off');
//        $is_need_hotel = $request->input('is_need_hotel');
//        $is_need_dine = $request->input('is_need_dine');
        if(!$event_id){
            return FAIL_RESPONSE_ARRAY("请选择会议");
        }
        $filter = $request->all();
        $query = $repository->getSendData($filter);
        $cnt = $query->count();
//        $list = $query->pagination()->get();
//        $list = $query->pluck("phone")->toArray();
        return SUCCESS_RESPONSE_ARRAY(compact('cnt'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function smsStore(Request $request, NoticeRepository $repository)
    {
        return $repository->smsStore($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

}
