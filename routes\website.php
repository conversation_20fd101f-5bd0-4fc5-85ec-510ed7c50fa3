<?php

use App\Http\Controllers\Website\AttendeeController;
use App\Http\Controllers\Website\InvitationCodeController;
use App\Http\Controllers\Website\PostersController;
use App\Http\Controllers\Website\SpeechController;
use App\Http\Controllers\Website\VisitStatsController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Website\EventAnnouncementController;
use App\Http\Controllers\Website\EventMaterialController;
use App\Http\Controllers\Website\EventPhotoController;
use App\Http\Controllers\Website\EventHistoryController;


Route::group(['prefix' => 'website', 'middleware' => 'visit.stats'], function () {
    //会议资料列表
    Route::get('materials', [EventMaterialController::class,'index']);
    //资料·详情
    Route::get('materials/{id}', [EventMaterialController::class, 'show'])->name('website.materials.show');
    //会议图片列表
    Route::get('photos', [EventPhotoController::class,'index']);
    //图片详情
    Route::get('photos/{id}', [EventPhotoController::class, 'show'])->name('website.materials.show');
    //会议公告
    Route::get('announcements', [EventAnnouncementController::class,'index']);
    //公告详情
    Route::get('announcements/{id}', [EventAnnouncementController::class, 'show'])->name('website.announcements.show');
    //往届回顾
    Route::get('histories', [EventHistoryController::class,'index']);
    //往届回顾详情
    Route::get('histories/{id}', [EventHistoryController::class, 'show'])->name('website.histories.show');
    //校验邀请码
    Route::get('invitation_code/{invitation_code}', [InvitationCodeController::class, 'check']);
    //官网报名用户
    Route::post('attendees/store', [AttendeeController::class, 'store'])->name('website.attendees.store');
    //官网新增申请演讲
    Route::post('speeches', [SpeechController::class, 'store'])->name('website.speeches.store');
    //官网查看审核通过的申请演讲
    Route::get('speeches/index', [SpeechController::class, 'index'])->name('website.speeches.index');
    Route::post('speeches/list', [SpeechController::class, 'list'])->name('website.speeches.list');
    //统计
    Route::get('count', [VisitStatsController::class, 'count'])->name('visit.stats.count');
    //海报提交
    Route::post('posters/store', [PostersController::class, 'store'])->name('website.posters.store');


    Route::group(['middleware' => ['auth:api']], function () {
        //资料新增
        Route::post('materials/store', [EventMaterialController::class, 'store'])->name('website.materials.store');
        //资料更新
        Route::put('materials/{id}/update', [EventMaterialController::class, 'update'])->name('website.materials.update');
        //资料删除
        Route::post('materials/{id}/destroy', [EventMaterialController::class, 'destroy'])->name('website.materials.destroy');

        //公告新增
        Route::post('announcements/store', [EventAnnouncementController::class, 'store'])->name('website.announcements.store');
        //公告更新
        Route::put('announcements/{id}/update', [EventAnnouncementController::class, 'update'])->name('website.announcements.update');
        //公告删除
        Route::post('announcements/{id}/destroy', [EventAnnouncementController::class, 'destroy'])->name('website.announcements.destroy');

        //图片新增
        Route::post('photos/store', [EventPhotoController::class, 'store'])->name('website.photos.store');
        //图片编辑
        Route::put('photos/{id}/update', [EventPhotoController::class, 'update'])->name('website.photos.update');
        //图片删除
        Route::post('photos/{id}/destroy', [EventPhotoController::class, 'destroy'])->name('website.photos.destroy');

        //往届回顾新增
        Route::post('histories/store', [EventHistoryController::class, 'store'])->name('website.histories.store');
        //往届回顾更新
        Route::put('histories/{id}/update', [EventHistoryController::class, 'update'])->name('website.histories.update');
        //往届回顾删除
        Route::post('histories/{id}/destroy', [EventHistoryController::class, 'destroy'])->name('website.histories.destroy');

        //申请演讲列表
        Route::get('speeches/list', [SpeechController::class, 'list'])->name('website.speeches.list');
        //申请演讲审核
        Route::put('speeches/audit/{id}', [SpeechController::class, 'audit'])->name('website.speeches.audit');
        Route::put('speeches/message/{id}', [SpeechController::class, 'message'])->name('website.speeches.message');
        Route::post('speeches/export', [SpeechController::class, 'export'])->name('website.speeches.export');

        //海报列表
        Route::post('posters/list', [PostersController::class, 'index'])->name('website.posters.list');
        //海报审核
        Route::put('posters/audit/{id}', [PostersController::class, 'audit'])->name('website.posters.audit')->where('id', '[0-9]+');
        Route::put('posters/message/{id}', [PostersController::class, 'message'])->name('website.posters.message');
        Route::post('posters/export', [PostersController::class, 'export'])->name('website.posters.export');
    });
});
