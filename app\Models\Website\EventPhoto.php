<?php

namespace App\Models\Website;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $title 标题
 * @property string $cover 封面
 * @property string $sub_title 副标题
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereCover($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereSubTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventPhoto whereUpdater($value)
 * @mixin \Eloquent
 */
class EventPhoto extends Model
{
    use HasFactory,PaginationTrait;

    protected $connection = 'mysql_website';

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function getCoverAttribute($value)
    {
        return config('filesystems.disks.sftp.domain') . $value;
    }

    public function setCoverAttribute($value)
    {
        // 假设 config('filesystems.disks.sftp.domain') 返回的是前缀
        $prefix = config('filesystems.disks.sftp.domain');

        // 检查 $value 是否以前缀开头
        if (strpos($value, $prefix) === 0) {
            // 如果是，则去掉前缀
            $value = substr($value, strlen($prefix));
        }

        // 设置 cover 属性
        $this->attributes['cover'] = $value;
    }
}
