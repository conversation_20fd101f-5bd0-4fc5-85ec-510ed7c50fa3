<?php

namespace App\Http\Controllers\Tool;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    //

    public function uploadImage(Request $request): array
    {
        $data = $this->uploadFileToSftp($request);
//        echo json_encode($data);
        return SUCCESS_RESPONSE_ARRAY($data);
    }

    public function uploadFile(Request $request): array
    {
        $data = $this->uploadFileToSftp($request, 'files');
        return SUCCESS_RESPONSE_ARRAY($data);
    }

    public function uploadTinyMceImage(Request $request): array
    {
        $data = $this->uploadFileToSftp($request, 'files');
        return array_merge(SUCCESS_ARRAY, ['location' => $data['path']]);
    }

    private function uploadFileToSftp(Request $request, $type = 'images'): array
    {
        $path = '';
        $relative_path = '';
        $size = '';
        if ($request->hasFile('file')) {
            $dir = "admin_seee/{$type}/" . Carbon::now()->format('Y-m');
            $file_name = Carbon::now()->format('YmdHis') . Str::random(6) . '.' . $request->file('file')->getClientOriginalExtension();
            $path = Storage::disk('sftp')->putFileAs($dir, $request->file('file'), $file_name);
            $path = config('filesystems.disks.sftp.domain') . $path;
            // 相对路径
            $relative_path = $dir . '/' . $file_name;
            $size = $request->file('file')->getSize() / (1024 * 1024);
            $size = round($size, 4) . 'MB';
        }
        $request->file('file')->getBasename();
        $client_original_name = $request->file('file')->getClientOriginalName();
        return compact('path', 'relative_path', 'client_original_name', 'size');
    }


}
