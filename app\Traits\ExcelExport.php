<?php

namespace App\Traits;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;

class ExcelExport implements FromCollection, WithTitle
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $data;
    public $header;
    public $title;
    public $dataTransformCallback;

    public function __construct($data, $header, $title, callable $dataTransformCallback = null)
    {
        $this->data = $data;
        $this->header = $header;
        $this->title = $title;
        $this->dataTransformCallback = $dataTransformCallback;
    }

    // 从集合中导出数据
    public function collection()
    {
        return new Collection($this->createData());
    }

    // 设置sheet名称
    public function title(): string
    {
        return $this->title;
    }

    public function createData(): array
    {
        $data = $this->data;
        $header = $this->header;
        $result = [];
        $result[] = $header;
        //获取表头字段名称
        foreach ($header as $key => $value) {
            $keyArr[] = $key;
        }

        //获取数据
        foreach ($data as $key => $value) {
            // 应用数据转换回调函数
            if ($this->dataTransformCallback) {
                $value = ($this->dataTransformCallback)($value);
            }
            $row = [];
            for ($i = 0; $i < count($keyArr); $i++) {
                $key = $keyArr[$i];
                $row = array_merge($row, [$keyArr[$i] => !is_array($value) ? $value->$key : $value[$key]]);
            }
            array_push($result, $row);
        }
        return $result;
    }

}
