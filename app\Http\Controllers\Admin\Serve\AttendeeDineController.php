<?php

namespace App\Http\Controllers\Admin\Serve;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\AttendeeDine;
use App\Models\Dine;
use App\Repositories\AttendeeDineExport;
use App\Repositories\AttendeeDineRepository;
use App\Traits\ExcelExport;
use App\Traits\ExcelExportStyle;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class AttendeeDineController extends Controller
{
    public function index(Request $request, AttendeeDineRepository $repository)
    {
        $query = $repository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('attendee_dines.id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 导出
    public function export(Request $request, AttendeeDineRepository $repository)
    {
        try {
            $list = $repository->listBuilder($request)->orderBy('attendee_dines.id', 'desc')->get();
            $export = new AttendeeDineExport($list);// 导出数据到 Excel 文件
            return Excel::download($export, '参会人就餐名单服务列表.xlsx');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    public function exportExcel(Request $request, AttendeeDineRepository $repository)
    {
        try {
            $list = $repository->listBuilder($request)->orderBy('attendee_dines.id', 'desc')->get()
//                ->each(function (&$item) {
//                    $item->attendee_phone_str = hidePhoneNumber($item->attendee_phone) ?? '';
//                    $item->attendee_identity_str = Attendee::convertIdentityType($item->attendee_identity) ?? '';
//                    $item->time_type_str = Dine::convertTimeType($item->time_type) ?? '';
//                    $item->type_str = Dine::convertType($item->type) ?? '';
//                    $item->check_in_status_str = Dine::convertCheckInStatus($item->check_in_status) ?? '';
//                    $item->is_need_dine_str = $item->is_need_dine === 1 ? '需要' : '不需要';
//                })
            ;

            // 数据转换逻辑
            $dataTransformCallback = function ($row) {
                $row['attendee_phone'] = hidePhoneNumber($row['attendee_phone']) ?? '';
                $row['attendee_identity'] = Attendee::convertIdentityType($row['attendee_identity']) ?? '';
                $row['time_type'] = Dine::convertTimeType($row['time_type']) ?? '';
                $row['type'] = Dine::convertType($row['type']) ?? '';
                $row['check_in_status'] = Dine::convertCheckInStatus($row['check_in_status']) ?? '';
                $row['is_need_dine'] = $row['is_need_dine'] == 1 ? '需要' : '不需要';
                return $row;
            };

            $title = '参会人就餐名单服务列表';
            $header = [
                'id' => '编号',
                'event_short_title' => '会议标题',
                'attendee_name' => '参会人姓名',
                'attendee_phone' => '参会人手机号',
                'attendee_identity' => '身份类别',
                'attendee_organization' => '所在单位',
                'attendee_position' => '职称职务',
                'is_need_dine' => '是否需要就餐',
                'dine_date' => '就餐日期',
                'time_type' => '早/中/晚类型',
                'type' => '就餐类型',
                'specific_time' => '具体就餐时间',
                'location' => '就餐地址',
                'user_real_name' => '对接人',
                'check_in_status' => '签到状态',
                'updater' => '最后更新人',
                'updated_at' => '最后更新时间'
            ];

            return Excel::download(new ExcelExport($list, $header, $title, $dataTransformCallback), $title . date('YmdHis') . '.xls');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }


    public function exportExcelStyle(Request $request, AttendeeDineRepository $repository)
    {
        try {
            $list = $repository->listBuilder($request)->orderBy('attendee_dines.id', 'desc')->get()
//                ->each(function (&$item) {
//                    $item->attendee_phone_str = hidePhoneNumber($item->attendee_phone) ?? '';
//                    $item->attendee_identity_str = Attendee::convertIdentityType($item->attendee_identity) ?? '';
//                    $item->time_type_str = Dine::convertTimeType($item->time_type) ?? '';
//                    $item->type_str = Dine::convertType($item->type) ?? '';
//                    $item->check_in_status_str = Dine::convertCheckInStatus($item->check_in_status) ?? '';
//                    $item->is_need_dine_str = $item->is_need_dine === 1 ? '需要' : '不需要';
//                })
            ;

            // 数据转换逻辑
            $dataTransformCallback = function ($row) {
                $row['attendee_phone'] = hidePhoneNumber($row['attendee_phone']) ?? '';
                $row['attendee_identity'] = Attendee::convertIdentityType($row['attendee_identity']) ?? '';
                $row['time_type'] = Dine::convertTimeType($row['time_type']) ?? '';
                $row['type'] = Dine::convertType($row['type']) ?? '';
                $row['check_in_status'] = Dine::convertCheckInStatus($row['check_in_status']) ?? '';
                $row['is_need_dine'] = $row['is_need_dine'] == 1 ? '需要' : '不需要';
                return $row;
            };

            $title = '参会人就餐名单服务列表';
            // 导出配置：表头名称及表头宽度
            $header = [
                'id' => ['name' => '编号', 'width' => 10],
                'event_short_title' => ['name' => '会议标题', 'width' => 30],
                'attendee_name' => ['name' => '参会人姓名', 'width' => 15],
//                'attendee_phone_str' => ['name' => '参会人手机号', 'width' => 15],
                'attendee_phone' => ['name' => '参会人手机号', 'width' => 15],
//                'attendee_identity_str' => ['name' => '身份类别', 'width' => 15],
                'attendee_identity' => ['name' => '身份类别', 'width' => 15],
                'attendee_organization' => ['name' => '所在单位', 'width' => 25],
                'attendee_position' => ['name' => '职称职务', 'width' => 15],
//                'is_need_dine_str' => ['name' => '是否需要就餐', 'width' => 15],
                'is_need_dine' => ['name' => '是否需要就餐', 'width' => 15],
                'dine_date' => ['name' => '就餐日期', 'width' => 15],
//                'time_type_str' => ['name' => '早/中/晚类型', 'width' => 15],
                'time_type' => ['name' => '早/中/晚类型', 'width' => 15],
                'specific_time' => ['name' => '具体就餐时间', 'width' => 25],
//                'type_str' => ['name' => '就餐类型', 'width' => 15],
                'type' => ['name' => '就餐类型', 'width' => 15],
                'location' => ['name' => '就餐地址', 'width' => 30],
//                'check_in_status_str' => ['name' => '签到状态', 'width' => 15],
                'check_in_status' => ['name' => '签到状态', 'width' => 15],
                'user_real_name' => ['name' => '对接人', 'width' => 15],
                'updater' => ['name' => '最后更新人', 'width' => 15],
                'updated_at' => ['name' => '最后更新时间', 'width' => 25],
            ];

            return Excel::download(new ExcelExportStyle($list, $header, $title, $dataTransformCallback), $title . date('YmdHis') . '.xls');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    // 当前会议下的所有餐饮信息
    public function eventDines(String $event_id)
    {
        $dines = Dine::where('event_id', $event_id)->orderBy('dine_date', 'asc')->orderBy('time_type', 'asc')->get();
        return SUCCESS_RESPONSE_ARRAY($dines);
    }

    /**
     * 当前会议下的所有餐饮信息，当前参会人已选的就餐为选中状态
     */
    public function eventAttendeeDines(string $attendee_id)
    {
        $event_id = Attendee::where('id', $attendee_id)->value('event_id');

        // 当前会议下的所有餐饮信息
        $dines = Dine::where('event_id', $event_id)->orderBy('dine_date', 'asc')->orderBy('time_type', 'asc')->get();
        // 当前参会人已选择的餐饮信息
        $attendee_dines = AttendeeDine::where('attendee_id', $attendee_id)->get();
        // 遍历当前会议下的所有餐饮信息，判断当前参会人是否已选择
        $dines->each(function ($item) use ($attendee_dines) {
            $item->is_selected = $attendee_dines->contains('dine_id', $item->id);
        });

        return SUCCESS_RESPONSE_ARRAY($dines);
    }

    /**
     * 当前参会人的就餐信息
     */
    public function attendeeDines(string $id)
    {
        // 当前参会人已选择的餐饮信息
        $attendee_dines = AttendeeDine::join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
            ->select('attendee_dines.*', 'dines.dine_date', 'dines.time_type', 'dines.type', 'dines.location', 'dines.identity', 'dines.specific_time')
            ->where('attendee_id', $id)
            ->orderBy('dine_date', 'asc')
            ->orderBy('time_type', 'asc')
            ->get();

        return SUCCESS_RESPONSE_ARRAY($attendee_dines);
    }



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // 删除
        $attendee_dine = AttendeeDine::find($id);
        if (!$attendee_dine) {
            return FAIL_RESPONSE_ARRAY('就餐信息不存在');
        }
        $attendee_dine->delete();
        return SUCCESS_RESPONSE_ARRAY("删除成功");
    }

    // 设置参会人餐饮信息
    public function setAttendeeDine(Request $request, string $attendee_id, AttendeeDineRepository $repository)
    {
        return $repository->setAttendeeDine($request, $attendee_id);
    }

    // 批量设置参会人餐饮信息
    public function setMoreAttendeeDine(Request $request, AttendeeDineRepository $repository)
    {
        return $repository->setMoreAttendeeDine($request);
    }

    // 修改是否需要就餐
    public function setIsNeedDine(Request $request, string $id)
    {
        $attendeeDine = AttendeeDine::find($id);
        if (!$attendeeDine) {
            return FAIL_RESPONSE_ARRAY('就餐信息不存在');
        }
        $attendeeDine->is_need_dine = $attendeeDine->is_need_dine == 1 ? 2 : 1;
        $attendeeDine->updater = $request->user()->real_name;
        $attendeeDine->update();
        return SUCCESS_RESPONSE_ARRAY("修改成功");
    }

    // 批量修改是否需要就餐
    public function setMoreIsNeedDine(Request $request)
    {
        $attendeeDineIds = $request->input('attendeeDineIds');
        $is_need_dine = $request->input('is_need_dine');
        if (empty($attendeeDineIds)) {
            return FAIL_RESPONSE_ARRAY('未选择就餐信息');
        }
        // 循环$dineIds，修改是否需要就餐
        foreach ($attendeeDineIds as $attendeeDineId) {
            $attendeeDine = AttendeeDine::find($attendeeDineId);
            if (!$attendeeDine) {
                return FAIL_RESPONSE_ARRAY('就餐信息不存在');
            }
            $attendeeDine->is_need_dine = $is_need_dine;
            $attendeeDine->updater = $request->user()->real_name;
            $attendeeDine->update();
        }

        return SUCCESS_RESPONSE_ARRAY("批量设置成功");
    }
}
