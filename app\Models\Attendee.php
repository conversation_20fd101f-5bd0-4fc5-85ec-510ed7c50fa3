<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

/**
 *
 *
 * @property int $id
 * @property int $event_id 会议id
 * @property int $member_id 小程序用户id
 * @property int $registrant_id 报名人id
 * @property string $name 姓名
 * @property string $phone 电话
 * @property int $gender 性别, 1:男 2:女 3:未知
 * @property int $identity 身份类别（和ieic后台保持一致）
 * @property string $organization 单位名称
 * @property string $position 职称职务
 * @property int $status 参会状态：1:未参会 2:已参会 3:待定
 * @property int $check_in_status 签到状态：1:已签到 2:未签到
 * @property int $user_id 对接人id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Event|null $event
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Forum> $forums
 * @property-read int|null $forums_count
 * @property-read \App\Models\Registrant|null $registrant
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee query()
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereCheckInStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereIdentity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereOrganization($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereRegistrantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Attendee whereUserId($value)
 * @mixin \Eloquent
 */
class Attendee extends Model
{
    use HasFactory, PaginationTrait, Notifiable;

    protected $guarded = [];

    protected $casts = [
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 报名状态：1:已报名 2:已取消 3:待确认 4驳回
    public static $status_ok = 1;
    public static $status_cancel = 2;
    public static $status_audit = 3;
    public static $status_reject = 4;

    //参会人可以报名多个论坛
    public function forums()
    {

        // 临时调试代码
//        \Log::error('Attendee id: ' . $this->id);
//        \Log::error('Attendee event_id: ' . $this->event_id);
        return $this->belongsToMany(Forum::class, 'attendee_forums', 'attendee_id', 'forum_id')
            ->withPivot('id', 'audit_status', 'audit_remark', 'intention', 'type');
//            ->wherePivot('event_id', $this->event_id);
    }

    //获取参会人的会议信息 一对一
    public function event()
    {
        return $this->hasOne(Event::class, 'id', 'event_id');
    }

    //参会人属于报名人
    public function registrant()
    {
        return $this->belongsTo(Registrant::class, 'registrant_id');
    }

    //对应多个attendeeForums
    public function attendeeForums()
    {
        return $this->hasMany(AttendeeForums::class, 'attendee_id');
    }

    //对接人一对一
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    //渠道一对一
    public function channel()
    {
        return $this->hasOne(Channel::class, 'id', 'channel_id');
    }

    // 对应多个消息
    public function messages()
    {
        return $this->hasMany(Message::class, 'attendee_id');
    }
    // 最后一条跟进消息
    public function lastFollowMessage()
    {
        return $this->hasOne(Message::class, 'attendee_id')
            ->whereIn('source', [Message::$source_user_submit, Message::$source_other_submit])->latest();
    }

    // 对应一个采访
    public function attendeeInterview()
    {
        return $this->hasOne(AttendeeInterview::class, 'attendee_id');
    }

    // 对应多个就餐
    public function attendeeDines()
    {
//        return $this->hasMany(AttendeeDine::class, 'attendee_id');
        return $this->belongsToMany(Dine::class, 'attendee_dines', 'attendee_id', 'dine_id')
            ->withPivot('id', 'check_in_status', 'is_need_dine');
    }
    // 是否就餐
    public function isDine()
    {
        return $this->hasOne(AttendeeDine::class, 'attendee_id')->where('is_need_dine', 1)->latest();
    }

    // 对应多个酒店
    public function attendeeHotels()
    {
        return $this->hasMany(AttendeeHotel::class, 'attendee_id');
//        return $this->belongsToMany(Hotel::class, 'attendee_hotels', 'attendee_id', 'hotel_id')
//            ->withPivot('id', 'type', 'is_need_hotel', 'check_in_date', 'check_in_days', 'booking_room_number', 'room_type', 'remark', 'is_need_pay', 'order_id');
    }
    // 对应多个住宿订单
    public function attendeeHotelOrders()
    {
        return $this->belongsToMany(Order::class, 'attendee_hotels', 'attendee_id', 'order_id');
    }

    // 是否住宿
    public function isHotel()
    {
        return $this->hasOne(AttendeeHotel::class, 'attendee_id')->where('is_need_hotel', 1)->latest();
    }
    // 不需要，自行办理入住
    public function notHotel()
    {
        return $this->hasOne(AttendeeHotel::class, 'attendee_id')
            ->where('is_need_hotel', 2)->where('type', 3)->latest();
    }

    // 对应多个接站
    public function attendeePickUps()
    {
        return $this->hasMany(AttendeePickUp::class, 'attendee_id');
    }
    // 是否接站
    public function isPickUp()
    {
        return $this->hasOne(AttendeePickUp::class, 'attendee_id')->where('is_need_pick_up', 1)->latest();
    }

    // 对应多个送站
    public function attendeeDropOffs()
    {
        return $this->hasMany(AttendeeDropOff::class, 'attendee_id');
    }
    // 是否送站
    public function isDropOff()
    {
        return $this->hasOne(AttendeeDropOff::class, 'attendee_id')->where('is_need_drop_off', 1)->latest();
    }


    public static function convertIdentityType($status)
    {
        switch ($status) {
            case 1:
                return '公办学校';
            case 2:
                return '民办学校';
            case 3:
                return '国际化学校';
            case 4:
                return '职业教育';
            case 5:
                return '教育主管部门';
            case 6:
                return '演讲嘉宾';
            case 7:
                return '参展学校';
            case 8:
                return '晚宴';
            case 9:
                return '国际化学校1';
            case 10:
                return 'VEIC-职教社';
            case 11:
                return 'VEIC-民协';
            case 12:
                return 'VEIC-易班网';
            case 13:
                return 'VEIC-远播教育';
            case 14:
                return '民办高中研讨会';
            case 15:
                return '民办高中校长高级研究班';
            default:
                return '';
        }
    }

    // 定义状态转换方法
    public static function convertOrganizationType($status)
    {
        switch ($status) {
            case 1:
                return '省教育考试院';
            case 2:
                return '市区教育学院';
            case 3:
                return '市区招办';
            case 4:
                return '高校招办';
            case 5:
                return '公办高中';
            case 6:
                return '教育局';
            case 7:
                return '大学非招办';
            case 8:
                return '民办学校小初高';
            case 9:
                return '协会';
            case 10:
                return '公办初中';
            case 11:
                return '公办小学';
            case 12:
                return '教育机构';
            case 13:
                return '非教育类机构';
            case 14:
                return '其它';
            default:
                return '';
        }
    }

    public static function convertStatus($status)
    {
        switch ($status) {
            case 1:
                return '已报名';
            case 2:
                return '已取消';
            case 3:
                return '待审核';
            case 4:
                return '已驳回';
            default:
                return '待确认';
        }
    }

    public static function convertAuditStatus($status)
    {
        switch ($status) {
            case 1:
                return '审核通过';
            case 4:
                return '审核驳回';
            default:
                return '待确认';
        }
    }
}
