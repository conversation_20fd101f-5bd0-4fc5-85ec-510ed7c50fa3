<?php

namespace App\Http\Middleware;

use App\Models\Website\VisitStats;
use Closure;

class VisitStatsMiddleware
{
    public function handle($request, Closure $next)
    {
        $visitStat = new VisitStats();
        $visitStat->ip_address = $request->ip();
        $visitStat->url = $request->url();
        $visitStat->visited_at = now();
        $visitStat->save();
        return $next($request);
    }

}
