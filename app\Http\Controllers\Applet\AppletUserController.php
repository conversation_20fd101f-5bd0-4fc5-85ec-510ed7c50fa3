<?php

namespace App\Http\Controllers\Applet;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\Member;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AppletUserController extends Controller
{
    //
    /**
     *  获取用户信息
     * @param Request $request
     * @return array
     */
    public function getUserInfo(Request $request)
    {
        $member = Member::with(['user:id,phone'])->find($request->user()->id);
        $member->is_business = 0;
        if ($member->user) {
            $member->is_business = 1; // 是商务
        }
        return SUCCESS_RESPONSE_ARRAY($member);
    }

    /**
     *  绑定手机号
     * @param Request $request
     * @return array
     */
    public function bindPhoneValidate(Request $request)
    {
        $user = $request->user();
        $phone = $request->phone;
        $openid = $user->open_id;


        // 第一步检查数据合法性
        $rst = Validator::make(compact('phone', 'openid'), [
            'phone' => 'required|regex:/^1[3456789]\d{9}$/',
            'openid' => 'required',
        ]);
        if (!$rst->passes()) {
            return FAIL_RESPONSE_ARRAY($rst->errors()->first());
        }

        // 第二步 校验当前openid是否已经绑定过手机号,如果绑定过,提示覆盖
        $member = Member::where([
            ['open_id', $openid],
            ['phone', '!=', ''],
            ['phone', '!=', $phone],
        ])->first();
        if (!empty($member)) {
            return ['code' => 1, 'message' => "该微信号已经绑定过手机号:{$member->phone},继续则覆盖"];
        }
        return SUCCESS_ARRAY;
    }

    public function bindPhone(Request $request)
    {
        $request->validate([
            'phone' => 'required|regex:/^1[3-9]\d{9}$/', // 验证手机号码格式
        ]);

        try {
            // 使用事务确保数据库操作的一致性
            DB::beginTransaction();
            $user = $request->user();
            $phone = $request->phone;
            $user->phone = $phone;
            $user->save();
            //todo 查询此手机号是否有参会人，有的话更新参会人中的member_id
            Attendee::query()->where('phone', $phone)->update(['member_id' => $user->id]);
            // 提交事务
            DB::commit();
            return SUCCESS_RESPONSE_ARRAY($request->user());
        } catch (\Exception $e) {
            // 发生异常时回滚事务并返回失败响应
            DB::rollBack();
            return FAIL_RESPONSE_ARRAY("绑定失败");
        }
    }
}
