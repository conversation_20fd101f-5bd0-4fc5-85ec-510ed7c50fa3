<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Models\AttendeeHotel;
use App\Models\Order;
use App\Models\RefundOrder;
use App\Models\User;
use App\Repositories\MiniAppRepository;
use App\Repositories\OrderRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, OrderRepository $orderRepository)
    {
        // 查询订单列表
        $query = $orderRepository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt' ));
    }

    //小程序端我的订单列表getMemberOrders
    public function getMemberOrders(string $type,Request $request)
    {
        $member_id = $request->user()->id;
        //查询pay_status不等于1的
        if ($type == '2') {
            $list = Order::query()
                ->leftJoin('members', 'members.id', '=', 'orders.member_id')
                ->leftJoin('attendees', 'attendees.id', '=', 'orders.attendee_id')
                ->leftJoin('events', 'attendees.event_id', '=', 'events.id')
                ->leftJoin(DB::raw('(SELECT order_id, SUM(amount) as total_invoice_amount FROM order_invoices WHERE status IN (1, 2) GROUP BY order_id) as invoices'), 'orders.id', '=', 'invoices.order_id')
                ->select(
                    'orders.*',
                    'members.phone as member_phone',
                    'events.id as event_id',
                    'events.title as event_title',
                    'events.short_title as event_short_title',
                    'events.cover as cover',
                    'attendees.name as attendee_name',
                    'attendees.organization as organization',
                    DB::raw('IFNULL(invoices.total_invoice_amount, 0) as total_invoice_amount'),
                    DB::raw('CASE
                    WHEN IFNULL(invoices.total_invoice_amount, 0) = orders.amount THEN 2
                    WHEN IFNULL(invoices.total_invoice_amount, 0) < orders.amount THEN 1
                    ELSE 0
                END as invoice_status')
                )
                //查询pay_status 2或者3的
                ->where('orders.member_id', $member_id)->whereIn('orders.pay_status', [2, 3, 5])->orderBy('orders.id', 'desc')->get();
            //循环list给cover添加前缀config('filesystems.disks.sftp.domain')
            $list->map(function ($item) {
                $item->cover = config('filesystems.disks.sftp.domain') . $item->cover;
            });
            return SUCCESS_RESPONSE_ARRAY($list);
        } elseif ($type == '3') {
            $list = Order::query()
                ->leftJoin('refund_orders', 'orders.id', '=', 'refund_orders.order_id')
                ->leftJoin('members', 'members.id', '=', 'orders.member_id')
                ->leftJoin('attendees', 'attendees.id', '=', 'orders.attendee_id')
                ->leftJoin('events', 'attendees.event_id', '=', 'events.id')
                ->select(
                    'refund_orders.*',
//                    'orders.*',
                    'orders.fee_info',
                    'orders.pay_type',
                    'orders.attendee_id',
                    'orders.order_no',
                    'orders.pay_certificate',
                    'members.phone as member_phone',
                    'events.id as event_id',
                    'events.title as event_title',
                    'events.short_title as event_short_title',
                    'events.cover as cover',
                    'attendees.name as attendee_name',
                    'attendees.organization as organization'
                )
                ->where('refund_orders.member_id', $member_id)->orderBy('refund_orders.id', 'desc')->get();
            //循环list给cover添加前缀config('filesystems.disks.sftp.domain')
            $list->map(function ($item) {
                $item->cover = config('filesystems.disks.sftp.domain') . $item->cover;
            });
            return SUCCESS_RESPONSE_ARRAY($list);
        }
    }

    //退费订单列表
    public function refundList(Request $request, OrderRepository $orderRepository)
    {
        // 查询订单列表
        $query = $orderRepository->refundListBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //保存新建的订单
        $data = filterRequestData('orders');
        //获取hotel_id
        $hotel_id = $request->input('hotel_id');
        //订单号
        $data['order_no'] = uniqid();
        //订单类型
        $data['type'] = 1;
        //下单IP
        $data['ip'] = $request->ip();
        //支付方式
        $data['pay_type'] = 1;
        //支付状态
        $data['pay_status'] = 1;
        $data['creator'] = $request->user()->real_name;

        // 获取pay_certificate_arr数组并转换为逗号分隔的字符串
        $pay_certificate_arr = $request->input('pay_certificate_arr', []);
        $data['pay_certificate'] = implode(',', $pay_certificate_arr);


        $order = Order::forceCreate($data);
        //获取$order的id
        $order_id = $order->id;

        //住宿服务表存订单信息
        $attendeeHotel = AttendeeHotel::find($hotel_id);
        $attendeeHotel->order_id = $order_id;
        $attendeeHotel->save();

        return SUCCESS_RESPONSE_ARRAY($order);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
        $order = Order::find($id);
        return SUCCESS_RESPONSE_ARRAY($order);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
        $order = Order::find($id);
        $order->pay_status = $request->input('pay_status');
        $order->updater = $request->user()->real_name;
        $order->pay_time = date('Y-m-d H:i:s');
        $order->save();
        //推送微信通知
        //根据id查询报名人
        /*$user = User::find($order->member_id);
        $template_id = 'Tz1trE_39Bhno40Ov_hKGrnYyWP8Say-vn4TC0Nqv8U';
        $formattedData = [
            "character_string2" => [
                "value" => $order->order_no,
            ],
            "amount3" => [
                "value" => $order->amount,
            ],
            "time8" => [
                "value" => $order->pay_time,
            ],
            "thing9" => [
                "value" => '支付成功'
            ],
            "thing14" => [
                "value" => '转账支付',
            ]
        ];
        $open_id = $user->open_id;
        $page = 'pages/customer/mine/mine';
        MiniAppRepository::sendMiniAppMessage($template_id,$formattedData,$open_id,$page);*/
        return SUCCESS_RESPONSE_ARRAY($order);
    }

    //confirmRefund
    public function confirmRefund(Request $request, string $id)
    {
        //获取订单信息
        $refundOrder = RefundOrder::find($id);
        $refundOrder->status = $request->input('status');
        $refundOrder->updater = $request->user()->real_name;
        $refundOrder->save();
        return SUCCESS_RESPONSE_ARRAY($refundOrder);
    }

    //更新凭证
    public function updatePayCertificate(Request $request, string $id)
    {
        //获取pay_certificate_arr数组并转换为逗号分隔的字符串
        $pay_certificate_arr = $request->input('pay_certificate_arr', []);
        $pay_certificate = implode(',', $pay_certificate_arr);

        $order = Order::find($id);
        $order->pay_certificate = $pay_certificate;
        $order->save();
        return SUCCESS_RESPONSE_ARRAY($order);
    }

    //修改退款信息updateRefund
    public function updateRefund(Request $request, string $id)
    {
        //获取退款信息
        $refundOrder = RefundOrder::find($id);
        $refundOrder->amount = $request->input('amount');
        $refundOrder->remark = $request->input('remark');
        $refundOrder->updater = $request->user()->real_name;
        $refundOrder->save();
        return SUCCESS_RESPONSE_ARRAY($refundOrder);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
