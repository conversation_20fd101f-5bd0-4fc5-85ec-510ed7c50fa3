<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人id');
            //付款人id
            $table->unsignedInteger('member_id')->default(0)->comment('付款人id');
            //订单号
            $table->string('order_no')->nullable(false)->default('')->comment('订单号');
            //金额
            $table->decimal('amount', 10, 2)->default(0.00)->comment('金额');
            //订单类型
            $table->tinyInteger('type')->default(1)->comment('订单类型，1:住宿订单 2:报名订单');
            //下单ip
            $table->string('ip')->nullable()->default('')->comment('下单ip');
            //支付方式
            $table->tinyInteger('pay_type')->default(1)->comment('支付方式，1:微信 2:转账支付');
            //转账支付凭证pay_certificate
            $table->string('pay_certificate')->nullable()->default('')->comment('转账支付凭证');
            //状态
            $table->tinyInteger('pay_status')->default(1)->comment('支付状态，1:未支付 2:已支付，3.待核实，4:已退款, 5已收款');
            //费用信息
            $table->string('fee_info')->nullable()->default('')->comment('费用信息');
            //支付时间
            $table->timestamp('pay_time')->nullable()->comment('支付时间');
            $table->string('creator', 20)->nullable()->default('')->comment('添加人');
            $table->string('updater', 20)->nullable()->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `orders` comment '订单表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
