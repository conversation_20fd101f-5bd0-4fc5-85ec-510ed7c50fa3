<?php

namespace App\YuanBo\Sms;

use App\YuanBo\Contracts\Sms\SmsSender;
use Illuminate\Support\Facades\Http;


class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> implements SmsSender
{
    function mergeRequestArr($data = []): array
    {
        return [
            'account' => env('JIANZHOU_SMS_ACCOUNT'),
            'password' => env('JIANZHOU_SMS_PASSWORD'),
            ...$data
        ];
    }

    public function balance()
    {
    }

    public function report()
    {
    }

    public function batchSend(array $mobiles = [], $message = '', $sign = null, $options = [])
    {
        $postData = [
            'destmobile' => implode(';', $mobiles),
            'msgText' => $sign . $message,
            ...$options,
        ];
        $response = Http::withoutVerifying()->asForm()->post(env('JIANZHOU_SMS_URL'), $this->mergeRequestArr($postData));
        return $response->json();
    }

    public function singleSend($mobile, $message = '', $sign = null, $options = [])
    {
        $postData = [
            'destmobile' => $mobile,
            'msgText' => $sign . $message,
            ...$options,
        ];
        $response = Http::withoutVerifying()->asForm()->post(env('JIANZHOU_SMS_URL'), $this->mergeRequestArr($postData));
        return $response->json();
    }

    public function templateSend($mobile, $templateCode, $templateParam, $signName)
    {

    }
}
