<?php

namespace App\YuanBo\Sms;

use App\YuanBo\Contracts\Sms\SmsSender;
use Illuminate\Support\Facades\Http;

class AliSender implements SmsSender
{
    // 文档地址： https://help.aliyun.com/document_detail/101343.html
    // API概览 : https://help.aliyun.com/document_detail/102715.html
    // 配置参数start=====================================
    protected $Format = 'JSON';
    protected $Version = "2017-05-25";
    protected $SignatureMethod = 'HMAC-SHA1';
    protected $SignatureVersion = '1.0';
    protected $RegionId = 'cn-hangzhou';

    protected $Method = 'POST';


    public function batchSend(array $mobiles = [], $message = '', $sign = null, $options = [])
    {
        // TODO: Implement batchSend() method.
    }

    public function singleSend($mobile, $message = '', $sign = null, $options = [])
    {
        // TODO: Implement singleSend() method.
    }

    public function templateSend($mobile, $templateCode, $templateParam = [], $signName = '远播国际教育')
    {
        $postData = [
            'Action' => 'SendSms',
            'PhoneNumbers' => $mobile,
            'SignName' => $signName,
            'TemplateCode' => $templateCode,
            'TemplateParam' => !$templateParam ? '' : collect($templateParam)->toJson(JSON_UNESCAPED_UNICODE),
        ];

        $apiParams = array_merge($this->commonParameters(), $postData);
        $apiParams["Signature"] = $this->computeSignature($apiParams);

        $response = Http::withoutVerifying()->asForm()->post(env('ALI_SMS_URL'), $apiParams);
        return $response->json();
    }

    public function balance()
    {
        // TODO: Implement balance() method.
    }

    public function report()
    {
        // TODO: Implement report() method.
    }


    // 公共方法


    private function commonParameters(): array
    {
        return [
            'Format' => $this->Format,
            'Version' => $this->Version,
            'AccessKeyId' => env('ALI_SMS_ACCESS_KEY_ID'),
            'SignatureMethod' => $this->SignatureMethod,
            'Timestamp' => gmdate("Y-m-d\TH:i:s\Z"),
            'SignatureVersion' => $this->SignatureVersion,
            'SignatureNonce' => md5(uniqid(mt_rand(), true)),
            'RegionId' => $this->RegionId,
        ];
    }

    private function computeSignature(array $parameters): string
    {
        ksort($parameters);
        $canonicalizedQueryString = '';
        foreach ($parameters as $key => $value) {
            $canonicalizedQueryString .= '&' . $this->percentEncode($key) . '=' . $this->percentEncode($value);
        }
        $stringToSign = $this->Method . '&%2F&' . $this->percentencode(substr($canonicalizedQueryString, 1));
        $signature = $this->signString($stringToSign, env('ALI_SMS_ACCESS_KEY_SECRET') . "&");
        return $signature;
    }

    private function signString(string $source, string $accessSecret): string
    {
        return base64_encode(hash_hmac('sha1', $source, $accessSecret, true));
    }

    private function percentEncode(string $str)
    {
        $res = urlencode($str);
        $res = preg_replace('/\+/', '%20', $res);
        $res = preg_replace('/\*/', '%2A', $res);
        $res = preg_replace('/%7E/', '~', $res);
        return $res;
    }
}
