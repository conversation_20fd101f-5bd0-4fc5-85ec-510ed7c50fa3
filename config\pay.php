<?php

return [
    'wechat' => [
        'app_id' => 'wx3e8e76dcbe626656',
        'secret' => 'adef48e6d4c9e3a4840b0aeda2453545',
        'mch_id' => '1598426831',
        // 商户证书
        'private_key' => storage_path('/certs/apiclient_key.pem'),
        'certificate' => storage_path('/certs/apiclient_cert.pem'),

        // v3 API 秘钥
        'secret_key' => '7Xh2Quk2q2YxblCE2CDAud125WKDc217',

        // v2 API 秘钥
        'v2_secret_key' => 'Ov23r3D0VgSISdlVnGWgFP0DlZ3RPO5j',

        /**
         * 接口请求相关配置，超时时间等，具体可用参数请参考：
         * https://github.com/symfony/symfony/blob/5.3/src/Symfony/Contracts/HttpClient/HttpClientInterface.php
         */
        'http' => [
            'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
            'timeout' => 5.0,
            // 'base_uri' => 'https://api.mch.weixin.qq.com/', // 如果你在国外想要覆盖默认的 url 的时候才使用，根据不同的模块配置不同的 uri
        ],
    ],
];
