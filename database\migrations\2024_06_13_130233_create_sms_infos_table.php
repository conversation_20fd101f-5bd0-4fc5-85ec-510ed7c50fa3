<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_send_logs', function (Blueprint $table) {
            $table->id();
            $table->string('sign')->nullable()->default('')->comment('签名');
            $table->string('mobile')->nullable()->default('')->comment('手机号');
            $table->string('message')->nullable()->default('')->comment('短信内容');
            $table->string('task_id')->nullable()->default('')->comment('建周任务id');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_send_logs');
    }
};
