<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kpis', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->comment('会议ID');
            $table->unsignedInteger('forum_id')->default(0)->comment('论坛ID');
            $table->unsignedInteger('department_one_id')->nullable()->comment('一级部门id');
            $table->unsignedInteger('department_two_id')->nullable()->comment('二级部门id');
            $table->integer('invitation_kpi')->default(0)->comment('邀约指标');
            $table->integer('visit_kpi')->default(0)->comment('到访指标');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `kpis` comment '指标管理表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kpis');
    }
};
