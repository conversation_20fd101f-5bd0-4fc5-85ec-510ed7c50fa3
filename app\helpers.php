<?php

// 如果helpers文件不生效,执行命令: composer dump-autoload

use App\Http\Controllers\Applet\MiniAppController;
use App\Models\SmsSendLog;
use App\Repositories\NoticeRepository;
use App\YuanBo\Sms\JianZhouSender;
use App\YuanBo\Support\Facades\SmsManagerFacade;
use EasyWeChat\MiniApp\AccessToken;
use EasyWeChat\MiniApp\Application;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;


// 自定义错误响应码
const CUSTOM_ERROR_CODE = 10;
const NUMBER_LIMIT_CODE = 3;
const REPEAT_DATA_CODE = 4;
const SUCCESS_ARRAY = ['code' => 0, 'message' => '操作成功'];
const AUDIT_ARRAY = ['code' => 2, 'message' => '操作成功，待审核'];

const LOGIN_FAIL_ARRAY = ['code' => 1, 'message' => '用户名或者密码错误'];
function SUCCESS_RESPONSE_ARRAY($data = []): array
{
    return array_merge(SUCCESS_ARRAY, [
        'data' => $data
    ]);
}
function AUDIT_RESPONSE_ARRAY($data = []): array
{
    return array_merge(AUDIT_ARRAY, [
        'data' => $data
    ]);
}

function FAIL_RESPONSE_ARRAY($message = '自定义错误信息'): array
{
    return [
        'code' => CUSTOM_ERROR_CODE,
        'message' => $message
    ];
}
function ERROR_RESPONSE_ARRAY($message = '自定义错误信息',$code = CUSTOM_ERROR_CODE): array
{
    return [
        'code' => $code,
        'message' => $message
    ];
}

function filterRequestData($tableName, $connection = 'mysql'): array
{
    $fieldArray = \Illuminate\Support\Facades\Schema::connection($connection)->getColumnListing($tableName);
    $fieldArray = array_filter($fieldArray, function ($item) {
        return $item !== 'id';
    });
    $reqArray = request()->all();
    // 取数据库和请求数据的交集
    return array_intersect_key($reqArray, array_flip($fieldArray));
}

function searchBetweenDate($dates): array
{
    if (!$dates)
        return [];
    return [\Carbon\Carbon::parse($dates[0])->startOfDay(), \Carbon\Carbon::parse($dates[1])->endOfDay()];
}

// 删除字符串最后一个字符
function removeLastChar($string) {
    if (!empty($string)) {
        return substr($string, 0, -1);
    }
    return $string; // 如果字符串为空，则返回原字符串
}

// 加密
function encrypt1($string): string
{
    return Crypt::encryptString($string);
}

// 解密
function decrypt1($string): string
{
    return Crypt::decryptString($string);
}


// 数字字符串加密
function encryptNumber($string): string
{
    // 将字符串中的1、2、3、4、5、6、7、8、9、0替换成J、I、H、G、F、E、D、C、B、A
    return str_replace(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'], ['J', 'I', 'H', 'G', 'F', 'E', 'D', 'C', 'B', 'A'], $string);
}

// 数字字符串解密
function decryptNumber($string): string
{
    // 将字符串中的J、I、H、G、F、E、D、C、B、A替换成1、2、3、4、5、6、7、8、9、0
    return str_replace(['J', 'I', 'H', 'G', 'F', 'E', 'D', 'C', 'B', 'A'], ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'], $string);
}

function encryptDecrypt($decrypt, $string) {
    if ($decrypt) {
        return base64_decode($string);
    } else {
        return base64_encode($string);
    }
}

// 凯撒加密函数，支持字母和数字
function caesar_encrypt($data, $key) {
    $shift = strlen($key) % 26;  // 根据密钥长度确定移位量
    $output = '';

    for ($i = 0; $i < strlen($data); $i++) {
        $char = $data[$i];

        if (ctype_lower($char)) {
            // 对小写字母进行移位加密
            $output .= chr(((ord($char) - 97 + $shift) % 26) + 97);
        } elseif (ctype_digit($char)) {
            // 对数字进行移位加密
            $output .= (string)((($char + $shift) % 10));
        } else {
            $output .= $char;  // 保留其他字符（或根据需要处理）
        }
    }

    return $output;
}

// 凯撒解密函数，支持字母和数字
function caesar_decrypt($data, $key) {
    $shift = strlen($key) % 26;  // 根据密钥长度确定移位量
    $output = '';

    for ($i = 0; $i < strlen($data); $i++) {
        $char = $data[$i];

        if (ctype_lower($char)) {
            // 对小写字母进行移位解密
            $output .= chr(((ord($char) - 97 - $shift + 26) % 26) + 97);
        } elseif (ctype_digit($char)) {
            // 对数字进行移位解密
            $output .= (string)(((($char - $shift + 10) % 10)));
        } else {
            $output .= $char;  // 保留其他字符（或根据需要处理）
        }
    }

    return $output;
}

// 生成二维码
function createQrCode($string): string
{
    $qrCode = QrCode::create($string)
        ->setEncoding(new Encoding('ISO-8859-1'))
//        ->setErrorCorrectionLevel(new ErrorCorrectionLevelLow())
//        ->setSize(300)
//        ->setMargin(10)
//        ->setRoundBlockSizeMode(new RoundBlockSizeModeMargin())
//        ->setForegroundColor(new Color(0, 0, 0))
//        ->setBackgroundColor(new Color(255, 255, 255))
    ;
    $result = (new PngWriter)->write($qrCode);


//    // 方法一：将二维码图片保存到本地服务器
//    $dir_url =  storage_path('app/public/' . date('Y-m-d') );
//    if (!File::exists($dir_url)){
//        File::makeDirectory($dir_url, 0777, true, true);
//    }
//    $file_name = time() . 'qrcode.png';
//    $file_path = storage_path('app/public/' . date('Y-m-d'). '/' . $file_name);
//    $result->saveToFile($file_path);
//    return env('APP_URL') . '/storage/' . date('Y-m-d'). '/' . $file_name;

    // 方法二：返回 base64 格式的图片
    return $result->getDataUri();

//    // 方法三：直接输出二维码
//    header( 'Content-Type: ' . $result -> getMimeType ());
//    echo $result->getString();
//    return "";
}

// 验证手机号是否符合规则
function checkMobile($mobile): bool
{
    // 去除$mobile前后的空格
    $mobile = trim($mobile);

    if (!$mobile) {
        return false;
    }

    // 检查是否是有效的手机号
    return preg_match('/^1[3456789]\d{9}$/', $mobile);
}

// 发送单条短信通知
function singleSendJianzhouSms($sign, $mobile, $message)
{
    if (checkMobile($mobile)){
        $rst = SmsManagerFacade::setSender(JianZhouSender::class)->singleSend($mobile, $message, $sign);
        // 记录发送日志
        NoticeRepository::insertSmsSendLog($sign, [$mobile], $message, $rst);
    } else {
        Log::error('手机号码有误，未发送短信，手机号码: '. $mobile);
    }
}

// 批量发送短信通知
function batchSendJianzhouSms($sign, $mobiles, $message)
{
    // 去重
    $mobiles = array_unique($mobiles);
    // 循环验证手机号是否正确，不正确的移除
    $mobiles = array_filter($mobiles, function ($mobile) {
        return checkMobile($mobile);
    });
    if (count($mobiles) > 1000) {
        return ERROR_RESPONSE_ARRAY('最多只能发送1000条短信', NUMBER_LIMIT_CODE);
    }
    $rst = SmsManagerFacade::setSender(JianZhouSender::class)->batchSend($mobiles, $message, $sign);
    // 记录发送日志
    NoticeRepository::insertSmsSendLog($sign, $mobiles, $message, $rst);
}


function replaceParams($content, $params) {
    foreach ($params as $key => $value) {
        $content = str_replace("$key", $value, $content);
    }
    return $content;
}


// 获取小程序路径
function getAppletUrlLink(mixed $wechat_path): mixed
{
    $app = new Application(MiniAppController::APPLET_CONFIG);

    $app->getAccessToken()->refresh();
//    $accessToken = new AccessToken(
//        appId: $app->getAccount()->getAppId(),
//        secret: $app->getAccount()->getSecret(),
//        cache: $app->getCache(),
//        httpClient: $app->getHttpClient(),
//    );
//
//    $app->setAccessToken($accessToken);
    $data = [
        'expire_type' => 1,
        'expire_interval' => 30,
    ];
    if (!empty($wechat_path)){
        $data = [
            'path' => $wechat_path,
            'expire_type' => 1,
            'expire_interval' => 30,
        ];
    }
    $response = $app->getClient()->postJson('wxa/generate_urllink', $data)->toArray(false);
    Log::error($response);
    //获取$response的url_link字段
    $url_link = $response['url_link'];
    return $url_link;
}


/**
 * 隐藏手机号中间四位数
 */
function hidePhoneNumber($phoneNumber)
{
    if (empty($phoneNumber)) {
        return '';
    }
    return substr($phoneNumber, 0, 3) . '****' . substr($phoneNumber, -4);
}
