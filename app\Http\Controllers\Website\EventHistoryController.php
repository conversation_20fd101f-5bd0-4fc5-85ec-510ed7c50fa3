<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Website\EventHistory;
use Illuminate\Http\Request;

//往届回顾
class EventHistoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //查询所有会议回顾
        $list = EventHistory::query()->orderBy('year', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY($list);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //新增会议回顾
        $history = new EventHistory();
        $history->title = $request->input('title');
        $history->year = $request->input('year');
        $history->content = $request->input('content');
        $history->url = $request->input('url');
        $history->creator = $request->user()->real_name;
        $history->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $history = EventHistory::find($id);
        return SUCCESS_RESPONSE_ARRAY($history);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //更新会议回顾
        $history = EventHistory::find($id);
        $history->title = $request->input('title');
        $history->year = $request->input('year');
        $history->content = $request->input('content');
        $history->url = $request->input('url');
        $history->updater = $request->user()->real_name;
        $history->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //删除会议回顾
        $history = EventHistory::find($id);
        $history->delete();
    }
}
