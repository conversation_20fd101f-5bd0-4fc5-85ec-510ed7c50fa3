<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property-read \App\Models\Hotel|null $hotel
 * @method static \Illuminate\Database\Eloquent\Builder|HotelBooking newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HotelBooking newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HotelBooking query()
 * @mixin \Eloquent
 */
class HotelBooking extends Model
{
    use HasFactory, PaginationTrait;

    protected $casts = [
        'booking_date' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 预定信息属于酒店
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }
}
