<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;

class PermissionController extends Controller
{
    //

    public function dropPermissions(): array
    {
        //
        return SUCCESS_RESPONSE_ARRAY(Permission::select(['id', 'display_name'])->get());
    }

    // 用户所有权限
    public function userAllPermissions(Request $request)
    {
        $userId = $request->user()->id;
        $roleIds = $request->user()->roles()->pluck('id');
        $permissions = Permission::whereHas('roles', function ($query) use ($roleIds, $userId) {
            $query->whereIn('id', $roleIds)->whereHas('users', fn($query) => $query->where('id', $userId));
        })->pluck('name');
        return SUCCESS_RESPONSE_ARRAY($permissions);

    }

    function list(Request $request)
    {
        $displayName = $request->input('display_name');
        $name = $request->input('name');
        $query = Permission::when($displayName, fn($query) => $query->where('display_name', 'like', "%$displayName%"))
            ->when($name, fn($query) => $query->where('name', 'like', "%$name%"));
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));

    }

    function store(Request $request)
    {
        $data = filterRequestData('permissions');
        $rst = Permission::create($data);
        return SUCCESS_RESPONSE_ARRAY($rst->id);
    }

    function update(Request $request, $id)
    {
        $permission = Permission::find($id);
        $data = filterRequestData('permissions');
        $rst = $permission->fill($data)->save();
        return SUCCESS_RESPONSE_ARRAY($rst);

    }


}
