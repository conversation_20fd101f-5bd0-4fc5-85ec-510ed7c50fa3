<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/7
 * Time 9:20
 */

namespace App\Repositories;

use App\Models\Registrant;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class RegistrantExport implements FromCollection, WithHeadings, WithEvents
{
    protected $data;
    protected $mergeCells = [];

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $exportData = collect([]);
        $startRow = 3; // Start from the first row

        foreach ($this->data as $item) {
            $rowsPerAttendee = [];
            foreach ($item['attendees'] as $attendee) {
                $numForums = 0; // Reset numForums for each attendee
                if (!empty($attendee['forums'])) {
                    $numForums = count($attendee['forums']);
                }
                $rowsPerAttendee[] = $numForums > 0 ? $numForums : 1;
            }

            $numRows = array_sum($rowsPerAttendee)==0?1:array_sum($rowsPerAttendee); // 至少有一行，即使没有参会人也要有一行


            for ($i = 0; $i < $numRows; $i++) {

                // 获取第一个参会人的channel信息
                $firstAttendeeChannel = !empty($item['attendees']) && !empty($item['attendees'][0]['channel']) ? $item['attendees'][0]['channel'] : null;
                $firstAttendeeChannelName = $firstAttendeeChannel ? $firstAttendeeChannel['name'] : '';
                $firstAttendeeChannelDepartment = $firstAttendeeChannel && !empty($firstAttendeeChannel['department']) ?
                    (!empty($firstAttendeeChannel['department']['parent']) ? $firstAttendeeChannel['department']['parent']['name'] : $firstAttendeeChannel['department']['name']) : '';

                $row = [
                    '报名人编号' => $i === 0 ? $item['id'] : '', // 只在第一行显示报名人编号
                    '会议标题' => $i === 0 ? $item['event']->short_title : '', // 会议标题
                    '身份类别' => $i === 0 ? Registrant::converIdentityType($item['identity']):'', // 身份
                    '单位名称' => $i === 0 ? $item['organization'] : '', // 单位名称
                    '单位代码' => $i === 0 ? $item['organization_code'] : '', // 单位名称
                    '姓名' => $i === 0 ? $item['name'] : '', // 只在第一行显示姓名
                    '手机号' => $i === 0 ? $this->hidePhoneNumber($item['phone']) : '', // 只在第一行显示手机号
                    '邀约渠道' => $i === 0 ? $firstAttendeeChannelName : '', // 只在第一行显示手机号
                    '邀约渠道所属事业部' => $i === 0 ? $firstAttendeeChannelDepartment : '', // 只在第一行显示手机号
                    '报名时间'=>$i === 0 ? $item['attendees'][0]['created_at'] : '',
                    '参会人编号' => '',
                    '参会人姓名' => '',
                    '参会人身份类别' => '',
                    '参会人手机号' => '',
                    '论坛编号' => '',
                    '论坛名称' => '',
                ];

                // Distribute attendee data into the correct rows
                $currentForumIndex = 0;
                foreach ($rowsPerAttendee as $attendeeIndex => $count) {
                    if ($i < $count + ($currentForumIndex === 0 ? 0 : $currentForumIndex)) {
                        $attendee = $item['attendees'][$attendeeIndex];
                        $row['参会人编号'] = $attendee['id'];
                        $row['参会人姓名'] = $attendee['name'];
                        $row['参会人手机号'] = $this->hidePhoneNumber($attendee['phone']);
                        $row['参会人身份类别'] =  Registrant::converIdentityType($attendee['identity']);

                        // Add a check to ensure the calculated index is non-negative
                        $forumIndex = $i - $currentForumIndex;
                        if (!empty($attendee['forums']) && $forumIndex >= 0 && isset($attendee['forums'][$forumIndex])) {
                            $forum = $attendee['forums'][$forumIndex];
                            $row['论坛编号'] = $forum['id'];
                            $row['论坛名称'] = $forum['name'];
                        }
                        break;
                    }
                    $currentForumIndex += $count;
                }

                $exportData[] = $row;
            }
            // 记录需要合并的单元格
            $endRow = $startRow + $numRows-1;
            $this->mergeCells[] = "A{$startRow}:A{$endRow}";
            $this->mergeCells[] = "B{$startRow}:B{$endRow}";
            $this->mergeCells[] = "C{$startRow}:C{$endRow}";
            $this->mergeCells[] = "D{$startRow}:D{$endRow}";
            $this->mergeCells[] = "E{$startRow}:E{$endRow}";
            $this->mergeCells[] = "F{$startRow}:F{$endRow}";
            $this->mergeCells[] = "G{$startRow}:G{$endRow}";
            $this->mergeCells[] = "H{$startRow}:H{$endRow}";
            $this->mergeCells[] = "I{$startRow}:I{$endRow}";
            $this->mergeCells[] = "J{$startRow}:J{$endRow}";
            // 记录参会人和论坛信息的合并
            $currentMergeStart = $startRow;
            foreach ($rowsPerAttendee as $count) {
                $endMergeRow = $currentMergeStart + $count - 1;
                $this->mergeCells[] = "K{$currentMergeStart}:K{$endMergeRow}";
                $this->mergeCells[] = "L{$currentMergeStart}:L{$endMergeRow}";
                $this->mergeCells[] = "M{$currentMergeStart}:M{$endMergeRow}";
                $this->mergeCells[] = "N{$currentMergeStart}:N{$endMergeRow}";
                $currentMergeStart = $endMergeRow + 1;
            }
            $startRow = $endRow + 1; // Update the start row for the next item
        }
        return $exportData;
    }

    public function headings(): array
    {
        return [
            ['报名人编号', '会议标题','身份类别','单位名称','单位代码','姓名', '手机号','邀约渠道', '邀约渠道所属事业部','报名时间',
                '参会人信息', '', '',  '',
                '论坛信息', ''],
            ['', '', '', '', '', '','', '','','',
                '参会人编号',	'参会人姓名', '参会人身份类别', '手机号',
                '论坛编号','论坛名称']
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // 设置所有单元格的对齐方式为居中
                $event->sheet->getDelegate()->getStyle('A1:P' . $event->sheet->getDelegate()->getHighestRow())
                    ->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER)
                    ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
                $event->sheet->getDelegate()->mergeCells('K1:N1');
                $event->sheet->getDelegate()->mergeCells('O1:P1');
                $event->sheet->getDelegate()->mergeCells('A1:A2');
                $event->sheet->getDelegate()->mergeCells('B1:B2');
                $event->sheet->getDelegate()->mergeCells('C1:C2');
                $event->sheet->getDelegate()->mergeCells('D1:D2');
                $event->sheet->getDelegate()->mergeCells('E1:E2');
                $event->sheet->getDelegate()->mergeCells('F1:F2');
                $event->sheet->getDelegate()->mergeCells('G1:G2');
                $event->sheet->getDelegate()->mergeCells('H1:H2');
                $event->sheet->getDelegate()->mergeCells('I1:I2');
                $event->sheet->getDelegate()->mergeCells('J1:J2');
                // Merge cells A, B, and C dynamically
                foreach ($this->mergeCells as $mergeCell) {
                    $event->sheet->getDelegate()->mergeCells($mergeCell);
                }
                // 设置单元格宽度自适应
                foreach (range('A', $event->sheet->getDelegate()->getHighestDataColumn()) as $col) {
                    $event->sheet->getDelegate()->getColumnDimension($col)->setAutoSize(true);
                }
            },
        ];
    }

    /**
     * 隐藏手机号中间四位数
     */
    public static function hidePhoneNumber($phoneNumber)
    {
        if (empty($phoneNumber)) {
            return '';
        }
        return substr($phoneNumber, 0, 3) . '****' . substr($phoneNumber, -4);
    }

}
