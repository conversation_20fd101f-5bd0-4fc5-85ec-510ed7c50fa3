<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;

use App\Models\Permission;
use App\Models\UserAccessLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class UserAccessLogController extends Controller
{
    public function list(Request $request)
    {
        $created_at = $request['created_at'];
        $creator = $request['creator'];
        $permission_name = $request['permission_name'];
        $parameter = $request['parameter'];
        $builder = UserAccessLog
            ::when($permission_name, fn($query) => $query->where('permission_name', $permission_name))
            ->when($creator, fn($query) => $query->where('creator', $creator))
            ->when($parameter, fn($query) => $query->where('parameters', 'like', "%$parameter%"))
            ->when($created_at, fn($query) => $query->whereBetween('created_at', searchBetweenDate($created_at)));
        $cnt = $builder->count();
        $list = $builder->pagination()->orderBy("id", "desc")->get();
        return SUCCESS_RESPONSE_ARRAY(compact('cnt', 'list'));
    }

    //
    public static function store(Request $request, Permission $permission)
    {
        $userAccessLog = new UserAccessLog();
        $userAccessLog->method = $request->method();
        $userAccessLog->url = $request->url();
        $userAccessLog->permission_id = $permission->id;
        $userAccessLog->permission_name = $permission->display_name;
        $userAccessLog->parameters = $request->all();
        $userAccessLog->creator = $request->user()->real_name;
        $userAccessLog->ip = $request->ip();
        $userAccessLog->api_token = $request->user()->api_token;
        $userAccessLog->created_at = Carbon::now();
        $userAccessLog->save();
    }


}
