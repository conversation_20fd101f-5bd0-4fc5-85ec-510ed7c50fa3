<?php

namespace App\Http\Middleware;

use App\Models\AppletUser;
use App\Models\Member;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AppletUserMiddleware
{
    /**
     *  废弃
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $this->getTokenForRequest();
        if (empty($token)) {
            return response()->json(['code' => 401, 'message' => 'openid不能为空']);
        }
        Member::firstOrCreate(['open_id' => $token]);
        return $next($request);
    }

    private function getTokenForRequest()
    {
        $inputKey = config('auth.guards.applet.input_key');
        $token = request()->query($inputKey);

        if (empty($token)) {
            $token = request()->input($inputKey);
        }

        if (empty($token)) {
            $token = request()->bearerToken();
        }

        return $token;
    }
}
