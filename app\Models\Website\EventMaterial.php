<?php

namespace App\Models\Website;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $title 资料标题
 * @property string $download_url 资料下载链接
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial whereDownloadUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMaterial whereUpdater($value)
 * @mixin \Eloquent
 */
class EventMaterial extends Model
{
    use HasFactory,PaginationTrait;

    protected $connection = 'mysql_website';

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 定义一个访问器，处理 download_url 字段的前缀添加逻辑
    public function getDownloadUrlAttribute($value)
    {
        return config('filesystems.disks.sftp.domain') . $value;
    }

    public function setDownloadUrlAttribute($value)
    {
        // 假设 config('filesystems.disks.sftp.domain') 返回的是前缀
        $prefix = config('filesystems.disks.sftp.domain');

        // 检查 $value 是否以前缀开头
        if (strpos($value, $prefix) === 0) {
            // 如果是，则去掉前缀
            $value = substr($value, strlen($prefix));
        }

        // 设置 cover 属性
        $this->attributes['download_url'] = $value;
    }
}
