<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/20
 * Time 11:06
 */


use App\Http\Controllers\Admin\Order\InvoiceController;
use App\Http\Controllers\Admin\Order\OrderController;
use App\Http\Controllers\Admin\Order\PayRecordsController;
use Illuminate\Support\Facades\Route;

//订单管理
Route::group(['prefix' => 'order'], function () {

    //订单列表
    Route::post('list', [OrderController::class,'index'])->name('orders.order.list');
    //确认支付
    Route::put('confirm_pay/{id}', [OrderController::class,'update'])->name('orders.order.update')->where('id', '[0-9]+');
    //创建订单
    Route::post('create', [OrderController::class,'store'])->name('orders.order.create');
    //详情
    Route::get('show/{id}', [OrderController::class,'show'])->name('orders.order.show')->where('id', '[0-9]+');

    //退费订单列表
    Route::post('refund_list', [OrderController::class,'refundList'])->name('orders.refundOrder._list');
    //确认退费
    Route::put('confirm_refund/{id}', [OrderController::class,'confirmRefund'])->name('orders.refundOrder.confirm')->where('id', '[0-9]+');

    //发票列表
    Route::post('invoice_list', [InvoiceController::class,'index'])->name('orders.invoice.invoice_list');
    //修改发票状态
//    Route::put('invoice/{id}', [InvoiceController::class,'confirmRefund'])->name('orders.invoice.confirmRefund')->where('id', '[0-9]+');
    //退票
    Route::put('refund_invoice/{id}', [InvoiceController::class,'confirmRefund'])->name('orders.invoice.refund_invoice')->where('id', '[0-9]+');
    //驳回发票
    Route::put('reject_invoice/{id}', [InvoiceController::class,'confirmRefund'])->name('orders.invoice.reject_invoice')->where('id', '[0-9]+');

    //上传发票
    Route::post('upload_invoice/{id}', [InvoiceController::class, 'uploadInvoice'])->name('orders.invoice.upload_invoice')->where('id', '[0-9]+');
    //更新发票信息
    Route::put('update_invoice/{id}', [InvoiceController::class, 'update'])->name('orders.invoice.update_invoice')->where('id', '[0-9]+');

    //缴费记录列表
    Route::post('payment_record_list', [PayRecordsController::class,'index'])->name('orders.payment_record.list');
    //导入
    Route::post('payment_record_import', [PayRecordsController::class,'import'])->name('orders.payment_record.import');
    Route::post('payment_record_export', [PayRecordsController::class,'export'])->name('orders.payment_record.export');
    //更新缴费记录
    Route::put('payment_record/{id}', [PayRecordsController::class,'update'])->name('orders.payment_record.update')->where('id', '[0-9]+');
});
