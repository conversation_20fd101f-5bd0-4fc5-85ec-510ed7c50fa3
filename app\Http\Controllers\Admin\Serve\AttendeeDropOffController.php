<?php

namespace App\Http\Controllers\Admin\Serve;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\AttendeeDropOff;
use App\Repositories\AttendeeDropOffExport;
use App\Repositories\AttendeeDropOffRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class AttendeeDropOffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, AttendeeDropOffRepository $repository)
    {
        $query = $repository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('attendee_drop_offs.id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 导出
    public function export(Request $request, AttendeeDropOffRepository $repository)
    {
        try {
            $list = $repository->listBuilder($request)->orderBy('attendee_drop_offs.id', 'desc')->get();
            $export = new AttendeeDropOffExport($list);// 导出数据到 Excel 文件
            return Excel::download($export, '参会人送站名单服务列表.xlsx');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    // 获取参会人送站信息
    public function getAttendeeDropOffs(string $id)
    {
        return SUCCESS_RESPONSE_ARRAY(Attendee::find($id)->attendeeDropOffs()
            ->orderBy('id', 'desc')->get());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        $data = filterRequestData('attendee_drop_offs');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $attendeeDropOff = AttendeeDropOff::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('attendeeDropOff'));
    }

    public function setMoreAttendeeDropOff(Request $request)
    {
        $attendeeIds = $request->input('attendeeIds');
        if (empty($attendeeIds)) {
            return FAIL_RESPONSE_ARRAY('未选择参会人');
        }

        foreach ($attendeeIds as $attendee_id) {
            $attendee = Attendee::find($attendee_id);
            if (!$attendee) {
                return FAIL_RESPONSE_ARRAY('参会人不存在');
            }

            // 新增
            $data = filterRequestData('attendee_drop_offs');
            $data['attendee_id'] = $attendee_id;
            $data['creator'] = $request->user()->real_name;
            $data['updater'] = $request->user()->real_name;

            AttendeeDropOff::forceCreate($data);
        }

        return SUCCESS_RESPONSE_ARRAY('批量设置成功');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request, string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        // 修改送站信息
        $attendeeDropOff = AttendeeDropOff::find($id);
        if (!$attendeeDropOff) {
            return FAIL_RESPONSE_ARRAY('送站信息不存在');
        }
        $data = filterRequestData('attendee_drop_offs');
        $data['updater'] = $request->user()->real_name;
        $attendeeDropOff->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY(compact('attendeeDropOff'));
    }

    // 批量修改送站人信息
    public function setMoreDropOffPerson(Request $request)
    {
        $attendeeDropOffIds = $request->input('attendeeDropOffIds');
        if (empty($attendeeDropOffIds)) {
            return FAIL_RESPONSE_ARRAY('未选择送站信息');
        }
        $data = filterRequestData('attendee_drop_offs');
        $data['updater'] = $request->user()->real_name;
        foreach ($attendeeDropOffIds as $attendeeDropOffId) {
            $attendeeDropOff = AttendeeDropOff::find($attendeeDropOffId);
            if (!$attendeeDropOff) {
                return FAIL_RESPONSE_ARRAY('送站信息不存在，attendeeDropOffId为：'. $attendeeDropOffId);
            }
            $attendeeDropOff->fill($data)->update();
        }

        return SUCCESS_RESPONSE_ARRAY("批量设置成功");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
        $attendeeDropOff = AttendeeDropOff::find($id);
        if (!$attendeeDropOff) {
            return FAIL_RESPONSE_ARRAY('送站信息不存在');
        }
        $attendeeDropOff->delete();
        return SUCCESS_RESPONSE_ARRAY("删除成功");
    }

    // 修改是否需要送站
    public function setIsNeedDropOff(Request $request, string $id)
    {
        $attendeeDropOff = AttendeeDropOff::find($id);
        if (!$attendeeDropOff) {
            return FAIL_RESPONSE_ARRAY('送站信息不存在');
        }
        $attendeeDropOff->is_need_drop_off = $attendeeDropOff->is_need_drop_off == 1 ? 2 : 1;
        $attendeeDropOff->updater = $request->user()->real_name;
        $attendeeDropOff->update();
        return SUCCESS_RESPONSE_ARRAY("修改成功");
    }

    // 批量修改是否需要送站
    public function setMoreIsNeedDropOff(Request $request)
    {
        $attendeeDropOffIds = $request->input('attendeeDropOffIds');
        $is_need_drop_off = $request->input('is_need_drop_off');
        if (empty($attendeeDropOffIds)) {
            return FAIL_RESPONSE_ARRAY('未选择送站信息');
        }
        // 循环$dineIds，修改是否需要就餐
        foreach ($attendeeDropOffIds as $attendeeDropOffId) {
            $attendeeDropOff = AttendeeDropOff::find($attendeeDropOffId);
            if (!$attendeeDropOff) {
                return FAIL_RESPONSE_ARRAY('送站信息不存在，attendeeDropOffId为：'. $attendeeDropOffId);
            }
            $attendeeDropOff->is_need_drop_off = $is_need_drop_off;
            $attendeeDropOff->updater = $request->user()->real_name;
            $attendeeDropOff->update();
        }

        return SUCCESS_RESPONSE_ARRAY("批量设置成功");
    }

}
