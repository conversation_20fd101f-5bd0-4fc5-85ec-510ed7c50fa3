<?php

namespace App\Http\Controllers\Admin\Event;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\PaidContent;
use App\Repositories\EventRepository;
use App\Repositories\UserRepository;
use EventState;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class EventController extends Controller
{
    public function index(Request $request, EventRepository $eventRepository): array
    {
        //分页查询会议列表
        $query = $eventRepository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    //查询所有已上架会议
    public function all(): array
    {
        $columns = Schema::getColumnListing('events'); // 获取模型的所有字段
        $columns = array_diff($columns, ['creator','updater','deleted_at','created_at','updated_at']); // 排除updater字段
        $events = Event::select($columns)->where('is_on', EventState::ON)->orderBy('id', 'desc')->get();
        // 假设 $list 是一个数组，包含多个元素，每个元素有一个 tags 字段
        foreach ($events as &$item) {
            // 检查 tags 是否为空
            if (!empty($item['tags'])) {
                // 使用 explode 函数将字符串按 '/' 分割成数组
                $tagArray = explode('/', $item['tags']);
                // 将分割后的数组赋值给新字段 tagArray
                $item['tagArray'] = $tagArray;
            } else {
                // 如果 tags 字段为空，则将空数组赋值给新字段 tagArray
                $item['tagArray'] = [];
            }
        }
        return SUCCESS_RESPONSE_ARRAY($events);
    }

    //dropList
    public function dropList(Request $request): array
    {
        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $columns = Schema::getColumnListing('events'); // 获取模型的所有字段
        $columns = array_diff($columns, ['creator','updater','deleted_at','created_at','updated_at']); // 排除updater字段
        $events = Event::query()->select($columns)
            ->whereIn('activity_type', $activityTypes)
            ->orderBy('start_time', 'desc')
            ->get();
        // 假设 $list 是一个数组，包含多个元素，每个元素有一个 tags 字段
        foreach ($events as &$item) {
            // 检查 tags 是否为空
            if (!empty($item['tags'])) {
                // 使用 explode 函数将字符串按 '/' 分割成数组
                $tagArray = explode('/', $item['tags']);
                // 将分割后的数组赋值给新字段 tagArray
                $item['tagArray'] = $tagArray;
            } else {
                // 如果 tags 字段为空，则将空数组赋值给新字段 tagArray
                $item['tagArray'] = [];
            }
        }
        return SUCCESS_RESPONSE_ARRAY($events);
    }

    public function create()
    {
        // 显示创建 Event 的表单
    }

    public function store(Request $request)
    {
        // 保存新创建的 Event
        $data = filterRequestData('events');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $event = Event::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY($event);
    }

    public function show($id)
    {
        // 显示特定 Event 的详细信息
        $event = Event::find($id);
        return SUCCESS_RESPONSE_ARRAY($event);
    }

    //前端查看会议详情
    public function showDetail($id)
    {
        //查询会议详情，会议下的已上架论坛
        $event = Event::query()
            ->with(['forums' => function ($query) {
                $query->where('is_on', EventState::ON);
            }])->find($id);
        //查询会议下得付费内容
        $count = PaidContent::where('event_id', $id)->where('is_on', EventState::ON)->count();
        //会议添加paidContentCount 值为count
        $event->paid_content_count = $count;

        return SUCCESS_RESPONSE_ARRAY($event);
    }

    public function edit($id)
    {
        // 显示更新 Event 的表单
    }

    public function update(Request $request, $id)
    {
        // 更新 Event
        $event = Event::find($id);
        $data = filterRequestData('events');
        $data['updater'] = $request->user()->real_name;
        $event->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY($event);
    }

    public function destroy($id)
    {
        // 删除 Event
        $event = Event::find($id);
        $event->delete();
    }

    public function toggleIsOn(Request $request, $id)
    {
        $event = Event::find($id);
        $event->is_on = $event->is_on == EventState::ON ? EventState::OFF : EventState::ON;
        $event->updater = $request->user()->real_name;
        $event->save();
        return SUCCESS_RESPONSE_ARRAY($event);
    }
    public function auditOrganization(Request $request, $id)
    {
        $event = Event::find($id);
        $event->is_audit_organization = $event->is_audit_organization == EventState::ON ? EventState::OFF : EventState::ON;
        $event->updater = $request->user()->real_name;
        $event->save();
        return SUCCESS_RESPONSE_ARRAY($event);
    }
    public function registrationStatus(Request $request, $id)
    {
        $event = Event::find($id);
        $event->is_registration = $event->is_registration == EventState::ON ? EventState::OFF : EventState::ON;
        $event->updater = $request->user()->real_name;
        $event->save();
        return SUCCESS_RESPONSE_ARRAY($event);
    }
}
