<?php

namespace App\Http\Controllers\Admin\Stat;

use App\Http\Controllers\Controller;
use App\Repositories\BusinessDataStatRepository;
use App\Repositories\DepTwoStatRepository;
use Illuminate\Http\Request;

class BusinessDataStatController extends Controller
{

    // 商务对接客户的住宿数据统计
    public function getAttendeeHotelStatByBusiness(Request $request, BusinessDataStatRepository $repository)
    {
        return $repository->getAttendeeHotelStatByBusiness($request);
    }

    // 商务对接客户住宿信息填写不完整的参会人列表
    public function getAttendeeHotelInfoNotFullListByBusiness(Request $request, BusinessDataStatRepository $repository)
    {
        return $repository->getAttendeeHotelInfoNotFullListByBusiness($request);
    }

    // 获取商务对接的参会人的用餐信息
    public function getAttendeeDineListByBusiness(Request $request, BusinessDataStatRepository $repository)
    {
        return $repository->getAttendeeDineListByBusiness($request);
    }

    // 获取商务跟进信息统计
    public function getFollowDataStatByBusiness(Request $request, BusinessDataStatRepository $repository)
    {
        return $repository->getFollowDataStatByBusiness($request);
    }

    // 获取某商务近七天的跟进数
    public function getNear7DaysFollowNumByBusiness(Request $request, BusinessDataStatRepository $repository)
    {
        return $repository->getNear7DaysFollowNumByBusiness($request);
    }

}
