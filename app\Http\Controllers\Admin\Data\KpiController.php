<?php

namespace App\Http\Controllers\Admin\Data;

use App\Http\Controllers\Controller;
use App\Models\Kpi;
use App\Repositories\KpiRepository;
use Illuminate\Http\Request;

class KpiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, KpiRepository $kpiRepository)
    {
        return $kpiRepository->listBuilder($request);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 新增指标
        $data = filterRequestData('kpis');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        // 如果forum_id为null，则表示是会议指标，则forum_id为0
        $data['forum_id'] = $data['forum_id'] ?? 0;
        $departments = $request->input('departments');
        if (count($departments) == 2) {
            $data['department_one_id'] = $departments[0];
            $data['department_two_id'] = $departments[1];
        } else {
            return FAIL_RESPONSE_ARRAY('部门不能为空');
        }

        $kpi = Kpi::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('kpi'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // 更新指标
        $kpi = Kpi::find($id);
        if (!$kpi) {
            return FAIL_RESPONSE_ARRAY('指标不存在');
        }
        $data = filterRequestData('kpis');
        $data['updater'] = $request->user()->real_name;
        $departments = $request->input('departments');
        if (count($departments) == 2) {
            $data['department_one_id'] = $departments[0];
            $data['department_two_id'] = $departments[1];
        } else {
            return FAIL_RESPONSE_ARRAY('部门不能为空');
        }

        $kpi->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY(compact('kpi'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // 删除指标
        $kpi = Kpi::find($id);
        if (!$kpi) {
            return FAIL_RESPONSE_ARRAY('指标不存在');
        }
        $kpi->delete();
        return SUCCESS_RESPONSE_ARRAY(compact('kpi'));
    }
}
