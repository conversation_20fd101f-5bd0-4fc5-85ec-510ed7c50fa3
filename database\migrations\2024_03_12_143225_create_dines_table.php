<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dines', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->default(0)->comment('会议ID');
            $table->date('dine_date')->nullable()->comment('用餐日期');
            $table->tinyInteger('time_type')->default(0)->comment('早中晚类型：1:早 2:中 3:晚');
            $table->tinyInteger('type')->default(0)->comment('用餐类型：1:电子餐券 2:自助餐 3:座位就餐 4：包间宴请');
            $table->string('location')->default('')->comment('用餐地点');
            $table->string('identity')->default('')->comment('用餐人身份');
            $table->string('specific_time')->default('')->comment('具体用餐时间');
            $table->tinyInteger('is_allow_select')->default(2)->comment('是否让客户自行选择：1:是 2:否');

            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `dines` comment '就餐管理'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dines');
    }
};
