<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\Channel;
use App\Models\Message;
use App\Models\Registrant;
use App\Models\User;
use App\Repositories\BusinessDataStatRepository;
use App\Repositories\DepTwoStatRepository;
use Illuminate\Http\Request;

class IndexController extends Controller
{

    // 新增客户，尚未分配对接人报名人列表
    public function getTodoAssignList(Request $request)
    {
        $roles = $request->user()->roles;
        $department_one_id = $request->user()->department_one_id;
        $department_two_id = $request->user()->department_two_id;

        $query = Registrant::join('attendees', 'attendees.registrant_id', '=', 'registrants.id')
            ->join('events', 'events.id', '=', 'attendees.event_id')
            ->leftJoin('channels', 'channels.id', '=', 'attendees.channel_id')
        ;

        $tag = false;
        if ($roles->contains('name', '管理员')) {
            // 管理员看所有数据，不分部门，不分渠道，无需处理
        } else if ($roles->contains('name', '事业部负责人') && $roles->contains('name', '无渠道分配人')) { // 登录用户是事业部负责人角色，又是无渠道分配人角色
            $query = $query->join('departments as d1', 'd1.id', '=', 'channels.department_one_id')
                ->where(function ($query) use ($department_one_id) {
                    $query->where('channels.department_one_id', $department_one_id)
                        ->orWhereNull('attendees.channel_id', 0);
                });
            $tag = true;
        } else if ($roles->contains('name', '事业部负责人')) { // 登录用户是事业部负责人角色
            $query = $query->join('departments as d1', 'd1.id', '=', 'channels.department_one_id')
                ->where('channels.department_one_id', $department_one_id);
            $tag = 1;
        } else if ($roles->contains('name', '部门负责人') && $roles->contains('name', '无渠道分配人')) { // 登录用户是部门负责人角色，又是无渠道分配人角色
            $query = $query->join('departments as d1', 'd1.id', '=', 'channels.department_one_id')
                ->where(function ($query) use ($department_two_id) {
                    $query->where('channels.department_two_id', $department_two_id)
                        ->orWhereNull('attendees.channel_id', 0);
                });
            $tag = true;
        } else if ($roles->contains('name', '部门负责人')) {// 登录用户是部门负责人角色
            $query = $query->join('departments as d1', 'd1.id', '=', 'channels.department_one_id')
                ->where('channels.department_two_id', $department_two_id);
            $tag = true;
        } else if ($roles->contains('name', '无渠道分配人')) {// 登录用户是无渠道分配人角色
            $query = $query->where('attendees.channel_id', 0);
        }

        if ($tag) {
            $query = $query->where('attendees.status', '=', Attendee::$status_ok)
                ->where('attendees.user_id', 0)
                ->selectRaw('registrants.id,registrants.name,registrants.phone,registrants.organization,registrants.organization_type,
                registrants.identity,min(attendees.created_at) as created_at,attendees.batch,count(*) as attendeesNum,events.short_title as event_short_title,
                d1.name as department_name,channels.name as channel_name')
                ->groupBy('registrants.id', 'attendees.batch', 'events.short_title', 'd1.name', 'channels.name');
        }  else {
            $query = $query->where('attendees.status', '=', Attendee::$status_ok)
                ->where('attendees.user_id', 0)
                ->selectRaw('registrants.id,registrants.name,registrants.phone,registrants.organization,registrants.organization_type,
                registrants.identity,min(attendees.created_at) as created_at,attendees.batch,count(*) as attendeesNum,events.short_title as event_short_title
                ,channels.name as channel_name')
                ->groupBy('registrants.id', 'attendees.batch', 'events.short_title', 'channels.name');
        }

        $cnt = $query->get()->count();
        $list = $query->pagination()->orderBy('registrants.id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }


    // 商务新分配客户（尚未跟进的客户）
    public function getBusinessNewAssignAttendeeList(Request $request)
    {
        $user = $request->user();

        $list = Attendee::join('events', 'events.id', '=', 'attendees.event_id')
            ->leftJoin('registrants', 'registrants.id', '=', 'attendees.registrant_id')
            ->with(['lastFollowMessage'])
            ->where('attendees.status', '=', Attendee::$status_ok)
            ->where('attendees.user_id', $user->id)
            ->doesntHave('lastFollowMessage')
            ->select('attendees.id', 'attendees.name', 'attendees.phone', 'attendees.organization', 'attendees.position',
                'attendees.identity', 'attendees.created_at', 'attendees.assigner', 'attendees.assign_at',
                'registrants.name as registrant_name', 'events.short_title as event_short_title')
            ->orderBy('attendees.assign_at', 'desc')
            ->get();

        return SUCCESS_RESPONSE_ARRAY($list);
    }


    // 客户更新信息列表
    public function getAttendeeEditInfoList(Request $request)
    {
        $list = Attendee::join('events', 'events.id', '=', 'attendees.event_id')
            ->join('registrants', 'registrants.id', '=', 'attendees.registrant_id')
            ->join('messages', 'messages.attendee_id', '=', 'attendees.id')
            ->where('attendees.user_id', $request->user()->id)
            ->where('messages.source', '=', Message::$source_customer_submit)
            ->select('attendees.id', 'attendees.name', 'attendees.phone', 'attendees.organization', 'attendees.position', 'attendees.identity',
                'messages.content', 'messages.created_at',
                'registrants.name as registrant_name', 'events.short_title as event_short_title')
            ->orderBy('messages.created_at', 'desc')
            ->get();

        return SUCCESS_RESPONSE_ARRAY($list);
    }

}
