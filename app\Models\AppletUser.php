<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $openid
 * @property string $phone
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser query()
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser whereOpenid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AppletUser whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class AppletUser extends Model
{
    use HasFactory;

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }
}
