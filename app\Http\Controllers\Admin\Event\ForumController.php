<?php

namespace App\Http\Controllers\Admin\Event;
use App\Models\Attendee;
use App\Models\Forum;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ForumController extends Controller
{
    public function index(Request $request, $event_id)
    {
        $is_on = $request->input('is_on');
        //查询会议下的所有论坛
        //如果all为1，则返回所有论坛，否则只返回上架论坛
        $forums = Forum::where('event_id', $event_id)
            ->when($is_on, fn($query) => $query->where('is_on', $is_on))
            ->orderBy('id', 'desc')->get();
        $forums->each(function ($forum) {
            $forum->attendees_count = $forum->attendees()
                ->where('attendees.status', 1)
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('attendee_forums.type', 2)
                            ->where('attendee_forums.audit_status', 1);
                    })->orWhere(function ($query) {
                        $query->where('attendee_forums.type', 1)
                            ->where('attendee_forums.intention', 1);
                    });
                })
                ->count();
        });
//        $forums->each(function ($forum) {
//            $forum->attendees_count = $forum->attendees()->where('attendees.status', 1)->where('audit_status',1)->count();
//        });
        //循环结果结果，获取所有的number的和和attendees_count的和
        $total_number = $forums->sum('number');
        $total_attendees_count = $forums->sum('attendees_count');
        return SUCCESS_RESPONSE_ARRAY(compact('forums', 'total_number', 'total_attendees_count'));
    }

    public function attendeeForumList(Request $request, $event_id, $forum_id)
    {
        $query = Attendee::select('attendees.*', 'forums.name as forum_name',
            'attendee_forums.audit_status', 'attendee_forums.type', 'attendee_forums.intention')
            ->where('attendees.event_id', $event_id)
            ->where('attendees.status', 1)
//            ->where('attendee_forums.audit_status', 1)
            ->join('attendee_forums', 'attendees.id', '=', 'attendee_forums.attendee_id')
            ->join('forums', 'forums.id', '=', 'attendee_forums.forum_id')
            ->where('forums.id', '=', $forum_id)
            ->where(function ($query) {
                $query->where(function ($query) {
                    $query->where('attendee_forums.type', 2)
                        ->where('attendee_forums.audit_status', 1);
                })->orWhere(function ($query) {
                    $query->where('attendee_forums.type', 1)
                        ->where('attendee_forums.intention', 1);
                });
            });
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('attendees.id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    //查询所有需要审核的论坛 id name
    public function auditForums(Request $request)
    {
        $event_ids = $request->input('event_id');
        //查询id name字段
        $forums = Forum::whereIn('event_id', $event_ids)->where('is_on', 1)->where('is_audit', 1)->get(['id', 'name']);
        return SUCCESS_RESPONSE_ARRAY($forums);
    }

    public function create()
    {
        // 显示创建 Forum 的表单
    }

    public function store(Request $request)
    {
        // 保存新创建的 Forum
        $data = filterRequestData('forums');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $forum = Forum::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY($forum);
    }

    public function show($id)
    {
        // 显示特定 Forum 的详细信息
        $forum = Forum::find($id);
        return SUCCESS_RESPONSE_ARRAY($forum);
    }

    public function edit($id)
    {
        // 显示更新 Forum 的表单
    }

    public function update(Request $request, $id)
    {
        // 更新 Forum
        $forum = Forum::find($id);
        $data = filterRequestData('forums');
        $data['updater'] = $request->user()->real_name;
        $forum->fill($data)->save();
    }

    public function destroy($id)
    {
        // 删除 Forum
        $forum = Forum::find($id);
        $forum->delete();
    }

    //主键为路由参数，如果当前is_on为1，则is_on改为2，否则改为1
    public function toggleIsOn(Request $request, $id)
    {
        $forum = Forum::find($id);
        $forum->is_on = $forum->is_on == 1 ? 2 : 1;
        $forum->updater = $request->user()->real_name;
        $forum->save();
        return SUCCESS_RESPONSE_ARRAY($forum);
    }

    //主键为路由参数 如果当前is_enroll为1，则is_enroll改为2，否则改为1
    public function toggleIsEnroll(Request $request, $id)
    {
        $forum = Forum::find($id);
        $forum->is_enroll = $forum->is_enroll == 1 ? 2 : 1;
        //如果is_enroll为2，则is_audit改为2
        if ($forum->is_enroll == 2) {
            $forum->is_audit = 2;
        }
        $forum->updater = $request->user()->real_name;
        $forum->save();
        return SUCCESS_RESPONSE_ARRAY($forum);
    }
    //主键为路由参数，如果当前is_audit为1，则is_audit改为2，否则改为1
    public function toggleIsAudit(Request $request, $id)
    {
        $forum = Forum::find($id);
        $forum->is_audit = $forum->is_audit == 1 ? 2 : 1;
        $forum->updater = $request->user()->real_name;
        $forum->save();
        return SUCCESS_RESPONSE_ARRAY($forum);
    }
    public function toggleIsCheck(Request $request, $id)
    {
        $forum = Forum::find($id);
        $forum->is_checked = $forum->is_checked == 1 ? 2 : 1;
        $forum->updater = $request->user()->real_name;
        $forum->save();
        return SUCCESS_RESPONSE_ARRAY($forum);
    }
}
