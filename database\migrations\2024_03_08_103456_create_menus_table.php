<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     * create table menus
     */
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table) {
            $table->id();
            $table->string('menu_name');
            $table->tinyInteger('level')->default(1);
            $table->integer('parent_id')->default(0);
            $table->string('url', 512)->nullable();
            $table->integer('sort')->default(999);
            $table->string('svg_icon', 2048)->nullable()->comment('菜单图标');

            $table->string('creator', 20)->nullable()->comment('添加人');
            $table->string('updater', 20)->nullable()->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `menus` comment '菜单表'");
        Schema::create('role_has_menus', function (Blueprint $table) {
            $table->unsignedInteger('menu_id');
            $table->unsignedInteger('role_id');
            $table->primary(['menu_id', 'role_id'], 'role_has_menus_menu_id_role_id_primary');
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `role_has_menus` comment '角色菜单表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menus');
        Schema::dropIfExists('role_has_menus');
    }
};
