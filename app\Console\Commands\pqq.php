<?php

namespace App\Console\Commands;

use App\Models\Member;
use App\Models\User;
use App\YuanBo\Sms\AliSender;
use App\YuanBo\Sms\JianZhouSender;
use App\YuanBo\Support\Facades\SmsManagerFacade;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class pqq extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:pqq';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

//        $rst = SmsManagerFacade::setSender(JianZhouSender::class)->singleSend('13162971279', 'Hello World !!!', '【远播国际】');
////        dd($rst);
//
//        $rst =SmsManagerFacade::setSender(AliSender::class)
//            ->templateSend('13162971279', 'SMS_465966650', ['forum_name' => '测试', 'time' => Carbon::now()->format('Y-m-d'), 'address' => '上海市徐汇区宜山路333号'], '远播教育');
//        dd($rst);


        //
//        $user = User::whereUsername('<EMAIL>')->first();
//        $user->password = bcrypt('114study');
//        $user->save();
//        dd($user->toArray());
    }
}
