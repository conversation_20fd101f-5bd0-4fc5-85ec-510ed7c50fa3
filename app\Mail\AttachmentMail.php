<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AttachmentMail extends Mailable
{
    use Queueable, SerializesModels;

    public $url;
    public $mimeType;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($url, $mimeType)
    {
        $this->url = $url;
        $this->mimeType = $mimeType;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.attachment')
            ->subject('SEEE电子发票')
            ->attach($this->url, [
                'as' => '电子发票' . ($this->mimeType == 'image/jpeg' ? '.jpg' : '.png'),
                'mime' => $this->mimeType,
            ]);
    }
}
