<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Website\InvitationCode;
use Illuminate\Http\Request;

class InvitationCodeController extends Controller
{
    //判断输入的邀请码是否正确且有效
    public function check(Request $request, $invitation_code)
    {
        //邀请码未使用
        $attendee = InvitationCode::where('code', $invitation_code)->where('status', 2)->first();
        if ($attendee) {
            return SUCCESS_RESPONSE_ARRAY();
        } else {
            return FAIL_RESPONSE_ARRAY('邀请码不存在');
        }
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
