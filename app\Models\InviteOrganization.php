<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 *
 *
 * @property-read \App\Models\Event|null $event
 * @method static \Illuminate\Database\Eloquent\Builder|InviteOrganization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InviteOrganization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InviteOrganization onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|InviteOrganization pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|InviteOrganization query()
 * @method static \Illuminate\Database\Eloquent\Builder|InviteOrganization withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|InviteOrganization withoutTrashed()
 * @mixin \Eloquent
 */
class InviteOrganization extends Model
{
    use SoftDeletes, HasFactory, PaginationTrait;

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //邀约单位属于会议
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class, 'event_id');
    }

    //attendees
    // 邀约单位有多个参会人
    public function attendees(): HasMany
    {
        return $this->hasMany(Attendee::class, 'organization', 'organization');
    }

    public static function convertOrganizationType($status)
    {
        switch ($status) {
            case '省教育考试院':
                return 1;
            case '市区教育学院':
                return 2;
            case '市区招办':
                return 3;
            case '高校招办4:
                return ';
            case '公办高中':
                return 5;
            case '教育局':
                return 6;
            case '大学非招办':
                return 7;
            case '民办学校小初高':
                return 8;
            case '协会':
                return 9;
            case '公办初中':
                return 10;
            case '公办小学':
                return 11;
            case '教育机构':
                return 12;
            case '非教育类机构':
                return 13;
            case '其它':
                return 14;
            default:
                return 0;
        }
    }

}
