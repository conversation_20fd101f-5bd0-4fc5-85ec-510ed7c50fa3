<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_event_histories', function (Blueprint $table) {
            $table->id();
            $table->string('title')->default('')->comment('标题');
            $table->string('edition')->default('')->comment('界、版本');
            $table->integer('year')->default(date('Y'))->comment('年份');
            $table->longText('introduction')->comment('介绍');
            $table->text('content')->comment('详情');
            $table->string('photo')->default('')->comment('图片地址');
            $table->string('url')->default('')->comment('跳转地址');
            //发布时间
            $table->timestamp('published_at')->nullable()->comment('发布时间');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `website_event_histories` comment '官网往届回顾表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('histories');
    }
};
