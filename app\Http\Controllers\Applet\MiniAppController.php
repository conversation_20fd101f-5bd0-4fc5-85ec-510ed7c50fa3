<?php

namespace App\Http\Controllers\Applet;

use App\Http\Controllers\Controller;
use App\Models\Member;
use EasyWeChat\Kernel\Exceptions\BadResponseException;
use EasyWeChat\Kernel\Exceptions\HttpException;
use EasyWeChat\MiniApp\AccessToken;
use Illuminate\Http\Request;


use EasyWeChat\MiniApp\Application;
use Illuminate\Support\Facades\Log;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;


class MiniAppController extends Controller
{
    const APPLET_CONFIG = [
        'app_id' => 'wx3e8e76dcbe626656',
        'secret' => 'adef48e6d4c9e3a4840b0aeda2453545',
        /**
         * 接口请求相关配置，超时时间等，具体可用参数请参考：
         * https://github.com/symfony/symfony/blob/5.3/src/Symfony/Contracts/HttpClient/HttpClientInterface.php
         */
        'http' => [
            'throw' => true, // 状态码非 200、300 时是否抛出异常，默认为开启
            'timeout' => 60,
            // 'base_uri' => 'https://api.weixin.qq.com/', // 如果你在国外想要覆盖默认的 url 的时候才使用，根据不同的模块配置不同的 uri

            'retry' => true, // 使用默认重试配置
        ],
    ];

    /**
     *  获取openid
     * @return array
     * @throws HttpException
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    function codeToSession()
    {
        $code = request()->code;
        $app = new Application(MiniAppController::APPLET_CONFIG);
        $response = $app->getUtils()->codeToSession($code);
        if (array_key_exists('openid', $response)) {
            Member::firstOrCreate(['open_id' => $response['openid']]);
            return SUCCESS_RESPONSE_ARRAY($response);
        } else {
            return FAIL_RESPONSE_ARRAY($response);
        }


        // {
        //     "openid": "o6_bmjrPTlm6_2sgVt7hMZOPxxxx",
        //     "session_key": "tiihtNczf5v6AKRyjwExxxx=",
        //     "unionid": "o6_bmasdasdsad6_2sgVt7hMZOxxxx",
        //     "errcode": 0,
        //     "errmsg": "ok"
        //}
    }

    /**
     *  获取用户手机号
     * @return array
     * @throws ClientExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws BadResponseException
     */
    function getPhoneNumber()
    {
        $app = new Application(MiniAppController::APPLET_CONFIG);
        $app->getAccessToken()->refresh();
//        $accessToken = $app->getAccessToken();
//        $token = $accessToken->getToken(); // string
//        dd($token);
        $data = [
            'code' => (string)request()->get('code'),
        ];
        $response = $app->getClient()->postJson('wxa/business/getuserphonenumber', $data)->toArray(false);

        return SUCCESS_RESPONSE_ARRAY($response);
    }

    //获取小程序二维码
    function getQrCode(Request $request)
    {
        $app = new Application(MiniAppController::APPLET_CONFIG);
        $data = [
            'scene' => $request->input('scene'),
            'check_path' => false,
            'page' => $request->input('path'),
            'env_version' => $request->input('env_version'),
        ];
        $response = $app->getClient()->postJson('wxa/getwxacodeunlimit', $data);
        return SUCCESS_RESPONSE_ARRAY($response->toDataUrl());
    }

    //获取小程序获取加密URLLink
    function getShortUrl(Request $request)
    {
        $app = new Application(MiniAppController::APPLET_CONFIG);
        Log::error('微信token更新getShortUrl');
        $accessToken = new AccessToken(
            appId: $app->getAccount()->getAppId(),
            secret: $app->getAccount()->getSecret(),
            cache: $app->getCache(),
            httpClient: $app->getHttpClient(),
        );
        Log::error('微信token更新$accessToken: '. $accessToken->getToken());

        $app->setAccessToken($accessToken);
        $data = [
            'path' => $request->input('path'),
            'expire_type' => 1,
            'expire_interval' => 30,
            'env_version' => $request->input('env_version'),
        ];
        $response = $app->getClient()->postJson('wxa/generate_urllink', $data)->toArray(false);
        //获取$response的url_link字段
        $errcode = $response['errcode'];
        $errmsg = $response['errmsg'];
        Log::error('微信token更新$errcode: ' . $errcode);
        Log::error('微信token更新$errmsg: ' . $errmsg);
        return SUCCESS_RESPONSE_ARRAY($response);
    }

    //发送订阅消息
    function sendSubscribeMessage(Request $request)
    {
        $app = new Application(MiniAppController::APPLET_CONFIG);
        // 直接构建符合微信小程序模板消息要求的数据结构
        $formattedData = [
            "thing1" => [
                "value" => "2024第八届教育考试与评价研讨会"
            ],
            "time2" => [
                "value" => "2024-11-01 15:30:00~2024-11-01 18:30:00"
            ],
            "thing3" => [
                "value" => "上海同济大学"
            ],
            "thing4" => [
                "value" => "wwt"
            ],
            "thing5" => [
                "value" => "测试"
            ]
        ];

        $data = [
            'template_id' => 'n1uStOJLYRBUcPjeMV77twhMfVtznHwgPL7_qMTqfoY',
            'touser' => $request->input('open_id'),
            'page' => 'pages/index/index',
            'data' => $formattedData,
            'miniprogram_state' => 'trial',
            'lang' => 'zh_CN'
            ];
        $response = $app->getClient()->postJson('cgi-bin/message/subscribe/send', $data)->toArray(false);
        return SUCCESS_RESPONSE_ARRAY($response);

    }

}
