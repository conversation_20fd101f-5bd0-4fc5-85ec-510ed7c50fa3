<?php

namespace App\Console\Commands;

use App\Models\Attendee;
use App\Models\AttendeeInterview;
use App\Models\Forum;
use Illuminate\Console\Command;
use Sign;

class SmsSendEveryMinuteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sms-send-every-minute-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建每分钟定时短信发送任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 发送采访即将开始短信
        $this->sendInterviewStartSms();

        // 发送论坛即将开始短信
        $this->sendForumStartSms();

        return 0;
    }


    // 发送采访即将开始短信
    public function sendInterviewStartSms()
    {
        // 获取参会人采访信息
        $query = AttendeeInterview::join('attendees', 'attendees.id', '=', 'attendee_interviews.attendee_id')
            ->select('attendees.phone', 'attendee_interviews.theme', 'attendee_interviews.time', 'attendee_interviews.location')
            ->where('attendees.status', Attendee::$status_ok)
            // 查找采访开始15分钟前
            ->where('attendee_interviews.time', '>=', date('Y-m-d H:i:s', strtotime('+20 minute')))
            ->where('attendee_interviews.time', '<=', date('Y-m-d H:i:s', strtotime('+21 minute')))
            ->get();

//        dd(date('Y-m-d H:i:s', strtotime('+15 minute')) . '---' . date('Y-m-d H:i:s', strtotime('+16 minute')));

        foreach ($query as $interview) {
            $smsContent = "您好，采访theme即将于time在address开始，请及时前往。";
            $params = [
                'theme' => $interview->theme,
                'time' => $interview->start_time,
                'address' => $interview->location
            ];
            // 发送短信
            singleSendJianzhouSms(Sign::SEEE, $interview->phone, replaceParams($smsContent, $params));
        }
    }

    // 发送论坛即将开始短信
    public function sendForumStartSms()
    {
        // 获取参会人论坛信息
        $forums = Forum::query()
            // 查找论坛开始10分钟前
            ->where('start_time', '>=', date('Y-m-d H:i:s', strtotime('+15 minute')))
            ->where('start_time', '<=', date('Y-m-d H:i:s', strtotime('+16 minute')))
            ->where('is_on', 1)
            ->get()
        ;

        $smsContent = "您好，您报名的forumName即将于startTime在address开始进行，请及时前往。";
        // 组织发送短信的参数
        foreach ($forums as $forum) {
            $params = [
                'forumName' => $forum->name,
                'startTime' => $forum->start_time,
                'address' => $forum->address
            ];

            batchSendJianzhouSms(Sign::SEEE, $forum->attendeeForums->pluck('attendee')->pluck('phone')->toArray(), replaceParams($smsContent, $params));
        }

    }
}
