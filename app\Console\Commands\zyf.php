<?php

namespace App\Console\Commands;

use App\Models\Attendee;
use App\Models\Department;
use App\Models\SmsNotice;
use App\Models\User;
use App\Notifications\HotelMsg;
use App\Notifications\Message;
use App\Repositories\NoticeRepository;
use App\YuanBo\Sms\AliSender;
use App\YuanBo\Sms\JianZhouSender;
use App\YuanBo\Support\Facades\SmsManagerFacade;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Crypt;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\Notification;


class zyf extends Command
{
    /**
     * The name and signature of the console command.
     * 控制台输入  php artisan app:zyf  执行当前类的handle方法
     * @var string
     */
    protected $signature = 'app:zyf';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试用command';

    /**
     * Execute the console command.
     */
    public function handle()
    {


//        $res = Department::whereId(3)
//            ->where(function($query){
//            })
//            ->with(['channels_copy'=>fn($q)=>$q->withCount(['attendees as userCount'])])
//            ->get()
//            ->toArray();
//        dd($res);
//        dd(222);
//
//
//        $sql = "WITH DateRange AS (
//                SELECT CURDATE() - INTERVAL 6 DAY AS `date`
//                UNION ALL
//                SELECT CURDATE() - INTERVAL 5 DAY
//                UNION ALL
//                SELECT CURDATE() - INTERVAL 4 DAY
//                UNION ALL
//                SELECT CURDATE() - INTERVAL 3 DAY
//                UNION ALL
//                SELECT CURDATE() - INTERVAL 2 DAY
//                UNION ALL
//                SELECT CURDATE() - INTERVAL 1 DAY
//                UNION ALL
//                SELECT CURDATE()
//            ),
//            AttendeeCounts AS (
//                SELECT
//                    DATE(a.created_at) AS `date`,
//                    COUNT(*) AS num
//                FROM attendees AS a
//                JOIN registrants AS b ON a.registrant_id = b.id
//                JOIN channels AS c ON b.channel_id = c.id
//                WHERE a.event_id = 2 AND a.`status`=1 AND c.department_one_id=2
//                GROUP BY DATE(a.created_at)
//            )
//            SELECT
//                d.`date`,
//                COALESCE(ac.num, 0) AS num
//            FROM DateRange d
//            LEFT JOIN AttendeeCounts ac ON d.`date` = ac.`date`
//            ORDER BY d.`date`;";
//       $res =  \DB::select($sql);
//       dd($res);
//
//
//        $arr = array(
//            'thing1' => array('value' => "会议主题：", 'color' => '#000000'),
//            'time3' => array('value' => '2024-05-20 13:14', 'color' => '#000000'),
//            'thing4' => array('value' => '会议地点', 'color' => '#000000'),
//        );
//        $rst = json_encode($arr,JSON_UNESCAPED_UNICODE);
//        dd($rst);


//        $res = SmsNotice::whereJsonContains('filter_json', ['name' => 'zy1f'])->get();
//        dd($res->toArray());
//        dd(222);
//        //
//        $res = [
//            'age' => '18',
//            'name' => 'zyf',
//            'sex' => '男'
//        ];
//        $json = json_encode($res, JSON_UNESCAPED_UNICODE);
//        dd($json);

        // 发送单条短信
//        $rst = SmsManagerFacade::setSender(JianZhouSender::class)->singleSend('18611134083', 'Hello World !!!', '【远播国际】');
//        dd($rst);
        // 发送批量短信
//        $rst = SmsManagerFacade::setSender(JianZhouSender::class)->batchSend(['18611134083','13367197295'], 'Hello World !!!', '【远播国际】');
//        dd($rst);

//        $rst =SmsManagerFacade::setSender(AliSender::class)
//            ->templateSend('18611134083', 'SMS_465911625', ['event_title' => '测试', 'time' => Carbon::now()->format('Y-m-d'), 'address' => '上海市徐汇区宜山路333号'], '远播教育');
//        dd($rst);  // 483422615676612142^0

        // ===================================== Excel导出定义表头 begin ==========================================
        $header = [
            'id' => ['name' => '编号', 'width' => 15],
            'event_short_title' => ['name' => '会议标题', 'width' => 30],
        ];
        $data = [
            ['id' => 1, 'event_short_title' => '会议标题111', 'event' => '123'],
            ['id' => 2, 'event_short_title' => '会议标题222', 'event' => '345'],
        ];
//        // 初始化 letters 数组
//        $letters = [];
//        foreach (array_keys($header) as $key) {
//            $letters[] = chr(ord('A') + array_search($key, array_keys($header)));
//        }
//        // 初始化 widths 数组
//        $widths = array_combine($letters, array_column($header, 'width'));
//        dd($widths);

        // 获取 header 中的键和 name
        $keysAndNames = array_map(function ($value) {
            return $value['name'];
        }, $header);
//        // 初始化新的数组
//        $newData = [];
//        // 将 header 的键和 name 作为第一个数据
//        $newData[] = $keysAndNames;
//        // 获取 header 中的键
//        $keys = array_keys($header);
//        // 遍历 data 数组
//        foreach ($data as $row) {
//            // 初始化一个临时数组
//            $tempRow = [];
//            // 遍历 header 中的键
//            foreach ($keys as $key) {
//                // 如果当前行中有这个键，则将其值加入临时数组
//                if (isset($row[$key])) {
//                    $tempRow[$key] = $row[$key];
//                }
//            }
//            // 将临时数组加入新的数组
//            $newData[] = $tempRow;
//        }


        // 初始化新的数组，并添加 header 的键和 name
        $newData = [$keysAndNames];
        // 遍历 data 数组
        foreach ($data as $row) {
            // 使用 array_intersect_key 提取交集部分
            $tempRow = array_intersect_key($row, $header);
            $newData[] = $tempRow;
        }

        dd($newData);
        // ===================================== Excel导出定义表头 end ==========================================

    }
}
