<?php

namespace App\YuanBo\Wechat;


use App\Models\WechatToken;
use EasyWeChat\Kernel\Exceptions\HttpException;

class MyAccessToken extends \EasyWeChat\OfficialAccount\AccessToken
{
    const CACHE_KEY_PREFIX = 'mini_app';

    public function getAccessToken(): string
    {
        $response = $this->httpClient->request(
            'GET',
            'cgi-bin/token',
            [
                'query' => [
                    'grant_type' => 'client_credential',
                    'appid' => $this->appId,
                    'secret' => $this->secret,
                ],
            ]
        )->toArray(false);

        if (empty($response['access_token'])) {
            throw new HttpException('Failed to get access_token: ' . json_encode($response, JSON_UNESCAPED_UNICODE));
        }

        $token = WechatToken::where('appid', $this->appId)->first();

        if ($token && !$token->isExpired()) {
            return $token->access_token;
        } else {
            $this->setToken($response['access_token'], intval($response['expires_in']));
        }

        return $response['access_token'];
    }

    public function setToken(string $token, int $expiresIn = 7200)
    {
//        dump($this->getCredentials()['appid']."=============");
        WechatToken::updateOrCreate(
            ['appid' => $this->appId],
            ['access_token' => $token,
                'expires_at' => now()->addSeconds($expiresIn)]
        );

        return $this;
    }
}
