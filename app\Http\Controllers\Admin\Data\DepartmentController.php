<?php

namespace App\Http\Controllers\Admin\Data;

use App\Http\Controllers\Controller;
use App\Models\Department;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class DepartmentController extends Controller
{
    /**
     * Display a listing of the resource.
     * 部门列表
     */
    public function list()
    {
        // 取出所有一级部门
        $departments = Department::where('status', 1)->where('parent_id', 0)->get();
        $departments->each(function ($item) {
            // 取出一级部门下的子部门
            $item->children = Department::where('status', 1)->where('parent_id', $item->id)->get();
        });
        return SUCCESS_RESPONSE_ARRAY($departments);
    }

    public function oneDepartmentsDrop()
    {
//        $departments = Cache::get('oneDepartmentsDrop');
//        if (Cache::has('oneDepartmentsDrop')) {
//            $departments = Cache::get('oneDepartmentsDrop');
//        }
//        else {
            // 取出所有启用的一级部门
            $departments = Department::select('id', 'name', 'code')
                ->where('status', 1)->where('parent_id', 0)->get();

//            Cache::put('oneDepartmentsDrop', $departments, 60 * 60 * 24);
//        }
        return SUCCESS_RESPONSE_ARRAY($departments);
    }

    // 根据一级部门id，获取二级部门列表
    public function getDepartmentByParentId(Request $request, $id)
    {
        $department = Department::find($id);
        if (!$department) {
            return FAIL_RESPONSE_ARRAY('部门不存在');
        }
        $children = Department::where('status', 1)->where('parent_id', $id)->get();
        return SUCCESS_RESPONSE_ARRAY($children);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 新增部门
        $data = filterRequestData('departments');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        // 将 activity_types 转换为 JSON 字符串
        if (is_array($data['activity_types']) && !empty($data['activity_types'])) {
            $data['activity_types'] = json_encode($data['activity_types']);
        } else {
            $data['activity_types'] = null;
        }
//        if (!is_array($data['activity_types'])) {
//            $data['activity_types'] = null;
//        } else {
//            if (!$data['activity_types'])
//                $data['activity_types'] = null;
//        }
        $department = Department::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY($department);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // 更新部门
        $department = Department::find($id);
        if (!$department) {
            return FAIL_RESPONSE_ARRAY('部门不存在');
        }

        $data = filterRequestData('departments');
        $data['updater'] = $request->user()->real_name;
        if (!is_array($data['activity_types'])) {
            $data['activity_types'] = null;
        } else {
            if (!$data['activity_types'])
                $data['activity_types'] = null;
        }
        $department->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY($department);
    }

    // 改变部门状态
    public function changeStatus(Request $request, $id)
    {
        $department = Department::find($id);
        if (!$department) {
            return FAIL_RESPONSE_ARRAY('部门不存在');
        }
        $department->update([
            'status' => 2,
            'updater' => $request->user()->real_name,
        ]);
        return SUCCESS_RESPONSE_ARRAY(compact('department'));
    }
}
