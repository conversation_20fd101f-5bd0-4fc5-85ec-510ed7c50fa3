<?php

namespace App\Http\Controllers\Admin\Data;

use App\Http\Controllers\Controller;
use App\Models\Hotel;
use App\Models\HotelBooking;
use Illuminate\Http\Request;

class HotelBookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $hotel_id = $request->input('hotel_id');
        if (!$hotel_id) {
            return FAIL_RESPONSE_ARRAY('参数错误');
        }

        $builder = HotelBooking::when($hotel_id, fn($query) => $query->where('hotel_id', $hotel_id));
        $cnt = $builder->count();
        $list = $builder->pagination()->with(['hotel:id,name'])
            ->orderBy('id', 'desc')->get()
            ->each(function (&$item) {
                $item->hotel_name = $item->hotel->hotel_name;
                unset($item->hotel);
            });

        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $hotel_id = $request->input('hotel_id');
        $hotel = Hotel::find($hotel_id);
        if (!$hotel) {
            return FAIL_RESPONSE_ARRAY('酒店不存在');
        }

        // 新增酒店预定
        $data = filterRequestData('hotel_bookings');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $hotelBooking = HotelBooking::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('hotelBooking'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // 修改酒店预定
        $hotelBooking = HotelBooking::find($id);
        if (!$hotelBooking) {
            return FAIL_RESPONSE_ARRAY('预定酒店不存在');
        }
        $data = filterRequestData('hotel_bookings');
        $data['updater'] = $request->user()->real_name;
        $hotelBooking->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY(compact('hotelBooking'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
