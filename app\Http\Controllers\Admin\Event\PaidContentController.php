<?php

namespace App\Http\Controllers\Admin\Event;

use App\Http\Controllers\Controller;
use App\Models\PaidContent;
use Illuminate\Http\Request;

class PaidContentController extends Controller
{
    public function index($event_id)
    {
        // 查询会议下的所有付费内容
        $paid_contents = PaidContent::where('event_id', $event_id)->get();
        return SUCCESS_RESPONSE_ARRAY($paid_contents);
    }

    public function create()
    {
        // 显示创建 PaidContent 的表单
    }

    public function store(Request $request)
    {
        // 保存新创建的 PaidContent
        $data = filterRequestData('paid_contents');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $paid_content = PaidContent::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY($paid_content);
    }

    public function show($id)
    {
        // 显示特定 PaidContent 的详细信息
        $paid_content = PaidContent::find($id);
        return SUCCESS_RESPONSE_ARRAY($paid_content);
    }

    public function edit($id)
    {
        // 显示更新 PaidContent 的表单
    }

    public function update(Request $request, $id)
    {
        // 更新 PaidContent
        $paid_content = PaidContent::find($id);
        $data = filterRequestData('paid_contents');
        $data['updater'] = $request->user()->real_name;
        $paid_content->fill($data)->save();
        return SUCCESS_RESPONSE_ARRAY($paid_content);
    }

    public function destroy($id)
    {
        // 删除 PaidContent
        $paid_content = PaidContent::find($id);
        $paid_content->delete();
    }

    //主键为路由参数，如果当前is_on为1，则is_on改为2，否则改为1
    public function toggleIsOn(Request $request, $id)
    {
        $paid_content = PaidContent::find($id);
        $paid_content->is_on = $paid_content->is_on == 1 ? 2 : 1;
        $paid_content->updater = $request->user()->real_name;
        $paid_content->save();
        return SUCCESS_RESPONSE_ARRAY($paid_content);
    }

}
