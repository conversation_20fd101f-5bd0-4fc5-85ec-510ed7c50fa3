<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    //
    function login(Request $request)
    {
        $credentials = [
            'username' => $request->input('username'),
            'password' => $request->input('password'),
        ];
        $user = User::with('department_one:id,name', 'department_two:id,name')
            ->where('username', $credentials['username'])
            ->where('state', 1)
            ->first();
        if (empty($user)) {
            return LOGIN_FAIL_ARRAY;
        } else {
            if (Hash::check($credentials['password'], $user->password)) {
                $validator = Validator::make($credentials, [
                    'password' => 'required|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
                ], [
                    'password.regex' => '密码至少8位,必须包含大写字母、小写字母、数字',
                    'password.required' => '密码至少8位,必须包含大写字母、小写字母、数字',
                    'password.min' => '密码至少8位,必须包含大写字母、小写字母、数字',
                ]);
                if ($validator->fails()) {
                    return SUCCESS_RESPONSE_ARRAY(['api_token' => -1, 'user' => ['id' => $user->id]]);
                }

                $api_token = $user->api_token;
                $user->last_login_at = now();
                $user->save();
                return SUCCESS_RESPONSE_ARRAY(compact('user', 'api_token'));
            }
            return LOGIN_FAIL_ARRAY;
        }
    }

    function loginFail(Request $request)
    {
        return response()->json(['message' => 'token无效,请重新登录'], 401);
    }
}
