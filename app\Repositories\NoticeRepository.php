<?php

namespace App\Repositories;

use App\Http\Controllers\Applet\MiniAppController;
use App\Models\Attendee;
use App\Models\AttendeePickUp;
use App\Models\SmsNotice;
use App\Models\SmsSendLog;
use App\YuanBo\Sms\JianZhouSender;
use App\YuanBo\Support\Facades\SmsManagerFacade;
use EasyWeChat\MiniApp\Application;
use Illuminate\Http\Request;

class NoticeRepository
{
    // 指标列表
    public function smsListBuilder(Request $request)
    {
        // 会议ID
        $event_id = $request->input('event_id');
        // 通知内容
        $content = $request->input('content');
        // 发送类型
        $send_type = $request->input('send_type');
        // 状态
        $status = $request->input('status');

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        return SmsNotice::join('events', 'sms_notices.event_id', '=', 'events.id')
            ->select('sms_notices.*', 'events.title as event_title', 'events.short_title as event_short_title')
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('sms_notices.event_id', $event_id))
            ->when($content, fn($query) => $query->where('sms_notices.content', 'like', "%$content%"))
            ->when($send_type, fn($query) => $query->where('sms_notices.send_type', $send_type))
            ->when($status, fn($query) => $query->where('sms_notices.status', $status))
            ;
    }

    public function getSendData($filter)
    {
        $event_id = $filter['event_id'];
        $forum_id = $filter['forum_id'];
        $event_status = $filter['event_status'];
        $identity_type = $filter['identity_type'];
        $organization = $filter['organization'];
        $channel_id = $filter['channel_id'];
        $is_need_pick_up = $filter['is_need_pick_up'];
        $is_need_drop_off = $filter['is_need_drop_off'];
        $is_need_hotel = $filter['is_need_hotel'];
        $hotel_id = $filter['hotel_id'];
        $is_need_dine = $filter['is_need_dine'];
        $phone = $filter['phone'];

        return Attendee::join('events', 'attendees.event_id', '=', 'events.id')
            ->join('registrants', 'attendees.registrant_id', '=', 'registrants.id')
            ->leftJoin('attendee_forums', 'attendees.id', '=', 'attendee_forums.attendee_id')
            ->leftJoin('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
//            ->with(['isDine', 'isHotel', 'isPickUp', 'isDropOff'])
            ->select('attendees.*', 'events.title as event_title', 'events.short_title as event_short_title')
            ->when($event_id, fn($query) => $query->where('attendees.event_id', $event_id))
            ->when($forum_id, fn($query) => $query->where('attendee_forums.forum_id', $forum_id))
            ->when($event_status, fn($query) => $query->where('attendees.status', $event_status))
            ->when($identity_type, fn($query) => $query->where('attendees.identity', $identity_type))
            ->when($organization, fn($query) => $query->where('attendees.organization', $organization))
            ->when($channel_id, fn($query) => $query->where('registrants.channel_id', $channel_id))
            ->when($phone, fn($query) => $query->where('attendees.phone', $phone))
            // 是否就餐
            ->when($is_need_dine, function ($query) use ($is_need_dine) {
                // 需要
                if ($is_need_dine == 1)
                    return $query->whereHas('isDine');
                // 不需要
                if ($is_need_dine == 2)
                    return $query->whereDoesntHave('isDine');
            })
            // 是否住宿
            ->when($is_need_hotel, function ($query) use ($is_need_hotel, $hotel_id) {
                // 需要
                if ($is_need_hotel == 1){
                    if($hotel_id)
                        return $query->whereHas('isHotel', function ($query) use ($hotel_id) {
                            return $query->where('hotel_id', $hotel_id);
                        });

                    return $query->whereHas('isHotel');
                }
                // 不需要
                if ($is_need_hotel == 2)
                    return $query->whereDoesntHave('isHotel');
            })
            // 是否接站
            ->when($is_need_pick_up, function ($query) use ($is_need_pick_up) {
                // 需要
                if ($is_need_pick_up == 1)
                    return $query->whereHas('isPickUp');
                // 不需要
                if ($is_need_pick_up == 2)
                    return $query->whereDoesntHave('isPickUp');
            })
            // 是否送站
            ->when($is_need_drop_off, function ($query) use ($is_need_drop_off) {
                // 需要
                if ($is_need_drop_off == 1)
                    return $query->whereHas('isDropOff');
                // 不需要
                if ($is_need_drop_off == 2)
                    return $query->whereDoesntHave('isDropOff');
            })
            ;
    }


    // 新增短信通知
    public function smsStore(Request $request)
    {
        //wechat_path
        $wechat_path = $request->input('wechat_path');
        $url_link = null;
        if (!empty($wechat_path)) {
            $url_link = getAppletUrlLink($wechat_path);
        }
        $filter = $request->input('filter');
        $event_id = $filter['event_id'];
        if(!$event_id){
            return FAIL_RESPONSE_ARRAY("请选择会议");
        }
        $query = $this->getSendData($filter);
        $cnt = $query->count();
        if($cnt>1000){
            return FAIL_RESPONSE_ARRAY("筛选条数超过1000条，请调整筛选条件");
        }

        // 将对象转换为关联数组
        $myArray = (array) $filter;
        // 使用 array_filter 过滤掉空值
        $filteredArray = array_filter($myArray, function ($value) {
            return !is_null($value) && !empty($value);
        });

        $delay_time = $request->input('delay_time');
        $sign = $request->input('sign');
        $content = $request->input('content');

        //如果$url_link 不为空，则content后拼上‘，打开小程序：url_link’
        if(!empty($url_link)){
            $content .= "，打开小程序：" . $url_link;
        }

        $notice = new SmsNotice();
        $notice->type = 1; // 短信类型 1 短信通知 2 小程序通知
        $notice->status = 2; // 状态：1:已执行 2:未执行
        $notice->event_id = $event_id;
        $notice->sign = $sign;
        $notice->content = $content;
        $notice->wechat_path = $wechat_path;
        $notice->filter_json = json_encode($filteredArray);
        $notice->creator = $request->user()->real_name;

        $options = [];
        // 延迟发送
        if($delay_time){
            $notice->send_type = 2; // 发送类型：1:立即发送 2:延迟发送
            $notice->delay_time = $delay_time;
            $options = [
                'sendDateTime' => date('YmdHi', strtotime($delay_time)),
            ];
        }

        // 创建短信通知
        $smsNotice = SmsNotice::forceCreate($notice->toArray());

        // 发送短信
        if($smsNotice) {
            // 获取符合筛选范围内的参会人手机号数组
            $phones = $query->pluck("phone")->toArray();

            $rst = SmsManagerFacade::setSender(JianZhouSender::class)->batchSend($phones, $content, $sign, $options);
            if ($rst) {
                $smsNotice->task_id = $rst;
                $smsNotice->status = 1; // 状态：1:已执行 2:未执行
                $smsNotice->save();
            }

            $this->insertSmsSendLog($sign, $phones, $content, $rst);
        }

        return SUCCESS_RESPONSE_ARRAY(compact('smsNotice'));
    }


    // 创建短信发送记录
    public static function insertSmsSendLog($sign, $mobiles, $message, $task_id): void
    {
        $data = [];
        foreach ($mobiles as $mobile) {
            $data[] = [
                'sign' => $sign,
                'message' => $message,
                'task_id' => $task_id,
                'mobile' => $mobile,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }
        SmsSendLog::insert($data);
    }
}
