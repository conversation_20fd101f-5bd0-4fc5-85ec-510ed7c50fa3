<?php

namespace App\Http\Controllers\Admin\Serve;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\AttendeeDropOff;
use App\Models\AttendeePickUp;
use App\Models\User;
use App\Repositories\AttendeePickUpExport;
use App\Repositories\AttendeePickUpRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class AttendeePickUpController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, AttendeePickUpRepository $repository)
    {
        $query = $repository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('attendee_pick_ups.id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }
    // 导出
    public function export(Request $request, AttendeePickUpRepository $repository)
    {
        try {
            $list = $repository->listBuilder($request)->orderBy('attendee_pick_ups.id', 'desc')->get();
            $export = new AttendeePickUpExport($list);// 导出数据到 Excel 文件
            return Excel::download($export, '参会人接站名单服务列表.xlsx');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    // 获取参会人接站信息
    public function getAttendeePickUps(string $id)
    {
        return SUCCESS_RESPONSE_ARRAY(Attendee::find($id)->attendeePickUps()
            ->orderBy('id', 'desc')->get());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    //获取AttendeePickUp表中的所有的接站人person数据  去重
    public function getAttendeePickUpPerson()
    {
        $attendeePickUpPerson = AttendeePickUp::select('person as value', 'tel')->where('person' , '!=' , '')->where('tel' , '!=' , '')->distinct()->get();
        $attendeeDropOffPerson = AttendeeDropOff::select('person as value', 'tel')->where('person' , '!=' , '')->where('tel' , '!=' , '')->distinct()->get();
        $users = User::select('real_name as value', 'phone as tel')->where('real_name' , '!=' , '')->where('phone' , '!=' , '')->distinct()->get();
        // 将数组转换为集合
        $attendeePickUpPersonCollection = collect($attendeePickUpPerson);
        $attendeeDropOffPersonCollection = collect($attendeeDropOffPerson);
        $usersCollection = collect($users);
        // 合并两个集合
        $combined = $attendeePickUpPersonCollection->merge($attendeeDropOffPersonCollection)->merge($usersCollection);
        // 根据 'value' 和 'tel' 字段去重
        $uniquePersons = $combined->unique(function ($item) {
            return $item['value'] . $item['tel'];
        });
        return SUCCESS_RESPONSE_ARRAY( $uniquePersons->values()->toArray());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        $data = filterRequestData('attendee_pick_ups');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $attendeePickUp = AttendeePickUp::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('attendeePickUp'));
    }

    public function setMoreAttendeePickUp(Request $request)
    {
        $attendeeIds = $request->input('attendeeIds');
        if (empty($attendeeIds)) {
            return FAIL_RESPONSE_ARRAY('未选择参会人');
        }

        foreach ($attendeeIds as $attendee_id) {
            $attendee = Attendee::find($attendee_id);
            if (!$attendee) {
                return FAIL_RESPONSE_ARRAY('参会人不存在');
            }

            // 新增
            $data = filterRequestData('attendee_pick_ups');
            $data['attendee_id'] = $attendee_id;
            $data['creator'] = $request->user()->real_name;
            $data['updater'] = $request->user()->real_name;

            AttendeePickUp::forceCreate($data);
        }

        return SUCCESS_RESPONSE_ARRAY('设置成功');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $attendee_id = $request->input('attendee_id');
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return FAIL_RESPONSE_ARRAY('参会人不存在');
        }
        // 修改接站信息
        $attendeePickUp = AttendeePickUp::find($id);
        if (!$attendeePickUp) {
            return FAIL_RESPONSE_ARRAY('接站信息不存在');
        }
        $data = filterRequestData('attendee_pick_ups');
        $data['updater'] = $request->user()->real_name;
        $attendeePickUp->fill($data)->update();

        return SUCCESS_RESPONSE_ARRAY(compact('attendeePickUp'));
    }

    // 批量修改接站人信息
    public function setMorePickUpPerson(Request $request)
    {
        $attendeePickUpIds = $request->input('attendeePickUpIds');
        if (empty($attendeePickUpIds)) {
            return FAIL_RESPONSE_ARRAY('未选择接站信息');
        }

        $data = filterRequestData('attendee_pick_ups');
        $data['updater'] = $request->user()->real_name;
        // 循环$pickUpIds，设置is_need_pick_up
        foreach ($attendeePickUpIds as $attendeePickUpId) {
            $attendeePickUp = AttendeePickUp::find($attendeePickUpId);
            if (!$attendeePickUp) {
                return FAIL_RESPONSE_ARRAY('接站信息不存在');
            }
            $attendeePickUp->fill($data)->update();
        }

        return SUCCESS_RESPONSE_ARRAY("批量设置成功");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
        $attendeePickUp = AttendeePickUp::find($id);
        if (!$attendeePickUp) {
            return FAIL_RESPONSE_ARRAY('接站信息不存在');
        }
        $attendeePickUp->delete();
        return SUCCESS_RESPONSE_ARRAY("删除成功");
    }

    // 修改是否需要接站
    public function setIsNeedPickUp(Request $request, string $id)
    {
        $attendeePickUp = AttendeePickUp::find($id);
        if (!$attendeePickUp) {
            return FAIL_RESPONSE_ARRAY('接站信息不存在');
        }
        $attendeePickUp->is_need_pick_up = $attendeePickUp->is_need_pick_up == 1 ? 2 : 1;
        $attendeePickUp->updater = $request->user()->real_name;
        $attendeePickUp->update();
        return SUCCESS_RESPONSE_ARRAY(compact('attendeePickUp'));
    }

    // 批量修改是否需要接站
    public function setMoreIsNeedPickUp(Request $request)
    {
        $attendeePickUpIds = $request->input('attendeePickUpIds');
        $is_need_pick_up = $request->input('is_need_pick_up');
        if (empty($attendeePickUpIds)) {
            return FAIL_RESPONSE_ARRAY('未选择接站信息');
        }

        // 循环$pickUpIds，设置is_need_pick_up
        foreach ($attendeePickUpIds as $attendeePickUpId) {
            $attendeePickUp = AttendeePickUp::find($attendeePickUpId);
            if (!$attendeePickUp) {
                return FAIL_RESPONSE_ARRAY('接站信息不存在');
            }
            $attendeePickUp->is_need_pick_up = $is_need_pick_up;
            $attendeePickUp->updater = $request->user()->real_name;
            $attendeePickUp->update();
        }

        return SUCCESS_RESPONSE_ARRAY("批量设置成功");
    }
}
