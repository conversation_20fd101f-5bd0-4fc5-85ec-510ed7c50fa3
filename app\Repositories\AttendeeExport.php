<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/7
 * Time 9:20
 */

namespace App\Repositories;

use App\Models\Attendee;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AttendeeExport implements FromCollection, WithTitle, WithStyles, WithHeadings, WithColumnWidths
{
    use Exportable, RegistersEventListeners;

    protected $searchResults;
    public $count;
    public $is_view_phone;

    public function __construct(Collection $searchResults, bool $is_view_phone)
    {
        $this->searchResults = $searchResults;
        $this->count = $searchResults->count();
        $this->is_view_phone = $is_view_phone;
    }

    public function collection()
    {
        return $this->searchResults->map(function ($result) {

            try {// 数据转换示例：将 Organization Type 字段从数字转换为文字
                return [
                    $result['id'],
                    $result['event_short_title'],
                    $result['name'],
                    $result['gender'] ==1 ? '男' : '女',
                    $result['position'],
                    $this->is_view_phone ? $result['phone'] : RegistrantExport::hidePhoneNumber($result['phone']),
                    Attendee::convertIdentityType($result['identity']),
                    $result['organization'],
                    $result['organization_type'],
                    $result['organization_province'],
//                    Attendee::convertOrganizationType($result['organization_type']),
                    $result['isPickUp'] ? '需要' : '不需要',
                    $result['isDropOff'] ? '需要' : '不需要',
                    $result['check_in_status']==1 ? '已签到' : '未签到',
                    $result['isHotel'] ? '需要' : '不需要',
                    $result['isDine'] ? '需要' : '不需要',
                    $result['attendeeInterview'] ? '需要' : '不需要',
                    Attendee::convertStatus($result['status']),
                    $result['registrant_name'],
                    $result['registrant_phone'],
                    $result['real_name'],
                    $result['lastFollowMessage'] ? $result['lastFollowMessage']['created_at'] : '',
                ];
            } catch (\Exception $e) {
                return [];
            }
        });
    }

    public function headings(): array
    {
        // 返回Excel表头
        return [
            ['参会人服务信息列表'],
            [
                '编号',
                '会议标题',
                '参会人姓名',
                '性别',
                '职称职务',
                '参会人手机号',
                '身份类别',
                '所在单位',
                '单位类型',
                '单位省份',
                '接站',
                '送站',
                '签到状态',
                '酒店住宿',
                '就餐',
                '采访',
                '报名状态',
                '报名人',
                '报名人手机号',
                '对接人',
                '最后跟进时间',
            ]
        ];
    }

    public function title(): string
    {
        return '参会人服务信息列表';
    }

    //设置单元格样式

    public function styles(Worksheet $sheet)
    {
        // 合并单元格
        $sheet->mergeCells('A1:U1');
        // 设置字体颜色
        $sheet->getStyle('A1')->getFont()->getColor()->setRGB('000000');
        $sheet->getStyle('A1:U2')->applyFromArray([
            //设置水平居中
            'alignment' => ['horizontal' => 'center'],
            //设置字体加粗、大小
            'font'      => ['bold' => true, 'size' => 18],
            // 设置单元格背景颜色
            'fill'      => ['fillType' => 'solid', 'startColor' => ['rgb' => 'e9e9eb']],
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);
        $sheet->getStyle('A2:U2')->applyFromArray([
            //设置字体大小
            'font'      => ['size' => 12],
        ]);

        // 设置其他单元格边框
        $sheet->getStyle('A3:U' . ($this->count + 2))->applyFromArray([
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);
    }

    // 设置宽度
    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 30,
            'C' => 15,
            'D' => 15,
            'E' => 15,
            'F' => 15,
            'G' => 15,
            'H' => 10,
            'I' => 10,
            'J' => 10,
            'K' => 10,
            'L' => 10,
            'M' => 10,
            'N' => 10,
            'O' => 10,
            'P' => 15,
            'Q' => 15,
            'R' => 20,
            'S' => 20,
            'T' => 20,
            'U' => 20,
        ];
    }



}
