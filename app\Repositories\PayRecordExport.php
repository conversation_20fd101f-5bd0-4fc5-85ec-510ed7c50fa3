<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/7
 * Time 9:20
 */

namespace App\Repositories;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class PayRecordExport implements FromCollection, WithHeadings, WithEvents
{
    use Exportable, RegistersEventListeners;

    protected $searchResults;

    public function __construct(Collection $searchResults)
    {
        $this->searchResults = $searchResults;
    }

    public function collection()
    {
        return $this->searchResults->map(function ($result) {

            try {// 数据转换示例：将 Organization Type 字段从数字转换为文字
                $status = $this->convertStatusType($result['status']);
                return [
                    $result['id'],
                    $result['event_title'],
                    $result['pay_user'],
                    RegistrantExport::hidePhoneNumber($result['phone']),
                    $result['organization'],
                    $result['amount'],
                    $status,
                    $result['pay_time'],
                    $result['creator'],
                    $result['created_at'],
                    $result['updater'],
                    $result['updated_at'],
                ];
            } catch (\Exception $e) {
                return [];
            }
        });
    }

    public function headings(): array
    {
        // 返回Excel表头
        return [
            '编号',
            '会议名称',
            '缴费人',
            '联系方式',
            '所在单位',
            '缴费金额',
            '缴费状态',
            '缴费时间',
            '创建人',
            '创建时间',
            '更新人',
            '更新时间',
        ];
    }

    protected function convertStatusType($status)
    {
        // 缴费状态 1.待缴费 2.已缴费 3.已退款
        switch ($status) {
            case 1:
                return '待缴费';
            case 2:
                return '已缴费';
            case 3:
                return '已退款';
            default:
                return '无';
        }
    }

}
