<?php

namespace App\Traits;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExcelExportStyle implements FromCollection, WithColumnWidths, WithTitle, WithStyles
{
    public $data;
    public $header;
    public $title;
    public $dataTransformCallback;

    // 构造函数
    public function __construct($data, $header, $title, callable $dataTransformCallback = null)
    {
        $this->data = $data;
        $this->header = $header;
        $this->title = $title;
        $this->dataTransformCallback = $dataTransformCallback;
    }

    // 从集合中导出数据
    public function collection()
    {
        return new Collection($this->createData());
    }

    // 设置sheet名称
    public function title(): string
    {
        return $this->title;
    }

    // 设置宽度
    public function columnWidths(): array
    {
        // 初始化 letters 数组：['A', 'B', ..., 'Z']
        $letters = [];
        foreach (array_keys($this->header) as $key) {
            $letters[] = chr(ord('A') + array_search($key, array_keys($this->header)));
        }
        // 初始化 widths 数组
        $widths = array_combine($letters, array_column($this->header, 'width'));

        return $widths;
    }

    // 设置样式
    public function styles(Worksheet $sheet)
    {
        // 初始化 letters 数组：['A', 'B', ..., 'Z']
        $letters = [];
        foreach (array_keys($this->header) as $key) {
            $letters[] = chr(ord('A') + array_search($key, array_keys($this->header)));
        }
        // 获取最后一个元素的值
        $lastElement = end($letters);

        // 设置字体颜色
        $sheet->getStyle('A1')->getFont()->getColor()->setRGB('000000');
        $sheet->getStyle('A1:' . $lastElement . '1')->applyFromArray([
            //设置水平居中
            'alignment' => ['horizontal' => 'center'],
            //设置字体加粗、大小
            'font'      => ['bold' => true, 'size' => 14],
            // 设置单元格背景颜色
            'fill'      => ['fillType' => 'solid', 'startColor' => ['rgb' => 'e9e9eb']],
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);

        $sheet->getStyle('A2:' . $lastElement . '2')->applyFromArray([
            //设置字体大小
            'font'      => ['size' => 12],
        ]);

        // 设置其他单元格边框
        $sheet->getStyle('A2:' . $lastElement . '2')->applyFromArray([
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);
    }

    // 创建数据
    public function createData(): array
    {
        // 初始化新的数组
        $newData = [];
        // 获取 header 中的键和 name
        $keysAndNames = array_map(function ($value) {
            return $value['name'];
        }, $this->header);

        // 将 header 的键和 name 作为第一个数据
        $newData[] = $keysAndNames;
        // 获取 header 中的键
        $keys = array_keys($this->header);
        // 遍历 data 数组
        foreach ($this->data as $row) {
            // 应用数据转换回调函数
            if ($this->dataTransformCallback) {
                $row = ($this->dataTransformCallback)($row);
            }
            // 初始化一个临时数组
            $tempRow = [];
            // 遍历 header 中的键
            foreach ($keys as $key) {
                $tempRow[$key] = $row[$key] ?? '';
            }
            // 将临时数组加入新的数组
            $newData[] = $tempRow;
        }

        return $newData;
    }

}
