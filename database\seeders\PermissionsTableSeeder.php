<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * php artisan db:seed --class=PermissionsTableSeeder
     * SELECT SUBSTRING_INDEX(name, '.', 1)                              AS level1,
     * SUBSTRING_INDEX(SUBSTRING_INDEX(name, '.', 2), '.', -1)    AS level2,
     * SUBSTRING(name, LENGTH(SUBSTRING_INDEX(name, '.', 2)) + 2) AS level3,
     * name
     * from permissions;
     */
    public function run(): void
    {
        //
        Permission::create(['name'=>'system.users.list','display_name'=>'用户列表','guard_name'=>'api']);
        Permission::create(['name'=>'system.users.store','display_name'=>'用户添加','guard_name'=>'api']);
        Permission::create(['name'=>'system.users.update','display_name'=>'用户更新','guard_name'=>'api']);
        Permission::create(['name'=>'system.users.reset_password','display_name'=>'重置密码','guard_name'=>'api']);
        Permission::create(['name'=>'system.users.change_state','display_name'=>'修改用户状态','guard_name'=>'api']);

        Permission::create(['name'=>'system.roles.list','display_name'=>'角色列表','guard_name'=>'api']);
        Permission::create(['name'=>'system.roles.store','display_name'=>'角色添加','guard_name'=>'api']);
        Permission::create(['name'=>'system.roles.update','display_name'=>'角色更新','guard_name'=>'api']);
    }
}
