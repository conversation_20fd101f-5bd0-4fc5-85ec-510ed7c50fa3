<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Mail\CommonMail;
use App\Models\Website\Poster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Traits\ExcelExport;
use Maatwebsite\Excel\Facades\Excel;

class PostersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //根据状态查询海报
        $status = $request->input('status');
        //手机号
        $phone = $request->input('phone');
        $query = Poster::query();
        $query->when($status, fn($query) => $query->where('status', $status))->when($phone, fn($query) => $query->where('phone', $phone));
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    //导出海报联系方式
    public function export(Request $request)
    {
        try {
            //根据状态查询海报
            $status = $request->input('status');
            //手机号
            $phone = $request->input('phone');
            $query = Poster::query();
            $query->when($status, fn($query) => $query->where('status', $status))->when($phone, fn($query) => $query->where('phone', $phone))->get();
            $list = $query->orderBy('id', 'desc')->get();

            // 数据转换逻辑
            $dataTransformCallback = function ($row) {
                // 检查并解码 JSON，如果是 JSON 字符串
                if (is_string($row['author_info'])) {
                    $row['author_info'] = json_decode($row['author_info'], true);
                }
                $row['author_name'] = $row['author_info'][0]['author_name'] ?? '';
                $row['status'] = Poster::convertStatus($row['status']) ?? '';
                return $row;
            };

            $title = '海报联系人列表';
            $header = [
                'id' => '编号',
                'author_name' => '海报作者',
                'phone' => '海报联系人手机号',
                'email' => '海报联系人邮箱',
                'status' => '状态',
                'auditor' => '审核人',
                'audit_remark' => '审核备注',
                'audit_time' => '审核时间',
            ];

            return Excel::download(new ExcelExport($list, $header, $title, $dataTransformCallback), $title . date('YmdHis') . '.xls');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //前端传递word_url和作者集合（作者信息集合包含作者姓名author_name和picture）
        $word_url = $request->input('word_url');
        $author_info = $request->input('author_info');
        //手机号
        $phone = $request->input('phone');
        $email = $request->input('email');
        $poster = new Poster();
        $poster->word_url = $word_url;
        $poster->author_info = json_encode($author_info);
        $poster->phone = $phone;
        $poster->email = $email;
        $poster->status = 2;
        $poster->save();
        return SUCCESS_RESPONSE_ARRAY("提交成功");
    }

    //audit
//    public function audit(Request $request, $id)
//    {
//        $status = $request->input('status');
//        $poster = Poster::find($id);
//        $poster->status = $status;
//        $poster->auditor = $request->user()->real_name;
//        $poster->audit_time = date('Y-m-d H:i:s');
//        $poster->save();
//        return SUCCESS_RESPONSE_ARRAY("审核成功");
//    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }


    public function audit(Request $request, string $id)
    {
        $poster = Poster::find($id);
        if (!$poster) {
            return FAIL_RESPONSE_ARRAY('海报不存在');
        }


        $status = $request->input('status');
        //审核备注
        $audit_remark = $request->input('audit_remark');

        $poster->status = $status;
        $poster->audit_remark = $audit_remark;
        $poster->auditor = $request->user()->real_name;
        $poster->audit_time = date('Y-m-d H:i:s');
        $poster->update();


        $sms_content = '您好！感谢您提交的海报申请，海报'  . ($status==1?' 审核通过':' 未通过审核') . '。审核反馈：'. $audit_remark;
        //发送短信
        try {
            $email = $poster->email;
            $message_str = $sms_content;
            $mail = new CommonMail($message_str);
            Mail::mailer('smtp_sh')->send($mail->from('<EMAIL>', '上海市教育考试院')->to($email));
        } catch (\Exception $e) {
            Log::error('发送邮件失败 $e: '.$e);
            return FAIL_RESPONSE_ARRAY("发送邮件失败");
        }

        return SUCCESS_RESPONSE_ARRAY("审核成功");
    }

    //留言
    public function message(Request $request, string $id)
    {
        $poster = Poster::find($id);
        if (!$poster) {
            return FAIL_RESPONSE_ARRAY('海报不存在');
        }
        $message = $request->input('message');
        if (!$message) {
            return FAIL_RESPONSE_ARRAY('留言内容不能为空');
        }
        $poster->message = $message;
        $poster->update();

        try {
            $email = $poster->email;
            $message_str = $message;
            $mail = new CommonMail($message_str);
            Mail::mailer('smtp_sh')->send($mail->from('<EMAIL>', '上海市教育考试院')->to($email));
        } catch (\Exception $e) {
            Log::error('发送邮件失败 $e: '.$e);
            return FAIL_RESPONSE_ARRAY("发送邮件失败");
        }

        return SUCCESS_RESPONSE_ARRAY("留言成功");
    }

}
