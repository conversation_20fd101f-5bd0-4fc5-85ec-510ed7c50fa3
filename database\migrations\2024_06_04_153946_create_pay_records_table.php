<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pay_records', function (Blueprint $table) {
            $table->id();
            //会议名称
            $table->string('event_title')->nullable()->default('')->comment('会议名称');
            //缴费人
            $table->string('pay_user')->nullable()->default('')->comment('缴费人');
            //电话
            $table->string('phone')->nullable()->default('')->comment('电话');
            //单位
            $table->string('organization')->nullable()->default('')->comment('单位');
            //缴费金额
            $table->decimal('amount', 10, 2)->nullable()->default(0)->comment('缴费金额');
            //缴费状态
            $table->tinyInteger('status')->nullable()->default(1)->comment('缴费状态 1.待缴费 2.已缴费 3.已退款');
            //缴费时间
            $table->dateTime('pay_time')->nullable()->comment('缴费时间');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `pay_records` comment '缴费记录表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pay_records');
    }
};
