<?php

namespace App\YuanBo\Contracts\Sms;

interface SmsSender
{
    /**
     * 批量短信发送
     * @param array $mobiles
     * @param $message
     * @param $sign
     * @return mixed
     */
    public function batchSend(array $mobiles = [], $message = '', $sign = null, $options = []);

    /**
     *  发送单条短信
     * @param $mobile
     * @param $message
     * @param $sign
     * @return mixed
     */
    public function singleSend($mobile, $message = '', $sign = null, $options = []);

    public function templateSend($mobile, $templateCode, $templateParam, $signName);


    /**
     * 余额
     * @return mixed
     */
    public function balance();

    /**
     * 报告
     * @return mixed
     */
    public function report();
}
