<?php

namespace App\Http\Controllers\Admin\Event;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\InviteOrganization;
use App\Models\Registrant;
use App\Repositories\InviteOrganizationImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class InviteOrganizationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index($event_id, Request $request)
    {
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $organization_type= $request->input('organization_type');
        $province= $request->input('province');

        // 查询会议下的所有邀约单位 分页，并预加载参会人信息和统计参会人数
        $query = InviteOrganization::where('event_id', $event_id)
            ->when($organization, fn($query) => $query->where('organization', 'like', '%' . $organization . '%'))
            ->when($organization_code, fn($query) => $query->where('organization_code', 'like', '%' . $organization_code . '%'))
            ->when($organization_type, fn($query) => $query->where('organization_type', 'like', '%' . $organization_type . '%'))
            ->when($province, fn($query) => $query->where('province', 'like', '%' . $province . '%'))
//            ->with(['attendees' => function($query) use ($event_id) {
//                $query->where('event_id', $event_id)->where('status', Attendee::$status_ok);
//            }])  // 预加载参会人信息
            ->withCount(['attendees' => function($query) use ($event_id) {
                $query->where('event_id', $event_id)->where('status', Attendee::$status_ok);
            }]);  // 统计参会人数
        //查询所有结果，获取所有的number的和和attendees_count的和
        $collection = $query->get();
        //循环结果结果，获取所有的number的和和attendees_count的和
        $total_number = $collection->sum('number');
        $total_attendees_count = $collection->sum('attendees_count');
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt','total_number','total_attendees_count'));
    }

    public function attendeeOrganizationList(Request $request, $event_id)
    {
        $organization = $request->input('organization');
        $query = Attendee::where('attendees.event_id', $event_id)
            ->where('status', 1)
            ->where('organization', $organization);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    //查询会议下所有邀约单位
    public function all($event_id)
    {
        $invite_organizations = InviteOrganization::where('event_id', $event_id)->get();
        foreach ($invite_organizations as $invite_organization) {
            $organization = $invite_organization->organization;
            //查询参会人状态不等于2的
            $attendee_count = Attendee::where('event_id', $event_id)->where('organization', $organization)->whereIn('status', [1, 3])->count();
            $invite_organization->number -= $attendee_count;
        }
        //查询已报名参会人数量
        return SUCCESS_RESPONSE_ARRAY($invite_organizations);
    }

    //导入
    public function import($event_id, Request $request)
    {
        $file = $request->file('file');
        try {
            // 执行导入操作
            $import = new InviteOrganizationImport($event_id, $request);
            // 执行导入操作
            Excel::import($import, $file);
            // 删除导入文件中不存在的数据库记录
            $import->removeMissingData();

            // 获取导入结果
            $importResult = (object) [
                'importedData' => $import->getImportedData(),
                'failedData' => $import->getFailedData(),
                'updatedData' => $import->getUpdatedData(),
                'deletedData' => $import->getDeletedData(),
            ];
            return SUCCESS_RESPONSE_ARRAY($importResult);
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY("导入失败");
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //新增
        //如果已存在组织名称，则提示重复
        $event_id = $request->input('event_id');
        $organization = $request->input('organization');
        if (InviteOrganization::where('event_id',$event_id)
            ->where('organization', $organization)
            ->exists()) {
            return FAIL_RESPONSE_ARRAY("组织名称已存在");
        }
        $data = filterRequestData('invite_organizations');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $invite_organization = InviteOrganization::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY($invite_organization);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //更新
        $invite_organization = InviteOrganization::find($id);
        $oldOrganization = $invite_organization->organization;
        $oldCode = $invite_organization->organization_code;
        $oldType = $invite_organization->organization_type;
        $oldProvince = $invite_organization->province;
        $data = filterRequestData('invite_organizations');
        $data['updater'] = $request->user()->real_name;
        $invite_organization->fill($data)->save();
        //更新报名人、参会人
        $organization = $data['organization'];
        $newCode = $data['organization_code'];
        $newType = $data['organization_type'];
        $newProvince = $data['province'];

        if ($oldOrganization != $organization ||$oldCode != $newCode || $oldType != $newType || $oldProvince != $newProvince) {
            //查询报名人中的邀约单位，如果有邀请人，则更新其邀约单位信息
            $attendees = Attendee::where('event_id', $invite_organization->event_id)->where('organization', $oldOrganization)->get();
            $attendees->each(function ($attendee) use ($organization, $newType, $newCode, $newProvince) {
                $attendee->organization = $organization;
                $attendee->organization_type = $newType;
                $attendee->organization_code = $newCode;
                $attendee->organization_province = $newProvince;
                $attendee->save();
            });
            //更新Registrant
            $registrants = Registrant::where('event_id', $invite_organization->event_id)->where('organization', $oldOrganization)->get();
            $registrants->each(function ($registrant) use ($organization, $newType, $newCode, $newProvince) {
                $registrant->organization = $organization;
                $registrant->organization_type = $newType;
                $registrant->organization_code = $newCode;
                $registrant->organization_province = $newProvince;
                $registrant->save();
            });
        }

        return SUCCESS_RESPONSE_ARRAY($invite_organization);
    }

    /**
     * Remove the specified resource from storage.
     * 删除会议下的所有邀约组织
     */
    public function destroy(string $id)
    {
        // 删除用餐
        $inviteOrganization = InviteOrganization::find($id);
        if (!$inviteOrganization) {
            return FAIL_RESPONSE_ARRAY('组织不存在');
        }
        $inviteOrganization->delete();
        return SUCCESS_RESPONSE_ARRAY(compact('inviteOrganization'));
    }
}
