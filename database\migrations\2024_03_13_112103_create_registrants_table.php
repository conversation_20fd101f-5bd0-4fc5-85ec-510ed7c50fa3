<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('registrants', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->default(0)->comment('会议id');
            $table->unsignedInteger('member_id')->default(0)->comment('小程序用户id');
            $table->string('name')->default('')->comment('姓名');
            $table->string('phone')->default('')->comment('电话');
            $table->string('wechat')->default('')->comment('微信号');
            $table->string('organization')->default('')->comment('单位名称');
            $table->unsignedInteger('channel_id')->default(0)->comment('邀约渠道id');
            $table->string('organization_type')->default('0')->comment('单位类型');
            $table->string('organization_code')->default('0')->comment('单位代码');
            $table->tinyInteger('identity')->default(0)->comment('身份类别');
            $table->unsignedInteger('user_id')->default(0)->comment('对接人id');
            $table->string('assigner', 20)->default('')->comment('分配人');
            $table->timestamp('assign_at')->nullable()->comment('分配时间');
            $table->tinyInteger('audit_status')->default(2)->comment('审核状态1:已通过 2:待审核 3:已驳回');
            $table->timestamp('audit_time')->nullable()->comment('审核时间');
            $table->string('audit_remark')->default('')->comment('审核备注');
            $table->string('audit_user', 20)->default('')->comment('审核人');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `registrants` comment '报名人表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('registrants');
    }
};
