<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Website\EventPhoto;
use Illuminate\Http\Request;

//会议图片
class EventPhotoController extends Controller
{
    public function index(Request $request)
    {
        // 获取年份
        $year = $request->input('year');

        // 查询所有符合条件的数据，并按 forums_sort_order 排序
        $data = EventPhoto::query()
            ->where('year', $year)
            ->orderBy('forums_sort', 'asc') // 按排序权重排序
            ->orderBy('id', 'asc') // 若排序权重相同，则按 ID 排序
            ->get();

        // 按 forums 分组
        $groupedData = $data->groupBy('forums')->map(function ($items, $forum) {
            return [
                'forums' => $forum ?? '', // 若 forums 为空，设置为空字符串
                'children' => $items->toArray(), // 转为数组
            ];
        })->values(); // 重置索引
        return SUCCESS_RESPONSE_ARRAY($groupedData);
    }

    public function create()
    {
        // 显示创建 EventPhoto 的表单
    }

    public function store(Request $request)
    {
        // 保存新创建的 EventPhoto，包含标题、封面、副标题、添加人
        $photo = new EventPhoto();
        $photo->title = $request->input('title');
        $photo->cover = $request->input('cover');
        $photo->sub_title = $request->input('sub_title');
        $photo->creator = $request->user()->real_name;
        $photo->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    public function show($id)
    {
        // 显示特定 EventPhoto 的详细信息
        $photo = EventPhoto::find($id);
        return SUCCESS_RESPONSE_ARRAY($photo);
    }

    public function edit($id)
    {
        // 显示更新 EventPhoto 的表单
    }

    public function update(Request $request, $id)
    {
        // 更新 EventPhoto,包含标题、封面、副标题、修改人
        $photo = EventPhoto::find($id);
        $photo->title = $request->input('title');
        $photo->cover = $request->input('cover');
        $photo->sub_title = $request->input('sub_title');
        $photo->updater = $request->user()->real_name;
        $photo->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    public function destroy($id)
    {
        // 删除 EventPhoto
        $photo = EventPhoto::find($id);
        $photo->delete();
    }
}
