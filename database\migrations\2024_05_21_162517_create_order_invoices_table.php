<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_invoices', function (Blueprint $table) {
            $table->id();
            //userid
            $table->unsignedInteger('member_id')->default(0)->comment('userid');
            //订单id
            $table->unsignedInteger('order_id')->default(0)->comment('订单id');
            //发票抬头
            $table->string('title')->nullable()->default('')->comment('发票抬头');
            //发票税号
            $table->string('tax_no')->nullable()->default('')->comment('发票税号');
            //开票金额
            $table->decimal('amount', 10, 2)->default(0.00)->comment('开票金额');
            //发票类型
            $table->tinyInteger('type')->default(1)->comment('发票类型，1普通发票，2专用发票');
            //接收方式：1电子发票，2纸质发票
            $table->tinyInteger('receive_type')->default(1)->comment('接收方式：1电子发票，2纸质发票');
            //邮件地址
            $table->string('email')->nullable()->default('')->comment('邮件地址');
            //邮寄地址
            $table->string('address')->nullable()->default('')->comment('邮寄地址');
            //联系人
            $table->string('contact')->nullable()->default('')->comment('联系人');
            $table->string('phone')->nullable()->default('')->comment('手机号');
            //开票状态
            $table->tinyInteger('status')->default(1)->comment('开票状态，1已开票，2已退票');
            //开票人
            $table->string('invoice_user', 20)->nullable()->default('')->comment('开票人');
            //开票文件地址
            $table->string('invoice_url')->nullable()->default('')->comment('开票文件地址');
            //开票时间
            $table->dateTime('invoice_time')->nullable()->comment('开票时间');
            $table->string('creator', 20)->nullable()->default('')->comment('添加人');
            $table->string('updater', 20)->nullable()->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `order_invoices` comment '订单发票表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_invoices');
    }
};
