<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/18
 * Time 15:22
 */

namespace App\Repositories;

use App\Models\Event;
use Illuminate\Http\Request;

class EventRepository
{
    //根据会议开始时间，会议结束时间，会议标题，是否上下架，会议状态查询
    public function listBuilder(Request $request)
    {
        $title = $request->input('title');
        $event_time = $request->input('event_time');
        $is_on = $request->input('is_on');
        $status = $request->input('status');
        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $query = Event::query()
            ->whereIn('activity_type', $activityTypes);
        $query->when($title, fn($query) => $query->where('title', 'like', '%' . $title . '%'))
            ->when($is_on, fn($query) => $query->whereIsOn($is_on))
            ->when($event_time, function ($query) use ($event_time) {
                if (is_array($event_time) && count($event_time) === 2) {
                    $start_date = $event_time[0];
                    $end_date = $event_time[1];

                    // Adjust end_date to include the entire day
                    $start_date = date('Y-m-d 00:00:00', strtotime($start_date));
                    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));

                    $query->where('start_time', '>=', $start_date);
                    $query->where('end_time', '<=', $end_date);
                }
            });
        if ($status) {
            $query->where(function ($q) use ($status) {
                if ($status == 1) {
                    $q->where('start_time', '>', now());
                } elseif ($status == 2) {
                    $q->where('start_time', '<=', now())
                        ->where('end_time', '>=', now());
                } elseif ($status == 3) {
                    $q->where('end_time', '<', now());
                }
            });
        }
        return $query;
    }


}
