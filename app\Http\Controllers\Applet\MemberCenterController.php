<?php

namespace App\Http\Controllers\Applet;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Repositories\EventWayRepository;
use Illuminate\Http\Request;

class MemberCenterController extends Controller
{

    // 获取用户当前会议的签到码信息
    public function getMemberCheckInCodes(Request $request, EventWayRepository $repository)
    {
        return $repository->getMemberCheckInCodes($request);
    }

    // 获取用户参加的所有会议
    public function getMemberAllEvents(Request $request)
    {
        $member_id = $request->user()->id;
        // 获取当前用户作为参会人参加的所有会议
        $events1 = Event::join('attendees', 'events.id', '=', 'attendees.event_id')
            ->join('registrants', 'attendees.registrant_id', '=', 'registrants.id')
            ->select('events.title' , 'events.cover' , 'events.start_time' , 'events.end_time', 'events.address', 'events.longitude', 'events.latitude', 'events.status',
                'attendees.id as attendee_id','attendees.status as attendee_status', 'attendees.name as attendee_name', 'attendees.created_at as attendee_created_at', 'registrants.name as registrant_name')
            ->where('attendees.member_id', $member_id);

        // 获取当前用户作为报名人，取当前报名人下的参会人参加的所有会议
        $events2 = Event::join('attendees', 'events.id', '=', 'attendees.event_id')
            ->join('registrants', 'attendees.registrant_id', '=', 'registrants.id')
            ->select('events.title' , 'events.cover' , 'events.start_time' , 'events.end_time', 'events.address', 'events.longitude', 'events.latitude', 'events.status',
                'attendees.id as attendee_id','attendees.status as attendee_status', 'attendees.name as attendee_name', 'attendees.created_at as attendee_created_at', 'registrants.name as registrant_name')
            ->where('registrants.member_id', $member_id);

        $events = $events1->union($events2)->get();

        return SUCCESS_RESPONSE_ARRAY($events);
    }
}
