<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Mail\AttachmentMail;
use App\Models\Order;
use App\Models\OrderInvoice;
use App\Repositories\OrderRepository;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, OrderRepository $orderRepository)
    {
        //
        $query = $orderRepository->invoiceListBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }
    //查询我的发票列表
    public function myInvoiceList(Request $request)
    {
        $orderInvoices = OrderInvoice::query()
            ->leftJoin('orders', 'orders.id', '=', 'order_invoices.order_id')
            ->leftJoin('attendees', 'attendees.id', '=', 'orders.attendee_id')
            ->leftJoin('events', 'events.id', '=', 'attendees.event_id')
            ->select('order_invoices.*', 'attendees.name as attendee_name', 'events.short_title as event_title', 'events.cover as event_cover', 'orders.fee_info')
            ->where('order_invoices.member_id', $request->user()->id)->get();
        //循环list给cover添加前缀config('filesystems.disks.sftp.domain')
        $orderInvoices->map(function ($item) {
            $item->event_cover = config('filesystems.disks.sftp.domain') . $item->event_cover;
        });
        return SUCCESS_RESPONSE_ARRAY($orderInvoices);
    }

    //confirmRefund
    public function confirmRefund(Request $request, string $id)
    {
        //获取发票信息
        $orderInvoice = OrderInvoice::find($id);
        $orderInvoice->status = $request->input('status');
        $orderInvoice->updater = $request->user()->real_name;
        $orderInvoice->save();
        return SUCCESS_RESPONSE_ARRAY($orderInvoice);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //获取order_id
        $order_id = $request->input('order_id');
        //查询这个订单的所有已开票金额是否大于订单金额 开票状态是1和2的
        $orderInvoice = OrderInvoice::query()->where('order_id', $order_id)->whereIn('status', [1, 2])->sum('amount');
        //查询订单金额
        $order = Order::query()->where('id', $order_id)->first();
        if ($orderInvoice >= $order->amount) {
            return ERROR_RESPONSE_ARRAY('当前订单发票已全部开具');
        }
        $data = filterRequestData('order_invoices');
        $data['member_id'] = $request->user()->id;
        $data['status'] = 2;
        $data['creator'] = "客户id：" . $request->user()->id . ' 手机号：' . $request->user()->phone;
        $orderInvoice = OrderInvoice::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY($orderInvoice);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
        $orderInvoice = OrderInvoice::find($id);
        $orderInvoice->fill(filterRequestData('order_invoices'))->save();
        return SUCCESS_RESPONSE_ARRAY($orderInvoice);
    }

    //开发票
    public function uploadInvoice(Request $request, string $id)
    {
        $orderInvoice = OrderInvoice::find($id);
        $orderInvoice->invoice_url = $request->input('invoice_url');
        $orderInvoice->invoice_user = $request->user()->real_name;
        $orderInvoice->invoice_time = now();
        $orderInvoice->status = 1;
        $orderInvoice->save();
        return SUCCESS_RESPONSE_ARRAY($orderInvoice);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    //sendInvoice
    public function sendInvoice(Request $request, string $id)
    {
        //获取邮箱
        $orderInvoice = OrderInvoice::find($id);
        $email = $request->input('email');
        //检查邮箱是否符合邮箱格式
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return FAIL_RESPONSE_ARRAY('邮箱格式不正确!');
        }
        if ($email) {
            $orderInvoice->email = $email;
            $orderInvoice->receive_type = 1;
            $orderInvoice->save();
            //发送邮件
            $invoice_url = $orderInvoice->invoice_url;
            $client = new Client();
            $response = $client->head($invoice_url);
            $mimeType = $response->getHeaderLine('Content-Type');
            try {
                Mail::to($email)->send(new AttachmentMail($invoice_url, $mimeType));
            } catch (\Exception $e) {
                Log::error('发送邮件失败 $e: '.$e);
                return FAIL_RESPONSE_ARRAY("发送邮件失败");
            }
        }
        return SUCCESS_RESPONSE_ARRAY('邮件发送成功!');
    }
}
