<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 *
 *
 * @property int $id
 * @property int $event_id 会议id
 * @property string $name 论坛名称
 * @property string $cover 论坛封面
 * @property string|null $start_time 论坛开始时间
 * @property string|null $end_time 论坛结束时间
 * @property string $address 论坛地点
 * @property int $is_enroll 是否可报名，1:可报名，2:不可报名
 * @property int $is_audit 是否需要审核，1:需要，2:不需要
 * @property int $is_on 是否上架，1:上架，2:下架
 * @property string|null $description 论坛描述
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attendee> $attendees
 * @property-read int|null $attendees_count
 * @property-read \App\Models\Event|null $event
 * @method static \Illuminate\Database\Eloquent\Builder|Forum newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Forum newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Forum pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|Forum query()
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereCover($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereIsAudit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereIsEnroll($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereIsOn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Forum whereUpdater($value)
 * @mixin \Eloquent
 */
class Forum extends Model
{
    use HasFactory, PaginationTrait;

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 定义一个访问器，处理 cover 字段的前缀添加逻辑
    public function getCoverAttribute($value)
    {
        return config('filesystems.disks.sftp.domain') . $value;
    }

    public function setCoverAttribute($value)
    {
        // 假设 config('filesystems.disks.sftp.domain') 返回的是前缀
        $prefix = config('filesystems.disks.sftp.domain');

        // 检查 $value 是否以前缀开头
        if (strpos($value, $prefix) === 0) {
            // 如果是，则去掉前缀
            $value = substr($value, strlen($prefix));
        }

        // 设置 cover 属性
        $this->attributes['cover'] = $value;
    }

    //论坛属于会议
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class, 'event_id');
    }

    //论坛下可以有多个参会人
    public function attendees()
    {
        return $this->belongsToMany(Attendee::class, 'attendee_forums', 'forum_id', 'attendee_id')
            ->withPivot('audit_status');
    }

    public function reviews()
    {
        return $this->hasMany(AttendeeForums::class);
    }

    //对应多个attendeeForums
//    public function attendeeForums()
//    {
//        return $this->hasMany(AttendeeForums::class, 'forum_id');
//    }
}
