<?php

namespace App\Repositories;

use App\Models\Kpi;
use App\Models\Website\Speech;
use Illuminate\Http\Request;

class SpeechRepository
{
    // 指标列表
    public function listBuilder(Request $request)
    {
        $status = $request->input('status');
        $phone = $request->input('phone');

        $builder = Speech::when($status, fn($query) => $query->where('status', $status))
            ->when($phone, fn($query) => $query->where('phone', $phone));

        $cnt = $builder->count();
        $list = $builder->pagination()->orderBy('id', 'desc')->get();

        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    public function index(Request $request)
    {
        $status = $request->input('status');
        $phone = $request->input('phone');

        $builder = Speech::when($status, fn($query) => $query->where('status', $status))
            ->when($phone, fn($query) => $query->where('phone', $phone));

        $list = $builder->orderBy('id', 'desc')->get();

        return SUCCESS_RESPONSE_ARRAY(compact('list'));
    }

}
