<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invite_organizations', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->default(0)->comment('会议id');
            //单位code
            $table->string('organization_code')->default('')->comment('单位code');
            $table->string('organization')->default('')->comment('单位名称');
            //单位类型
            $table->string('organization_type')->default('')->comment('单位类型');
            $table->string('province')->default('')->comment('省份');
            $table->unsignedInteger('number')->default(0)->comment('人数');
            $table->string('creator', 20)->nullable()->comment('添加人');
            $table->string('updater', 20)->nullable()->comment('最后更新人');
            $table->softDeletes();
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `invite_organizations` comment '邀约单位表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invite_organizations');
    }
};
