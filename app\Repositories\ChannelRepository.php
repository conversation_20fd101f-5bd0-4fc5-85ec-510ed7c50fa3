<?php

namespace App\Repositories;

use App\Models\Channel;
use App\Models\Department;
use Illuminate\Http\Request;

class ChannelRepository
{
    // 渠道列表
    public function listBuilder(Request $request)
    {
        $departments = $request->input('departments');
        $department_one_id = '';
        $department_two_id = '';
        if ($departments) {
            $department_one_id = $departments[0];
            // 判断$departments的长度
            if( count($departments) > 1){
                $department_two_id = $departments[1];
            }
        }
        $name = $request->input('name');
        $status = $request->input('status');

        // 根据检索条件查询获取列表数据
        $builder = Channel::when($name, fn($query) => $query->where('name', 'like', '%' . $name . '%'))
            ->when($status, fn($query) => $query->where('status', $status))
            ->when($department_one_id, fn($query) => $query->where('department_one_id', $department_one_id))
            ->when($department_two_id, fn($query) => $query->where('department_two_id', $department_two_id));

        $cnt = $builder->count();
        // 对列表数据进行处理：关联部门表的id,name,parent_id字段，设置附属属性部门名称
        $list = $builder->pagination()
            ->with(['department:id,name,parent_id'])
            ->orderBy('id', 'desc')->get()
            ->each(function (&$item) {
                // 获取所有的父级
                $department_arr = $item->department->allParentNames()->toArray();
                // 获取的父级部门名称数组 倒序
                $department_arr = array_reverse($department_arr, true);
                $department_str = '';
                // 拼接所有的父级
                foreach ($department_arr as $value) {
                    $department_str .= $value['name'] . '-';
                }
                // 当前部门父级部门门全称
                $item->parent_department_name = removeLastChar($department_str);
                // 当前部门名称
                $item->department_name = $item->department->name;
                $item->parent_department_id = $item->department->parent_id;

                // 去除department属性
                unset($item->department);
            });

        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    public function dropChannelList(Request $request)
    {
        //查询所有的父级部门 value为部门id label为部门名称
        $departments = Department::where('status', 1)->where('parent_id', 0)->get();
        //查询父级部门下的子部门，并塞到$departments的children，把当前父级部门也放到children
        $departments->each(function ($department) {

            // 查询当前父级部门下的子部门
            $children = Department::where('status', 1)->where('parent_id', $department->id)->get();

            // 遍历子部门，为每个子部门查询并赋值其子用户
            $children = $children->map(function ($child) {
                $channels = Channel::query()->where('department_two_id', $child->id)->get();
                // 将查询到的渠道作为子部门的children
                $child->children = $channels->map(function ($channel) use ($child) {
                    return [
                        'value' => $child->id . '.' . $channel->id,
                        'label' => $channel->name,
                        // 这里可以添加更多用户字段
                    ];
                });
                return [
                    'value' => 'two.' . $child->id,
                    'label' => $child->name,
                    'children' => $child->children,
                    // 这里可以添加更多部门字段
                ];
            });

            // 查询当前父级部门的直接用户
            $channels = Channel::query()
                ->where('department_one_id', $department->id)
                ->whereNull('department_two_id')
                ->get();
            // 将直接用户作为当前父级部门的children
            $department->children = $channels->map(function ($channel) use ($department) {
                return [
                    'value' => $department->id . '.' . $channel->id,
                    'label' => $channel->name,
                    // 这里可以添加更多用户字段
                ];
            })->concat($children->map(function ($child) {
                return [
                    'value' => $child['value'],
                    'label' => $child['label'],
                    'children' => $child['children'],
                ];
            }));
        });

        // 返回最终结果
        $result = $departments->map(function ($department) {
            return [
                'value' => 'one.' . $department->id,
                'label' => $department->name,
                'children' => $department->children,
            ];
        });
        // 添加一个额外的父级项到结果的最后
        $result->push([
            'value' => '0.0',
            'label' => '无邀约渠道'
        ]);
        return $result;
        // 返回最终结果
//        return $departments->map(function ($department) {
//            return [
//                'value' => 'one.' . $department->id,
//                'label' => $department->name,
//                'children' => $department->children,
//            ];
//        });
    }


}
