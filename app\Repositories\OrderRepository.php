<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/5/15
 * Time 10:53
 */

namespace App\Repositories;

use App\Models\Order;
use App\Models\OrderInvoice;
use App\Models\RefundOrder;
use Illuminate\Http\Request;

class OrderRepository
{
    public function listBuilder(Request $request)
    {
        $event_id = $request->input('event_id');
        //参会人姓名
        $attendee_name = $request->input('attendee_name');
        //单位
        $organization = $request->input('organization');
        $phone = $request->input('phone');
        //费用类型
        $type = $request->input('type');
        $pay_status = $request->input('pay_status');
        //订单编号
        $order_no = $request->input('order_no');

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        return Order::query()->leftJoin('members', 'members.id', '=', 'orders.member_id')
            ->leftJoin('attendees', 'attendees.id', '=', 'orders.attendee_id')
            ->leftJoin('events', 'attendees.event_id', '=', 'events.id')
            ->select(
                'orders.*',
                'members.phone as member_phone',
                'members.nick_name as member_nick_name',
                'events.id as event_id',
                'events.title as event_title',
                'events.short_title as event_short_title',
                'events.activity_type',
                'attendees.name as attendee_name',
                'attendees.phone as attendee_phone',
                'attendees.organization as organization'
            )
            ->whereIn('events.activity_type', $activityTypes)
            ->when($attendee_name, function ($query) use ($attendee_name) {
                $query->where('attendees.name', 'like', "%{$attendee_name}%");
            })
            ->when($order_no, function ($query) use ($order_no) {
                $query->where('orders.order_no', 'like', "%{$order_no}%");
            })
            // 会议ID检索
            ->when($event_id, function ($query) use ($request) {
                $query->whereIn('events.id', $request->input('event_id'));
            })
            ->when($organization, function ($query) use ($organization) {
                $query->where('attendees.organization', 'like', "%{$organization}%");
            })
            ->when($phone, function ($query) use ($phone) {
                $query->where('attendees.phone', 'like', "%{$phone}%");
            })
            ->when($type, function ($query) use ($type) {
                $query->where('orders.type', $type);
            })
            ->when($pay_status, function ($query) use ($pay_status) {
                $query->where('orders.pay_status', $pay_status);
            });
    }

    //退费订单列表refundListBuilder
    public function refundListBuilder(Request $request)
    {
        $event_id = $request->input('event_id');
        //参会人姓名
        $attendee_name = $request->input('attendee_name');
        //单位
        $organization = $request->input('organization');
        $phone = $request->input('phone');
        //退费状态
        $status = $request->input('status');
        //订单编号
        $order_no = $request->input('order_no');
        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);
       return RefundOrder::query()->leftJoin('members', 'members.id', '=', 'refund_orders.member_id')
           ->leftJoin('orders', 'refund_orders.order_id', '=', 'orders.id')
           ->leftJoin('attendees', 'attendees.id', '=', 'orders.attendee_id')
           ->leftJoin('events', 'attendees.event_id', '=', 'events.id')
           ->select(
               'refund_orders.*',
               'members.id as member_id',
               'members.phone as member_phone',
               'events.id as event_id',
               'events.title as event_title',
               'events.short_title as event_short_title',
               'events.activity_type',
               'attendees.name as attendee_name',
               'attendees.organization as organization',
               'orders.order_no as order_no',
               'orders.pay_type as pay_type',
           )
           ->whereIn('events.activity_type', $activityTypes)
           ->when($attendee_name, function ($query) use ($attendee_name) {
               $query->where('attendees.name', 'like', "%{$attendee_name}%");
           })
           ->when($order_no, function ($query) use ($order_no) {
               $query->where('orders.order_no', 'like', "%{$order_no}%");
           })
           // 会议ID检索
           ->when($event_id, function ($query) use ($request) {
               $query->whereIn('events.id', $request->input('event_id'));
           })
           ->when($organization, function ($query) use ($organization) {
               $query->where('attendees.organization', 'like', "%{$organization}%");
           })
           ->when($phone, function ($query) use ($phone) {
               $query->where('members.phone', 'like', "%{$phone}%");
           })
           ->when($status, function ($query) use ($status) {
               $query->where('refund_orders.status', $status);
           });
   }

   //invoiceListBuilder
    public function invoiceListBuilder(Request $request)
    {
        $event_id = $request->input('event_id');
        //参会人姓名
        $attendee_name = $request->input('attendee_name');
        //单位
        $organization = $request->input('organization');
        $phone = $request->input('phone');
        //发票状态
        $status = $request->input('status');
        //订单编号
        $order_no = $request->input('order_no');
        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);
        return OrderInvoice::query()
            ->leftJoin('orders', 'orders.id', '=', 'order_invoices.order_id')
            ->leftJoin('members', 'members.id', '=', 'order_invoices.member_id')
            ->leftJoin('attendees', 'attendees.id', '=', 'orders.attendee_id')
            ->leftJoin('events', 'attendees.event_id', '=', 'events.id')
            ->select(
                'order_invoices.*',
                'members.id as member_id',
                'members.phone as member_phone',
                'events.id as event_id',
                'events.title as event_title',
                'events.short_title as event_short_title',
                'events.activity_type',
                'attendees.name as attendee_name',
                'attendees.organization as organization',
                'orders.order_no as order_no'
            )
            ->whereIn('events.activity_type', $activityTypes)
            ->when($attendee_name, function ($query) use ($attendee_name) {
                $query->where('attendees.name', 'like', "%{$attendee_name}%");
            })
            ->when($order_no, function ($query) use ($order_no) {
                $query->where('orders.order_no', 'like', "%{$order_no}%");
            })
            // 会议ID检索
            ->when($event_id, fn($query) => $query->whereIn('events.id', $event_id))
            ->when($organization, function ($query) use ($organization) {
                $query->where('attendees.organization', 'like', "%{$organization}%");
            })->when($phone, function ($query) use ($phone) {
                $query->where('members.phone', 'like', "%{$phone}%");
            })->when($status, function ($query) use ($status) {
                $query->where('order_invoices.status', $status);
            });
    }
}
