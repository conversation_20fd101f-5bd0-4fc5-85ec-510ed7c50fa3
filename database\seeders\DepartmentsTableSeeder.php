<?php

namespace Database\Seeders;

use App\Models\Department;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DepartmentsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //collection
        $department = Department::forceCreate([
            'name' => '教育平台事业部',
            'code' => '01',
            'creator' => '系统管理员',
            'updater' => '系统管理员',
        ]);

        $data = [
            [
                'parent_id' => $department->id,
                'name' => '华东大区',
                'code' => '0101',
                'creator' => '系统管理员',
                'updater' => '系统管理员',
                'created_at' => date('Y-m-d H:i:s',time()),
                'updated_at' => date('Y-m-d H:i:s',time()),
            ],
            [
                'parent_id' => $department->id,
                'name' => '华中大区',
                'code' => '0102',
                'creator' => '系统管理员',
                'updater' => '系统管理员',
                'created_at' => date('Y-m-d H:i:s',time()),
                'updated_at' => date('Y-m-d H:i:s',time()),
            ]
        ];
        DB::table('departments')->insert($data);

    }
}
