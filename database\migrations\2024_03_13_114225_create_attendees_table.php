<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendees', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('event_id')->default(0)->comment('会议id');
            $table->unsignedInteger('member_id')->default(0)->comment('小程序用户id');
            $table->unsignedInteger('registrant_id')->nullable(false)->default(0)->comment('报名人id');
            $table->string('name')->nullable(false)->default('')->comment('姓名');
            $table->string('wechat')->default('')->comment('微信号');
            $table->string('phone')->nullable(false)->default('')->comment('电话');
            $table->tinyInteger('gender')->nullable(false)->default(3)->comment('性别, 1:男 2:女 3:未知');
            $table->tinyInteger('identity')->default(0)->comment('身份类别（和ieic后台保持一致）');
            $table->string('organization')->default('')->comment('单位名称');
            $table->string('organization_type')->default('')->comment('单位类型');
            $table->string('organization_code')->default('')->comment('单位代码');
            $table->string('position')->default('')->comment('职称职务');
            $table->tinyInteger('status')->default(3)->comment('报名状态：1:已报名 2:已取消 3:待确认');
            $table->tinyInteger('status_remark')->nullable()->default('')->comment('报名状态备注');
            $table->tinyInteger('check_in_status')->default(2)->comment('签到状态：1:已签到 2:未签到');
            $table->timestamp('check_in_time')->nullable()->comment('签到时间');
            $table->unsignedInteger('user_id')->default(0)->comment('对接人id');
            $table->unsignedInteger('channel_id')->nullable()->default(0)->comment('邀约渠道id');
            //批次
            $table->unsignedInteger('batch')->nullable()->default(0)->comment('报名批次');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `attendees` comment '参会人表'");

        Schema::create('attendee_forums', function (Blueprint $table){
            $table->id();
            $table->unsignedInteger('event_id')->default(0)->comment('会议id');
            $table->unsignedInteger('forum_id')->default(0)->comment('论坛id');
            $table->unsignedInteger('attendee_id')->default(0)->comment('参会人id');
            $table->tinyInteger('audit_status')->default(2)->comment('审核状态1:已通过 2:待审核 3:已驳回');
            $table->timestamp('audit_time')->nullable()->comment('审核时间');
            $table->string('audit_remark')->default('')->comment('审核备注');
            $table->string('audit_user', 20)->default('')->comment('审核人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `attendee_forums` comment '参会人论坛关系表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendees');
        Schema::dropIfExists('attendee_forums');
    }
};
