<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use Illuminate\Http\Request;

class CheckInController extends Controller
{

    public function index(Request $request)
    {
        $event_id = $request->input('event_id');
        $attendee_id = 0;
        $checkInStr = $request->input('checkInStr');
        // 解码字符串
        $str = encryptDecrypt(true, $checkInStr);

        // 判断$str包含event字符串
        if (strpos($str, 'event') !== false) { // 会议签到
            // 找到第一个冒号的位置
            $colonPos = strpos($str, ':');
            if ($colonPos !== false) {
                // 从冒号后一个位置开始截取字符串
                $attendee_id = substr($str, $colonPos + 1);
            }
            return $this->eventCheckIn($event_id, $attendee_id);
        } else if (strpos($str, 'dine') !== false) { // 用餐签到
            return SUCCESS_RESPONSE_ARRAY('签到失败：用餐二维码签到待实现！');
        } else { // 默认手动输入手机号会议签到
            return $this->checkInByPhone($request);
        }
    }


    public function eventCheckIn($event_id, $attendee_id)
    {
        $attendee = Attendee::find($attendee_id);
        if (!$attendee) {
            return SUCCESS_RESPONSE_ARRAY('签到失败：参会人不存在！');
        }

        if($attendee->event_id != $event_id){
            return SUCCESS_RESPONSE_ARRAY('签到失败：参会人签到所属会议错误！');
        }

        $attendee->check_in_status = 1;
        $attendee->check_in_time = now();
        $attendee->check_in_type = 1; // 签到类型：1:扫码仪 2:现场签到 3:纸质签到
        $result = $attendee->update();
        if(!$result){
            return SUCCESS_RESPONSE_ARRAY('签到失败：会场扫码签到逻辑错误！');
        }

        $list[] = $attendee;
        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 根据手机号签到
    public function checkInByPhone(Request $request)
    {
        $event_id = $request->input('event_id');
        $phone = $request->input('checkInStr');

        // 校验手机号是否合法
        if (!checkMobile($phone)) {
            return SUCCESS_RESPONSE_ARRAY('签到失败：手机号不合法！');
        }

        $attendees = Attendee::where('event_id', $event_id)->where('phone', $phone)->where('status', 1)->get();
        // 判断$attendees是否有值
        if ($attendees->isEmpty()) {
            return SUCCESS_RESPONSE_ARRAY('签到失败：已报名的参会人里未查询到该手机号！');
        }

        // 循环遍历，判断是否签到
        foreach ($attendees as $attendee) {
            if ($attendee->check_in_status != 1) {
                $attendee->check_in_status = 1;
                $attendee->check_in_time = now();
                $attendee->check_in_type = 2; // 签到类型：1:扫码仪 2:现场签到 3:纸质签到
                $result = $attendee->update();
                if(!$result){
                    return SUCCESS_RESPONSE_ARRAY('签到失败：手机号现场签到逻辑错误！');
                }
            }
        }

        foreach ($attendees as $attendee) {
            // 替换手机号中间四位为*
            $attendee->phone = substr_replace($attendee->phone, '****', 3, 4);
        }

        return SUCCESS_RESPONSE_ARRAY($attendees);
    }

    // 根据参会人姓名获取单位列表信息
    public function getOrganizationList(Request $request)
    {
        $event_id = $request->input('event_id');
        $name = $request->input('name');

        $organizations = Attendee::where('event_id', $event_id)
            ->where('status', 1)
            ->where('name', $name)
//            ->get();
            ->pluck('organization')
            ->unique();

        return SUCCESS_RESPONSE_ARRAY($organizations);
    }

    // 根据姓名单位会议签到
    public function checkInByName(Request $request)
    {
        $event_id = $request->input('event_id');
        $name = $request->input('name');
        $organization = $request->input('organization');

        $attendee = Attendee::where('event_id', $event_id)
            ->where('status', 1)
            ->where('name', $name)
            ->where('organization', $organization)
            ->first();
        // 判断$attendee是否有值
        if (!$attendee) {
            return SUCCESS_RESPONSE_ARRAY('签到失败：已报名的参会人里未查询到该“姓名"，请核对姓名是否正确！');
        }

        if ($attendee->check_in_status != 1) {
            $attendee->check_in_status = 1;
            $attendee->check_in_time = now();
            $attendee->check_in_type = 2; // 签到类型：1:扫码仪 2:现场签到 3:纸质签到
            $result = $attendee->update();
            if(!$result){
                return SUCCESS_RESPONSE_ARRAY('签到失败：手机号现场签到逻辑错误！');
            }
        }

        // 替换手机号中间四位为*
        $attendee->phone = substr_replace($attendee->phone, '****', 3, 4);

        $list[] = $attendee;
        return SUCCESS_RESPONSE_ARRAY($list);
    }
}
