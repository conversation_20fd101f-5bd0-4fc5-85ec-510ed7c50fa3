<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use App\Repositories\MenuRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class MenuController extends Controller
{
    //
    public function index()
    {

    }

    /**
     * 一级菜单
     * @return array
     */
    public function categoryOne()
    {
        $list = Menu::where('parent_id', 0)->select('id', 'menu_name')->get()->toArray();
        array_unshift($list, ['id' => 0, 'menu_name' => '顶级菜单']);
        return SUCCESS_RESPONSE_ARRAY($list);
    }

    public function list(Request $request, MenuRepository $menuRepository)
    {
        $query = $menuRepository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')
            ->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));

    }

    public function store(Request $request)
    {
        $data = filterRequestData('menus');
        if ($data['parent_id'] == 0) {
            $data['level'] = 1;
        } else {
            $data['level'] = 2;
        }
        $data['updater'] = $request->user()->real_name;
        $data['creator'] = $request->user()->real_name;
        $rst = Menu::create($data);
        return SUCCESS_RESPONSE_ARRAY($rst->id);

    }

    public function update(Request $request, $id)
    {
        $menu = Menu::find($id);
        $data = filterRequestData('menus');
        $data['updater'] = $request->user()->real_name;
        $rst = $menu->fill($data)->save();
        return SUCCESS_RESPONSE_ARRAY($rst);
    }

    public function userSideBar(Request $request)
    {
        $roles = $request->user()->roles;
        $menus = Menu::whereHas('roles', function ($query) use ($roles) {
            $query->whereIn('role_id', $roles->pluck('id'));
        })->orderBy('sort', 'asc')->get();
        $result = $menus->filter(fn($item) => $item->level == 1)
            ->each(function ($item) use ($menus) {
                $item->children = $menus->filter(fn($sub) => $sub->level == 2 && $sub->parent_id == $item->id)->values();
            });

        return SUCCESS_RESPONSE_ARRAY($result);
    }
}
