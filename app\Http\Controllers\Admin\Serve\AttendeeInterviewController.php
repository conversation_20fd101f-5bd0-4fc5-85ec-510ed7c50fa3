<?php

namespace App\Http\Controllers\Admin\Serve;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\AttendeeInterview;
use App\Repositories\AttendeeInterviewExport;
use App\Repositories\AttendeeInterviewsRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class AttendeeInterviewController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, AttendeeInterviewsRepository $repository)
    {
        $query = $repository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('attendee_interviews.id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 导出
    public function export(Request $request, AttendeeInterviewsRepository $repository)
    {
        try {
            $list = $repository->listBuilder($request)->orderBy('attendee_interviews.id', 'desc')->get();
            $export = new AttendeeInterviewExport($list);// 导出数据到 Excel 文件
            return Excel::download($export, '参会人采访名单服务列表.xlsx');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    // 根据是否有ID，新增或修改采访信息
    public function save(Request $request)
    {
        $id = $request->input('id');
        $time = $request->input('time') ? json_encode($request->input('time')) : null;
        if ($id) {
            // 修改
            $attendeeInterview = AttendeeInterview::find($id);
            if (!$attendeeInterview) {
                return FAIL_RESPONSE_ARRAY('采访信息不存在');
            }
            $data = filterRequestData('attendee_interviews');
            $data['time'] = $time;
            $data['updater'] = $request->user()->real_name;
            $attendeeInterview->forceFill($data)->save();
            return SUCCESS_RESPONSE_ARRAY(compact('attendeeInterview'));
        } else {
            // 新增
            $attendee_id = $request->input('attendee_id');
            $attendee = Attendee::find($attendee_id);
            if (!$attendee) {
                return FAIL_RESPONSE_ARRAY('参会人不存在');
            }
            $data = filterRequestData('attendee_interviews');
            $data['time'] = $time;
            $data['creator'] = $request->user()->real_name;
            $data['updater'] = $request->user()->real_name;
            $attendeeInterview = AttendeeInterview::forceCreate($data);
            return SUCCESS_RESPONSE_ARRAY(compact('attendeeInterview'));
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
