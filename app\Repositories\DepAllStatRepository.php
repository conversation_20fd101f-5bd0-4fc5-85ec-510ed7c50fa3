<?php

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\HotelBooking;
use App\Models\Kpi;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DepAllStatRepository
{
    // 一级部门会议指标数据统计
    public function getAllDepEventKpiDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');

        // 根据检索条件查询获取列表数据
        $builder = Kpi::join('departments as d1', 'kpis.department_one_id', '=', 'd1.id')
            ->selectRaw('kpis.department_one_id, sum(kpis.invitation_kpi) as invitation_kpi, sum(kpis.visit_kpi) as visit_kpi, d1.name, d1.manager')
            ->where('forum_id', '=', 0)
            ->where('event_id', $event_id)
            ->groupByRaw('kpis.department_one_id, d1.name, d1.manager')
            ->get();

        $list = $builder->each(function (&$item) use ($event_id, $dates) {
            // 实际邀约数
            $item->realistic_invitation_kpi = $this->getEventInvitationKpi($event_id, $item->department_one_id, 1, $dates);
            // 实际到访数
            $item->realistic_visit_kpi = $this->getEventInvitationKpi($event_id, $item->department_one_id, 2, $dates);
            // 邀约指标完成率
            $item->invitation_kpi_completion_rate = $item->realistic_invitation_kpi ? round($item->realistic_invitation_kpi / $item->invitation_kpi * 100, 2) . '%' : '0%';
            // 到访指标完成率
            $item->visit_kpi_completion_rate = $item->realistic_visit_kpi ? round($item->realistic_visit_kpi / $item->visit_kpi * 100, 2) . '%' : '0%';
            // 邀约到访率
            $item->invitation_visit_rate = $item->realistic_visit_kpi && $item->realistic_invitation_kpi ? round($item->realistic_visit_kpi / $item->realistic_invitation_kpi * 100, 2) . '%' : '0%';
        });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取会议实际邀约指标
    private function getEventInvitationKpi($event_id, $department_id, $type, $dates)
    {
        $count = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->where('attendees.event_id', $event_id)
            ->where('channels.department_one_id', $department_id)
            ->where(function ($query) use ($type, $dates) {
                if($type == 1){
                    $query->where('attendees.status', 1);
                    if($dates){
                        $query->whereBetween('attendees.created_at', $dates);
                    }
                }
                if($type == 2){
                    $query->where('attendees.check_in_status', 1);
                    if($dates){
                        $query->whereBetween('attendees.check_in_time', $dates);
                    }
                }
            })
            ->count();
        return $count;
    }

    // 一级部门论坛指标数据统计
    public function getAllDepForumKpiDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');
        $forum_id = $request->input('forum_id');

        // 根据检索条件查询获取列表数据
        $builder = Kpi::join('departments as d1', 'kpis.department_one_id', '=', 'd1.id')
            ->join('forums', 'kpis.forum_id', '=', 'forums.id')
            ->selectRaw('kpis.department_one_id, d1.name as department_name, d1.manager as department_manager, kpis.forum_id, forums.name as forum_name,
            sum(kpis.invitation_kpi) as invitation_kpi, sum(kpis.visit_kpi) as visit_kpi')
            ->where('kpis.event_id', $event_id)
            ->where(function ($query) use ($forum_id) {
                if($forum_id){
                    $query->where('kpis.forum_id', $forum_id);
                } else {
                    $query->where('kpis.forum_id', '>', 0);
                }
            })
            ->groupByRaw('kpis.department_one_id, d1.name, d1.manager, kpis.forum_id, forums.name')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id, $dates) {
                // 实际邀约数
                $item->realistic_invitation_kpi = $this->getForumInvitationKpi($item->forum_id, $item->department_one_id, 1, $dates);
                // 实际到访数
                $item->realistic_visit_kpi = $this->getForumInvitationKpi($item->forum_id, $item->department_one_id, 2, $dates);
                // 邀约指标完成率
                $item->invitation_kpi_completion_rate = $item->realistic_invitation_kpi ? round($item->realistic_invitation_kpi / $item->invitation_kpi * 100, 2) . '%' : '0%';
                // 到访指标完成率
                $item->visit_kpi_completion_rate = $item->realistic_visit_kpi ? round($item->realistic_visit_kpi / $item->visit_kpi * 100, 2) . '%' : '0%';
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 论坛总指标数据统计
    public function getAllDepForumAllKpiDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');

        // 根据检索条件查询获取列表数据
        $builder = Kpi::join('forums', 'kpis.forum_id', '=', 'forums.id')
            ->selectRaw('kpis.forum_id, forums.name as forum_name, sum(kpis.invitation_kpi) as invitation_kpi, sum(kpis.visit_kpi) as visit_kpi')
            ->where('kpis.event_id', $event_id)
            ->where('kpis.forum_id', '>', 0)
            ->groupByRaw('kpis.forum_id, forums.name')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id, $dates) {
//                // 实际邀约数
//                $item->realistic_invitation_kpi = $this->getForumInvitationKpi($item->forum_id, null, 1, $dates);
//                // 邀约指标完成率
//                $item->invitation_kpi_completion_rate = $item->realistic_invitation_kpi ? round($item->realistic_invitation_kpi / $item->invitation_kpi * 100, 2) . '%' : '0%';
                // 实际到访数
                $item->realistic_visit_kpi = $this->getForumInvitationKpi($item->forum_id, null, 2, $dates);
                // 到访指标完成率
                $item->visit_kpi_completion_rate = $item->realistic_visit_kpi ? round($item->realistic_visit_kpi / $item->visit_kpi * 100, 2) . '%' : '0%';
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取论坛实际邀约指标
    private function getForumInvitationKpi($forum_id, $department_id, $type, $dates)
    {
        $count = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('attendee_forums', 'attendees.id', '=', 'attendee_forums.attendee_id')
            ->where('attendee_forums.forum_id', $forum_id)
            ->when($department_id, fn($query) => $query->where('channels.department_one_id', $department_id))
            ->where(function ($query) use ($type, $dates) {
                if($type == 1){
                    $query->where('attendees.status', 1);
                    if($dates){
                        $query->whereBetween('attendees.created_at', $dates);
                    }
                }
                if($type == 2){
                    $query->where('attendees.check_in_status', 1);
                    if($dates){
                        $query->whereBetween('attendees.check_in_time', $dates);
                    }
                }
            })
            ->count();

        return $count;
    }

    // 获取参会人员身份统计
    public function getAllDepAttendeeIdentityStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');

        $list = Attendee::query()
            ->selectRaw('identity, count(*) num')
            ->where('status', 1)
            ->where('event_id', $event_id)
            ->when($dates, function ($query) use ($dates) {
                $query->whereBetween('attendees.created_at', $dates);
            })
            ->groupBy('identity')
            ->get()
        ;
        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取所有事业部近七天邀约参会人人数统计
    public function getAllDepNear7DaysAttendeeStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        // 获取近七天入库的参会人的所有邀约事业部
        $builder = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('departments', 'channels.department_one_id', '=', 'departments.id')
            ->selectRaw('departments.id,departments.name')
            ->where('attendees.status', 1)
            ->where('attendees.event_id', $event_id)
            ->whereRaw("DATE(attendees.created_at) > ?", Carbon::now()->subDays(7)->toDateString())
            ->groupBy('departments.id', 'departments.name')
            ->get();

        $list = $builder->each(function (&$item) use ($event_id) {
            $item->near_7_days_stat = $this->getAttendeeNear7DaysStatByDep($item->id, $event_id);
        });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取某个事业部近七天的邀约数
    private function getAttendeeNear7DaysStatByDep($department_id, $event_id)
    {
        $sql = "WITH DateRange AS (
                SELECT CURDATE() - INTERVAL 6 DAY AS `date`
                UNION ALL
                SELECT CURDATE() - INTERVAL 5 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 4 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 3 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 2 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 1 DAY
                UNION ALL
                SELECT CURDATE()
            ),
            AttendeeCounts AS (
                SELECT
                    DATE(a.created_at) AS `date`,
                    COUNT(*) AS num
                FROM attendees AS a
                JOIN channels AS c ON a.channel_id = c.id
                WHERE a.`status`=1 AND a.event_id = " . $event_id . " AND c.department_one_id=" . $department_id . "
                GROUP BY DATE(a.created_at)
            )
            SELECT
                d.`date`,
                COALESCE(ac.num, 0) AS num
            FROM DateRange d
            LEFT JOIN AttendeeCounts ac ON d.`date` = ac.`date`
            ORDER BY d.`date`;";
        $res = \DB::select($sql);
        return $res;
    }



    // 酒店预定数据统计
    public function getAllDepHotelBookingDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }

        // 根据检索条件查询获取列表数据
        $builder = HotelBooking::join('hotels', 'hotel_bookings.hotel_id', '=', 'hotels.id')
            ->selectRaw('hotels.id, hotels.name, hotel_bookings.booking_date,
            sum(hotel_bookings.person_number) as person_number, sum(hotel_bookings.room_number) as room_number,
            sum(hotel_bookings.standard_room_number) as standard_room_number, sum(hotel_bookings.large_bed_room_number) as large_bed_room_number')
            ->where('hotels.event_id', $event_id)
            ->groupByRaw('hotels.id, hotels.name, hotel_bookings.booking_date')
            ->orderBy('hotels.id')
            ->orderBy('hotel_bookings.booking_date')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id) {
                $item->actual_standard_room_number = $this->getHotelActualCheckInNum($event_id, $item->id, $item->booking_date, 1);
                $item->actual_large_bed_room_number = $this->getHotelActualCheckInNum($event_id, $item->id, $item->booking_date, 2);
                // 到访指标完成率
                $item->room_rate = round(($item->actual_standard_room_number + $item->actual_large_bed_room_number) / $item->room_number * 100, 2) . '%';
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取酒店实际入住人数
    private function getHotelActualCheckInNum($event_id, $hotel_id, $booking_date, $room_type)
    {
        $count = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->selectRaw('sum(attendee_hotels.booking_room_number) as booking_room_number')
            ->where('attendees.status', 1)
            ->where('attendees.event_id', $event_id)
            ->where('attendee_hotels.hotel_id', $hotel_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            ->where('attendee_hotels.check_in_date', $booking_date)
            ->where('attendee_hotels.room_type', $room_type)
            ->first()
            ->booking_room_number ?? 0;

        return $count;
    }

    // 某个酒店各事业部入住人数统计
    public function getAllDepHotelCheckInNumStat(Request $request)
    {
        $event_id = $request->input('event_id');
        $hotel_id = $request->input('hotel_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        if(empty($hotel_id)){
            return FAIL_RESPONSE_ARRAY('请选择酒店');
        }

        $list = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('departments', 'channels.department_one_id', '=', 'departments.id')
            ->selectRaw('departments.id, departments.name, attendee_hotels.check_in_date, count(*) as num')
            ->where('attendees.status', 1)
            ->where('attendees.event_id', $event_id)
            ->where('attendee_hotels.hotel_id', $hotel_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            ->groupByRaw('departments.id, departments.name, attendee_hotels.check_in_date')
            ->orderBy('departments.id')
            ->orderBy('attendee_hotels.check_in_date')
            ->get();

        return SUCCESS_RESPONSE_ARRAY($list);
    }



    // 获取用餐统计
    public function getAllDepDineDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        $dine_date = $request->input('dine_date');
        $time_type = $request->input('time_type');
        $type = $request->input('type');
        $location = $request->input('location');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }

        $builder = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('departments', 'channels.department_one_id', '=', 'departments.id')
            ->join('attendee_dines', 'attendees.id', '=', 'attendee_dines.attendee_id')
            ->join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
            ->selectRaw('departments.id, departments.name, count(*) AS num')
            ->where('attendees.event_id', $event_id)
            ->where('attendee_dines.is_need_dine', 1)
            ->when($dine_date, fn($query) => $query->where('dines.dine_date', $dine_date))
            ->when($time_type, fn($query) => $query->where('dines.time_type', $time_type))
            ->when($type, fn($query) => $query->where('dines.type', $type))
            ->when($location, fn($query) => $query->where('dines.location', $location))
            ->groupBy('departments.id')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id, $dine_date, $time_type, $type, $location) {
                $item->sign_in_num = $this->getDineSignInNum($event_id, $item->id, $dine_date, $time_type, $type, $location);
                // 签到率
                $item->room_rate = round($item->sign_in_num / $item->num * 100, 2) . '%';
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取用餐签到人数
    private function getDineSignInNum($event_id, $department_id, $dine_date, $time_type, $type, $location)
    {
        $count = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('attendee_dines', 'attendees.id', '=', 'attendee_dines.attendee_id')
            ->join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
            ->selectRaw('count(*) AS num')
            ->where('attendees.event_id', $event_id)
            ->where('attendee_dines.is_need_dine', 1)
            ->where('attendee_dines.check_in_status', 1)
            ->where('channels.department_one_id', $department_id)
            ->when($dine_date, fn($query) => $query->where('dines.dine_date', $dine_date))
            ->when($time_type, fn($query) => $query->where('dines.time_type', $time_type))
            ->when($type, fn($query) => $query->where('dines.type', $type))
            ->when($location, fn($query) => $query->where('dines.location', $location))
            ->first()
            ->num ?? 0;

        return $count;
    }


}
