<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/20
 * Time 11:06
 */


use App\Http\Controllers\Admin\Data\ChannelController;
use App\Http\Controllers\Admin\Event\AttendeeForumController;
use App\Http\Controllers\Admin\Event\EventController;
use App\Http\Controllers\Admin\Event\ForumController;
use App\Http\Controllers\Admin\Event\InviteOrganizationController;
use App\Http\Controllers\Admin\Event\PaidContentController;
use App\Http\Controllers\Admin\Event\RegistrantController;
use App\Http\Controllers\Applet\MiniAppController;
use Illuminate\Support\Facades\Route;

//会务管理
Route::group(['prefix' => 'events'], function () {

    //会务列表
    Route::post('list', [EventController::class,'index'])->name('event.events.list');
    Route::get('all', [EventController::class, 'dropList'])->name('event.events.dropList');
    Route::post('create', [EventController::class, 'store'])->name('event.events.create');
    Route::get('{id}', [EventController::class, 'show'])->name('event.events.show')->where('id', '[0-9]+');
    Route::put('update/{id}', [EventController::class, 'update'])->name('event.events.update')->where('id', '[0-9]+');
    Route::delete('destroy/{id}', [EventController::class, 'destroy'])->name('event.events.destroy')->where('id', '[0-9]+');
    Route::post('toggle_is_on/{id}', [EventController::class, 'toggleIsOn'])->name('event.events.toggle_is_on')->where('id', '[0-9]+');
    Route::post('audit_organization/{id}', [EventController::class, 'auditOrganization'])->name('event.events.audit_organization')->where('id', '[0-9]+');
    Route::post('registration_status/{id}', [EventController::class, 'registrationStatus'])->name('event.events.registrationStatus')->where('id', '[0-9]+');
    //会议下所有论坛
    Route::get('{event_id}/forums', [ForumController::class, 'index'])->name('event.events.forums_list')->where('event_id', '[0-9]+');
    Route::get('forums/attendee_list/{event_id}/{forum_id}', [ForumController::class,'attendeeForumList'])->name('event.events.forums_attendee_list');
    Route::post('forums/create', [ForumController::class, 'store'])->name('event.events.forums_create');
    Route::get('forums/{id}', [ForumController::class, 'show'])->name('event.events.forums_show')->where('id', '[0-9]+');
    Route::put('forums/update/{id}', [ForumController::class, 'update'])->name('event.events.forums_update')->where('id', '[0-9]+');
    Route::delete('forums/destroy/{id}', [ForumController::class, 'destroy'])->name('event.events.forums_destroy')->where('id', '[0-9]+');
    Route::post('forums/toggle_is_on/{id}', [ForumController::class, 'toggleIsOn'])->name('event.events.forums_toggle_is_on')->where('id', '[0-9]+');
    Route::post('forums/toggle_is_enroll/{id}', [ForumController::class, 'toggleIsEnroll'])->name('event.events.forums_toggle_is_enroll')->where('id', '[0-9]+');
    Route::post('forums/toggle_is_audit/{id}', [ForumController::class, 'toggleIsAudit'])->name('event.events.forums_toggle_is_audit')->where('id', '[0-9]+');
    Route::post('forums/toggle_is_check/{id}', [ForumController::class, 'toggleIsCheck'])->name('event.events.forums_toggle_is_audit')->where('id', '[0-9]+');
    //论坛下拉
    Route::post('forums/audit_forums', [ForumController::class, 'auditForums'])->name('event.events.audit_forums');


    //付费内容
    Route::get('{event_id}/paid_contents', [PaidContentController::class, 'index'])->name('event.events.paid_contents_list')->where('event_id', '[0-9]+');
    Route::post('paid_contents/create', [PaidContentController::class, 'store'])->name('event.events.paid_contents_create');
    Route::get('paid_contents/{id}', [PaidContentController::class, 'show'])->name('event.events.paid_contents_show')->where('id', '[0-9]+');
    Route::put('paid_contents/update/{id}', [PaidContentController::class, 'update'])->name('event.events.paid_contents_update')->where('id', '[0-9]+');
    Route::delete('paid_contents/destroy/{id}', [PaidContentController::class, 'destroy'])->name('event.events.paid_contents_destroy')->where('id', '[0-9]+');
    Route::post('paid_contents/toggle_is_on/{id}', [PaidContentController::class, 'toggleIsOn'])->name('event.events.paid_contents_toggle_is_on')->where('id', '[0-9]+');

//    Route::post('attendees', [AttendeeController::class, 'index'])->name('event.events.attendees.list');
    //报名人列表
    Route::post('registrants', [RegistrantController::class, 'index'])->name('event.registrants.list');
    //报名人导出
    Route::post('registrants/export', [RegistrantController::class, 'export'])->name('event.registrants.export');
    //分配对接人assignUser
    Route::post('assign_user', [RegistrantController::class, 'assignUser'])->name('event.registrants.assign_user');
    //更新报名人信息
    Route::put('registrants/{id}', [RegistrantController::class, 'update'])->name('event.registrants.update')->where('id', '[0-9]+');
    Route::put('attendees/{id}', [RegistrantController::class, 'updateAttendee'])->name('event.registrants.updateAttendee')->where('id', '[0-9]+');

    //报名人show
    Route::get('registrants/{id}/{batch}', [RegistrantController::class, 'show'])->name('event.registrants.show')->where('id', '[0-9]+');

    //报名审核列表
    Route::post('registrants/audit_list', [RegistrantController::class, 'auditList'])->name('event.registrants_audit.list');
    Route::post('registrants/audit/export', [RegistrantController::class, 'auditListExport'])->name('event.registrants.auditListExport');
    Route::post('registrants/invite_list', [RegistrantController::class, 'auditList'])->name('event.invite_registrants_audit.list');
    //auditRegistrant
    Route::post('registrants/audit', [RegistrantController::class, 'auditRegistrant'])->name('event.registrants_audit.audit');
    //对接人下拉列表
    Route::post('drop_user_list', [RegistrantController::class, 'dropUserList'])->name('event.events.drop_user_list');

    //论坛审核列表
    Route::post('attendee_forums', [AttendeeForumController::class, 'index'])->name('event.attendee_forums.list');
    //导出
    Route::post('attendee_forums/export', [AttendeeForumController::class, 'export'])->name('event.attendee_forums.export');
    //forumsStatistics
    Route::post('forums_statistics', [AttendeeForumController::class, 'forumsStatistics'])->name('event.forums_statistics');
    //auditForums
    Route::post('audit_forums/audit', [AttendeeForumController::class, 'auditForums'])->name('event.attendee_forums.audit_forums');
    Route::post('audit_forums/inviteForums', [AttendeeForumController::class, 'inviteForums'])->name('event.attendee_forums.inviteForums');

    //邀约单位
    Route::post('{event_id}/invite_organizations', [InviteOrganizationController::class, 'index'])->name('event.events.paid_contents.list')->where('event_id', '[0-9]+');
    Route::post('{event_id}/invite_organizations/import', [InviteOrganizationController::class, 'import'])->name('event.events.invite_organizations.import')->where('event_id', '[0-9]+');
    //新增邀约单位
    Route::post('invite_organizations/create', [InviteOrganizationController::class, 'store'])->name('event.events.invite_organizations.create');
    //更新邀约单位
    Route::put('invite_organizations/update/{id}', [InviteOrganizationController::class, 'update'])->name('event.events.invite_organizations.update')->where('id', '[0-9]+');
    Route::post('invite_organizations/attendee_list/{event_id}', [InviteOrganizationController::class,'attendeeOrganizationList'])->name('event.events.invite_organizations_attendee_list');
    //删除会议下的所有邀约单位
    Route::delete('invite_organizations/destroy/{id}', [InviteOrganizationController::class, 'destroy'])->name('event.events.invite_organizations.destroy')->where('id', '[0-9]+');


    //获取渠道链接二维码
    Route::post('get_qr_code', [MiniAppController::class, 'getQrCode']);

    //渠道下拉列表
    Route::post('drop_channel_list', [ChannelController::class, 'dropChannelList'])->name('event.events.drop_channel_list');
});
