<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('api_token')->nullable();
            $table->string('real_name',20)->unique();
            $table->unsignedInteger('department_one_id')->nullable()->comment('一级部门id');
            $table->unsignedInteger('department_two_id')->nullable()->comment('二级部门id');

            $table->tinyInteger('state')->default(2)->comment('1:启用 2:禁用');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('email')->nullable();
            $table->string('phone',15)->nullable()->comment('手机号');
            $table->string('head_img')->default('')->comment('头像');
            $table->timestamp('last_login_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->rememberToken();
            $table->string('creator', 20)->nullable()->comment('添加人');
            $table->string('updater', 20)->nullable()->comment('最后更新人');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
