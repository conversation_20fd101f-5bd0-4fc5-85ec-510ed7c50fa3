<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Log;

class CommonMail extends Mailable
{
    use Queueable, SerializesModels;

    public $message_str;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($message_str)
    {
        $this->message_str = $message_str;
    }

    public function build()
    {
        return $this->view('emails.shksy')
            ->subject('上海市教育考试院')
            ->with([ 'message_str' => $this->message_str ] );
    }
}
