<?php

namespace App\Http\Controllers\Admin\Event;

use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\User;
use App\Repositories\AttendeeExport;
use App\Repositories\AttendeeRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Sign;

class AttendeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, AttendeeRepository $attendeeRepository)
    {
        // 查询参会人服务信息列表
        $query = $attendeeRepository->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 导出参会人服务信息列表
    public function export(Request $request, AttendeeRepository $attendeeRepository)
    {
        try {
            $list = $attendeeRepository->listBuilder($request)->orderBy('id', 'desc')->get();
            // 导出是否有权限查看手机号
            $is_view_phone = $request->user()->is_view_phone == 1;
            $export = new AttendeeExport($list, $is_view_phone);// 导出数据到 Excel 文件
            return Excel::download($export, '参会人服务信息列表.xlsx');
        } catch (\Exception $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }

    //cancel取消报名
    public function cancelEvent(string $id,Request $request)
    {
        try {
            DB::beginTransaction();
            //将参会人的状态改为已取消，同时将参会人已报名的论坛状态audit_status改为驳回3，audit_remark为取消报名，时间为当前时间
            $attendee = Attendee::find($id);
            $status_remark = $request->input('status_remark');
            if ($attendee->check_in_status == 1) {
                return SUCCESS_RESPONSE_ARRAY("已签到的会议不可取消报名");
            }
//            if ($attendee->status !== Attendee::$status_ok) {
//                return SUCCESS_RESPONSE_ARRAY("非已报名的会议不可取消报名");
//            }

            $attendee->status = Attendee::$status_cancel;
            $attendee->status_remark = $status_remark;
            $attendee->save();
            $attendee->forums()->update(['audit_status' => 3, 'audit_remark' => $status_remark, 'audit_time' => now()]);

            DB::commit();

            // 查询$user_id对象
            $user = User::query()->find($attendee->user_id);
            //如果用户存在则发送短信
            if ($user) {
                $smsContent = "您好，您跟进的客户name已取消报名，取消报名原因：status_remark请及时联系对方进行跟进。";
                $params = [
                    'name' => $attendee->name,
                    'status_remark' => $status_remark
                ];
                // 给商务发送短信
                singleSendJianzhouSms(Sign::SEEE, $user->phone, replaceParams($smsContent, $params));
            }

            return SUCCESS_RESPONSE_ARRAY("取消成功");
        } catch (\Exception $e) {
            DB::rollBack();
            return ERROR_RESPONSE_ARRAY($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // 参会人详情
//        return SUCCESS_RESPONSE_ARRAY(Attendee::with('registrant',
//            'registrant.channel:id,name,department_two_id',
//            'registrant.channel.department:id,name,parent_id,code',
//            'registrant.channel.department.parent:id,name',
//            'user:id,real_name',
//            'event:id,title',
//            'forums:id,name',
//            'attendeeInterview'
//        )->find($id));

        return SUCCESS_RESPONSE_ARRAY(Attendee::with('registrant',
            'channel:id,name,department_two_id',
            'channel.department:id,name,parent_id,code',
            'channel.department.parent:id,name',
            'user:id,real_name',
            'event:id,title',
            'forums:id,name',
            'attendeeInterview'
        )->find($id));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id, AttendeeRepository $attendeeRepository)
    {
        return $attendeeRepository->update($id, $request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

}
