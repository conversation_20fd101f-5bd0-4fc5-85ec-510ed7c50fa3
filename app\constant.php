<?php

// 公共常量 如果文件不生效,执行命令: composer dump-autoload


//是否需要，1:需要，2:不需要
class EventState
{
    const ON = 1;
    const OFF = 2;
}

//是否需要，1:需要，2:不需要
class Need
{
    const NEED = 1;
    const NOT_NEED = 2;
}

//会议类型,1·开放型，2·限制型
class EventType
{
    const OPEN = 1;
    const LIMIT = 2;
    const INVITE = 3;
}

//报名状态：1:已报名 2:已取消 3:待确认(审核),4：已驳回,
class AuditStatus
{
    const PASS = 1;
    const CANCEL = 2;
    const WAIT = 3;
    const REJECT = 4;
}


const HOST = 'https://conference-api.114study.com';

class AppletUrl
{
    // 小程序链接
    const URL_LINK = 'https://wxaurl.cn/6FBs1IAGLxp';
    // 小程序首页短链
    const INDEX = '/pages/index/index';
    // 小程序参会通道短链
    const EVENT_WAY_URL = '/pages/customer/channel/channel';
}


// 短信签名
class Sign
{
    const YUANBO = '【远播国际】';

    const SEEE = '【SEEE大会组委】';
}
