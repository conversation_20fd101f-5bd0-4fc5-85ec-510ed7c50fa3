<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 16:53
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\AttendeePickUp;
use Illuminate\Http\Request;

class AttendeePickUpRepository
{
    public function listBuilder(Request $request)
    {
        // 会议ID
        $event_id = $request->input('event_id');
        // 参会人单位
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $position = $request->input('position');
        // 参会人姓名
        $name = $request->input('name');
        $phone = $request->input('phone');
        // 接站人
        $person = $request->input('person');
        // 是否是飞机高铁
        $is_train = $request->input('is_train');
        // 是否需要接站
        $is_need_pick_up = $request->input('is_need_pick_up');
        // 对接人
        $user_id_arr = $request->input('user_id_arr');
        $user_ids = AttendeeRepository::getFilterUserIds($user_id_arr);
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = RegistrantRepository::getFilterChannelIds($channel_id_arr);

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $query = AttendeePickUp::join('attendees', 'attendee_pick_ups.attendee_id', '=', 'attendees.id')
            ->join('events', 'attendees.event_id', '=', 'events.id')
            ->leftjoin('users', 'attendees.user_id', '=', 'users.id')
            ->select('attendee_pick_ups.id', 'attendee_pick_ups.is_need_pick_up', 'attendee_pick_ups.attendee_id', 'attendee_pick_ups.arrive_time', 'attendee_pick_ups.plan_arrive_time',
                'attendee_pick_ups.is_train', 'attendee_pick_ups.station_info', 'attendee_pick_ups.place', 'attendee_pick_ups.to_place', 'attendee_pick_ups.hotel_contact',
                'attendee_pick_ups.standard', 'attendee_pick_ups.car_one', 'attendee_pick_ups.car_two', 'attendee_pick_ups.person',
                'attendee_pick_ups.tel', 'attendee_pick_ups.is_car_following', 'attendee_pick_ups.updater', 'attendee_pick_ups.updated_at',
                'attendees.name as attendee_name', 'attendees.phone as attendee_phone', 'attendees.position as attendee_position', 'attendees.organization as attendee_organization',
                'attendees.organization_code as attendee_organization_code', 'attendees.organization_type as attendee_organization_type', 'attendees.identity as attendee_identity', 'attendees.event_id',
                'events.short_title as event_short_title',
                'users.real_name as user_real_name')
//            ->where('attendee_pick_ups.is_need_pick_up', 1)
            ->where('attendees.status', Attendee::$status_ok)
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('attendees.event_id', $event_id))
            ->when($organization, fn($query) => $query->where('attendees.organization', 'like', "%$organization%"))
            ->when($organization_code, fn($query) => $query->where('attendees.organization_code', 'like', "%$organization_code%"))
            ->when($position, fn($query) => $query->where('attendees.position', 'like', "%$position%"))
            ->when($name, fn($query) => $query->where('attendees.name', 'like', "%$name%"))
            ->when($phone, fn($query) => $query->where('attendees.phone', "$phone"))
            ->when($user_id_arr, fn($query) => $query->whereIn('attendees.user_id', $user_ids))
            ->when($channel_id_arr, fn($query) => $query->whereIn('attendees.channel_id', $channel_id))
            ->when($person, fn($query) => $query->where('attendee_pick_ups.person', 'like', "%$person%"))
            ->when($is_train, fn($query) => $query->where('attendee_pick_ups.is_train', $is_train))
            ->when($is_need_pick_up, fn($query) => $query->where('attendee_pick_ups.is_need_pick_up', $is_need_pick_up));
        $query = RegistrantRepository::getAuthFilterQuery($request, $query);
        return $query;
//            ->when($arrive_times, fn($query) => $query->where('attendee_pick_ups.arrive_time', '>=', $arrive_times[0]))
//            ->when($arrive_times, fn($query) => $query->where('attendee_pick_ups.arrive_time', '<=', $arrive_times[1]));
    }


}
