<?php

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\Kpi;
use Illuminate\Http\Request;

class KpiRepository
{
    // 指标列表
    public function listBuilder(Request $request)
    {
        // 指标类型：1 会议 2 论坛
        $kpi_type = $request->input('kpi_type', 1);
        $event_id = $request->input('event_id');
        $departments = $request->input('departments');
        $department_one_id = '';
        $department_two_id = '';
        if ($departments) {
            $department_one_id = $departments[0];
            // 判断$departments的长度
            if( count($departments) > 1){
                $department_two_id = $departments[1];
            }
        }
        $department_head = $request->input('department_head');

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        // 根据检索条件查询获取列表数据
        $builder = Kpi::join('events', 'kpis.event_id', '=', 'events.id')
            ->leftJoin('departments as d1', 'kpis.department_one_id', '=', 'd1.id')
            ->leftJoin('departments as d2', 'kpis.department_two_id', '=', 'd2.id')
            ->select('kpis.*', 'events.title as event_title', 'events.short_title as event_short_title',
                'd1.name as department_one_name', 'd1.manager as department_one_manager',
                'd2.name as department_two_name', 'd2.manager as department_two_manager')
            ->whereIn('events.activity_type', $activityTypes)
            ->where(function ($query) use ($kpi_type) {
                if($kpi_type == 1){
                    $query->where('forum_id', '=', 0);
                } else {
                    $query->where('forum_id', '>', 0);
                }
            })
            ->when($event_id, fn($query) => $query->whereIn('event_id', $event_id))
            ->when($department_one_id, fn($query) => $query->where('department_one_id', $department_one_id))
            ->when($department_two_id, fn($query) => $query->where('department_two_id', $department_two_id))
            ->when($department_head, fn($query) => $query->where('d2.manager', 'like', '%' . $department_head . '%'));

        $cnt = $builder->count();
        $builder = $builder->pagination();
        if ($kpi_type == 2) {
            $builder = $builder->with(['forum:id,name']);
        }

        $list = $builder->orderBy('id', 'desc')->get()
            ->each(function (&$item) use ($kpi_type) {
                if ($item->forum_id) {
                    // 当前论坛名称
                    $item->forum_name = $item->forum->name;
                    unset($item->forum);
                }
                // 实际邀约数
                $item->realistic_invitation_kpi = $this->getInvitationKpi($kpi_type, $item->event_id, $item->forum_id, $item->department_two_id, 1);
                // 实际到访数
                $item->realistic_visit_kpi = $this->getInvitationKpi($kpi_type, $item->event_id, $item->forum_id, $item->department_two_id, 2);
                // 邀约指标完成率
                $item->invitation_kpi_completion_rate = $item->realistic_invitation_kpi ? round($item->realistic_invitation_kpi / $item->invitation_kpi * 100, 2) . '%' : '0%';
                // 到访指标完成率
                $item->visit_kpi_completion_rate = $item->realistic_visit_kpi ? round($item->realistic_visit_kpi / $item->visit_kpi * 100, 2) . '%' : '0%';
            });

        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    // 获取实际邀约指标
    public function getInvitationKpi($kpi_type, $event_id, $forum_id, $department_id, $type)
    {
        $count = 0;
        // 会议指标
        if($kpi_type==1){
            $count = Attendee::join('registrants', 'attendees.registrant_id', '=', 'registrants.id')
                ->join('channels', 'registrants.channel_id', '=', 'channels.id')
                ->where('channels.department_two_id', $department_id)
                ->where('attendees.event_id', $event_id)
                ->where(function ($query) use ($type) {
                    if($type == 1){
                        $query->where('attendees.status', 1);
                    }
                    if($type == 2){
                        $query->where('attendees.check_in_status', 1);
                    }
                })
                ->count();
        } else { // 论坛指标
            $count = Attendee::join('registrants', 'attendees.registrant_id', '=', 'registrants.id')
                ->join('channels', 'registrants.channel_id', '=', 'channels.id')
                ->join('attendee_forums', 'attendees.id', '=', 'attendee_forums.attendee_id')
                ->where('channels.department_two_id', $department_id)
                ->where('attendee_forums.forum_id', $forum_id)
                ->where(function ($query) use ($type) {
                    if($type == 1){
                        $query->where('attendees.status', 1);
                    }
                    if($type == 2){
                        $query->where('attendees.check_in_status', 1);
                    }
                })
                ->count();
        }
        return $count;
    }
}
