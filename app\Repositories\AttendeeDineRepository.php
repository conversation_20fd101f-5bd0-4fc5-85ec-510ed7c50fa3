<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 16:53
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\AttendeeDine;
use App\Models\Dine;
use Illuminate\Http\Request;

class AttendeeDineRepository
{
    public function listBuilder(Request $request)
    {
        // 会议ID
        $event_id = $request->input('event_id');
        // 参会人单位
        $organization = $request->input('organization');
        $position = $request->input('position');
        // 参会人姓名
        $name = $request->input('name');
        $phone = $request->input('phone');
        // 用餐时间
        $location = $request->input('location');
        // 用餐时间
        $dine_date = $request->input('dine_date');
        // 是否需要餐饮
        $is_need_dine = $request->input('is_need_dine');
        // 对接人
        $user_id_arr = $request->input('user_id_arr');
        $user_ids = AttendeeRepository::getFilterUserIds($user_id_arr);
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = RegistrantRepository::getFilterChannelIds($channel_id_arr);
        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $query = AttendeeDine::join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
            ->join('attendees', 'attendee_dines.attendee_id', '=', 'attendees.id')
            ->join('events', 'dines.event_id', '=', 'events.id')
            ->leftjoin('users', 'attendees.user_id', '=', 'users.id')
            ->select('attendee_dines.id', 'attendee_dines.is_need_dine', 'attendee_dines.attendee_id', 'attendee_dines.check_in_status', 'attendee_dines.updated_at', 'attendee_dines.updater',
                'dines.dine_date', 'dines.time_type', 'dines.type', 'dines.location', 'dines.specific_time',
                'attendees.name as attendee_name', 'attendees.phone as attendee_phone', 'attendees.position as attendee_position', 'attendees.organization as attendee_organization',
                'attendees.organization_code as attendee_organization_code','attendees.organization_type as attendee_organization_type', 'attendees.identity as attendee_identity', 'attendees.event_id',
                'events.short_title as event_short_title',
                'users.real_name as user_real_name')
//            ->where('attendee_dines.is_need_dine', 1)
            ->where('attendees.status', Attendee::$status_ok)
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('attendees.event_id', $event_id))
            ->when($organization, fn($query) => $query->where('attendees.organization', 'like', "%$organization%"))
            ->when($position, fn($query) => $query->where('attendees.position', 'like', "%$position%"))
            ->when($name, fn($query) => $query->where('attendees.name', 'like', "%$name%"))
            ->when($phone, fn($query) => $query->where('attendees.phone', "$phone"))
            ->when($user_id_arr, fn($query) => $query->whereIn('attendees.user_id', $user_ids))
            ->when($channel_id_arr, fn($query) => $query->whereIn('attendees.channel_id', $channel_id))
            ->when($location, fn($query) => $query->where('dines.location', 'like', "%$location%"))
            ->when($dine_date, fn($query) => $query->where('dines.dine_date', $dine_date))
            ->when($is_need_dine, fn($query) => $query->where('attendee_dines.is_need_dine', $is_need_dine));

        $query = RegistrantRepository::getAuthFilterQuery($request, $query);
        return $query;
    }

    // 设置参会人餐饮信息
    public function setAttendeeDine(Request $request, string $attendee_id)
    {
        if (empty($attendee_id)) {
            return FAIL_RESPONSE_ARRAY('未选择参会人');
        }
        $dineIds = $request->input('dineIds');
        if (empty($dineIds)) {
            return FAIL_RESPONSE_ARRAY('未选择就餐信息');
        }

        $tag = false;
        // 获取参会人已选择的餐饮信息
        $selected_dine_ids = AttendeeDine::where('attendee_id', $attendee_id)->get()->pluck('dine_id')->toArray();
        // 合并两个数组$selected_dine_ids 和 $dineIds
        $all_dine_ids = array_merge($selected_dine_ids, $dineIds);
        // 查找Dine 表中的id在$all_dine_ids中的数据，根据dine_date、time_type分组，获取数量
        $dines = Dine::whereIn('id', $all_dine_ids)->selectRaw('dine_date,time_type,count(*) as total')
            ->groupBy('dine_date', 'time_type')
            ->get();
        $dines->each(function ($item) use (&$tag) {
            if($item->total > 1){
                $tag = true;
            }
        });
        if($tag){
            return FAIL_RESPONSE_ARRAY('同一天同一个早中晚类型的用餐只能选择一个');
        }

        // 循环$dine_ids，添加餐饮信息
        foreach ($dineIds as $dine_id) {
            // 判断是否存在，存在则跳过，不存在则添加
            $attendee_dine = AttendeeDine::where('attendee_id', $attendee_id)->where('dine_id', $dine_id)->exists();
            if (!$attendee_dine) {
                $attendee_dine = new AttendeeDine();
                $attendee_dine->attendee_id = $attendee_id;
                $attendee_dine->dine_id = $dine_id;
                $attendee_dine->is_need_dine = 1;
                $attendee_dine->creator = $request->user()->real_name;
                $attendee_dine->updater = $request->user()->real_name;
                $attendee_dine->save();
            }
        }
        return SUCCESS_RESPONSE_ARRAY('用餐设置成功');
    }

    // 批量设置参会人餐饮信息
    public function setMoreAttendeeDine(Request $request)
    {
        $attendeeIds = $request->input('attendeeIds');
        $dineIds = $request->input('dineIds');
        if (empty($attendeeIds)) {
            return FAIL_RESPONSE_ARRAY('未选择参会人');
        }
        if (empty($dineIds)) {
            return FAIL_RESPONSE_ARRAY('未选择就餐');
        }

        // 双循环$attendeeIds、$dineIds，添加餐饮信息
        foreach ($attendeeIds as $attendee_id) {
            // 获取之前参会人已选择的餐饮信息
            $selected_old_dine_ids = AttendeeDine::where('attendee_id', $attendee_id)->get()->pluck('dine_id')->toArray();
            // 合并两个数组 $selected_old_dine_ids 和 $dineIds
            $all_dine_ids = array_merge($selected_old_dine_ids, $dineIds);
            // 查找Dine 表中的id在$all_dine_ids中的数据，根据dine_date、time_type分组，获取数量
            $dines = Dine::whereIn('id', $all_dine_ids)->selectRaw('dine_date,time_type,count(*) as total')
                ->groupBy('dine_date', 'time_type')
                ->get();
            $dines->each(function ($item) use ($attendee_id) {
                if($item->total > 1){
                    // 删除之前选择已存在同一天同一个早中晚类型的用餐
                    AttendeeDine::join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
                        ->where('attendee_dines.attendee_id', $attendee_id)
                        ->where('dines.dine_date', $item->dine_date)
                        ->where('dines.time_type', $item->time_type)
                        ->delete();
                }
            });

            // 循环$dine_ids，添加餐饮信息
            foreach ($dineIds as $dine_id) {
                // 判断是否存在，存在则跳过，不存在则添加
                $attendee_dine = AttendeeDine::where('attendee_id', $attendee_id)->where('dine_id', $dine_id)->exists();
                if (!$attendee_dine) {
                    $attendee_dine = new AttendeeDine();
                    $attendee_dine->attendee_id = $attendee_id;
                    $attendee_dine->dine_id = $dine_id;
                    $attendee_dine->is_need_dine = 1;
                    $attendee_dine->creator = $request->user()->real_name;
                    $attendee_dine->updater = $request->user()->real_name;
                    $attendee_dine->save();
                }
            }
        }

        return SUCCESS_RESPONSE_ARRAY('用餐设置成功');
    }

}
