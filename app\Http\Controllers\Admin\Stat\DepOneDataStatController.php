<?php

namespace App\Http\Controllers\Admin\Stat;

use App\Http\Controllers\Controller;
use App\Repositories\DepOneStatRepository;
use Illuminate\Http\Request;

class DepOneDataStatController extends Controller
{

    // 事业部下各二级部门会议指标数据统计
    public function getOneDeptEventKpiDataStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepEventKpiDataStat($request);
    }

    // 事业部下各二级部门论坛指标数据统计
    public function getOneDepForumKpiDataStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepForumKpiDataStat($request);
    }

    // 事业部下各二级部门论坛总指标数据统计
    public function getOneDepForumAllKpiDataStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepForumAllKpiDataStat($request);
    }

    // 事业部下各二级部门参会人员身份统计
    public function getOneDepAttendeeIdentityStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepAttendeeIdentityStat($request);
    }

    // 事业部下各二级部门近七天邀约参会人人数统计
    public function getOneDepNear7DaysAttendeeStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepNear7DaysAttendeeStat($request);
    }


    // 事业部下各二级部门酒店预定数据统计
    public function getOneDepHotelBookingDataStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepHotelBookingDataStat($request);
    }

    // 事业部下各二级部门某个酒店各事业部入住人数统计
    public function getOneDepHotelCheckInNumStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepHotelCheckInNumStat($request);
    }


    // 事业部下各二级部门用餐数据统计
    public function getOneDepDineDataStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepDineDataStat($request);
    }

    // 事业部下各二级部门客户跟进数据统计
    public function getOneDepFollowDataStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepFollowDataStat($request);
    }

    // 事业部下各商务的客户跟进数据统计
    public function getOneDepBusinessFollowDataStat(Request $request, DepOneStatRepository $repository)
    {
        return $repository->getOneDepBusinessFollowDataStat($request);
    }


}
