<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Website\EventAnnouncement;
use Illuminate\Http\Request;

//会议公告
class EventAnnouncementController extends Controller
{
    public function index(Request $request)
    {
        // 获取年份
        $year = $request->input('year');
        // 返回所有 EventAnnouncement
        $builder = EventAnnouncement::query()
            ->where('year', $year);
        $cnt = $builder->count();
        $list = $builder->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    public function create()
    {
        // 显示创建 EventAnnouncement 的表单
    }

    public function store(Request $request)
    {
        // 保存新创建的 EventAnnouncement
        $announcement = new EventAnnouncement();
        $announcement->title = $request->input('title');
        $announcement->introduction = $request->input('introduction');
        $announcement->creator = $request->user()->real_name;
        $announcement->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    public function show($id)
    {
        // 显示特定 EventAnnouncement 的详细信息
        $announcement = EventAnnouncement::find($id);
        return SUCCESS_RESPONSE_ARRAY($announcement);
    }

    public function edit($id)
    {
        // 显示更新 EventAnnouncement 的表单
    }

    public function update(Request $request, $id)
    {
        // 更新 EventAnnouncement
        $announcement = EventAnnouncement::find($id);
        $announcement->title = $request->input('title');
        $announcement->introduction = $request->input('introduction');
        $announcement->updater = $request->user()->real_name;
        $announcement->save();
        return SUCCESS_RESPONSE_ARRAY();
    }

    public function destroy($id)
    {
        // 删除 EventAnnouncement
        $announcement = EventAnnouncement::find($id);
        $announcement->delete();
        return SUCCESS_RESPONSE_ARRAY();
    }
}
