<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class RolesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * php artisan make:seed RolesTableSeeder
     * php artisan db:seed --class=RolesTableSeeder
     */
    public function run(): void
    {
        //

        DB::transaction(function () {
            $role = Role::forceCreate([
                'name' => '系统管理员',
                'guard_name' => 'api',
            ]);
            $user = User::where('username', '<EMAIL>')->first();
            $user->roles()->sync([$role->id]);
        });

    }
}
