<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 16:53
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\AttendeeForums;
use App\Models\AttendeeHotel;
use App\Models\Channel;
use App\Models\Department;
use App\Models\Event;
use App\Models\Forum;
use App\Models\InviteOrganization;
use App\Models\Member;
use App\Models\Message;
use App\Models\Registrant;
use App\Models\User;
use AppletUrl;
use AuditStatus;
use EventType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery\Exception;
use Need;
use Sign;
use function Symfony\Component\Translation\t;

class RegistrantRepository
{
    // 获取渠道联动筛选后的渠道id
    public static function getFilterChannelIds(mixed $channel_id_arr)
    {
        // 初始化结果数组
        $department_one_id = [];
        $department_two_id = [];
        $channel_id = [];

        // 检查$user_id_arr是否不为空
        if (!empty($channel_id_arr)) {
            // 遍历$user_id_arr数组
            foreach ($channel_id_arr as $channel_id_element) {
                // 分割字符串，得到"."之前和之后的部分
                $parts = explode('.', $channel_id_element);
                if (count($parts) == 2) {
                    $prefix = $parts[0];
                    $number = $parts[1];
                    // 根据"."之前的部分决定将数字放入哪个数组
                    if ($prefix == 'one') {
                        $department_one_id[] = $number;
                    } elseif ($prefix == 'two') {
                        $department_two_id[] = $number;
                    } else {
                        $channel_id[] = $number;
                    }
                }
            }
        }

        // 查询User表，并根据$department_one_id中的ID获取主键ID
        if (!empty($department_one_id)) {
            $ids_one = Channel::query()->whereIn('department_one_id', $department_one_id)->pluck('id')->toArray();
            $channel_id = array_merge($channel_id, $ids_one);
        }

        // 查询User表，并根据$department_two_id中的ID获取主键ID
        if (!empty($department_two_id)) {
            $ids_two = Channel::query()->whereIn('department_two_id', $department_two_id)->pluck('id')->toArray();
            $channel_id = array_merge($channel_id, $ids_two);
        }

        // 去重$user_id数组
        return array_unique($channel_id);
    }

    //auditListBuilder
    public function auditListBuilder(Request $request, $type)
    {
        $event_id = $request->input('event_id');
        $position = $request->input('position');
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $organization_type = $request->input('organization_type');
        $organization_province = $request->input('organization_province');
        $identity = $request->input('identity');
        $name = $request->input('name');
        $phone = $request->input('phone');
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = $this->getFilterChannelIds($channel_id_arr);

        $user_id_arr = $request->input('user_id_arr');
        $user_id = AttendeeRepository::getFilterUserIds($user_id_arr);

        $is_audit_organization = $request->input('is_audit_organization');
        //是否已分配
        $is_assigned = $request->input('is_assigned');
        $is_assigned = $request->input('is_assigned');
        //审核状态
        $audit_status = $request->input('audit_status');
        $created_time = $request->input('created_time'); // Added created_time filter

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        //查询报名人及报名人下的参会人信息
        $query = Registrant::query()
            ->select('registrants.*', 'attendees.batch') // Ensure we are selecting registrant fields
            ->addSelect(DB::raw('MAX(attendees.id) as max_attendee_id'))
            ->with(['attendees',
                'attendees.channel:id,name,department_two_id',
                'attendees.user:id,real_name',
                'event:id,short_title,activity_type',
                'attendees.channel.department:id,name,parent_id,code',
                'attendees.channel.department.parent:id,name'])
            ->join('events', 'events.id', '=', 'registrants.event_id')
            ->join('attendees', 'attendees.registrant_id', '=', 'registrants.id') // Join with attendees
            ->orderBy('max_attendee_id', 'desc')
            ->groupBy('attendees.batch', 'registrants.id') // Group by batch and registrant ID
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('registrants.event_id', $event_id))
            ->when($organization, fn($query) => $query->where('registrants.organization', 'like', '%' . $organization . '%'))
            ->when($organization_code, fn($query) => $query->where('registrants.organization_code', 'like', '%' . $organization_code . '%'))
            ->when($organization_type, fn($query) => $query->where('registrants.organization_type', 'like', '%' . $organization_type . '%'))
            ->when($organization_province, fn($query) => $query->where('registrants.organization_province', 'like', '%' . $organization_province . '%'))
            ->when($position, fn($query) => $query->where('attendees.position', 'like', '%' . $position . '%'))
            ->when($identity, fn($query) => $query->where('registrants.identity', $identity))
            ->when($channel_id_arr, fn($query) => $query->whereIn('attendees.channel_id', $channel_id))
            ->when($name, fn($query) => $query->where('registrants.name', 'like', '%' . $name . '%'))
            ->when($phone, fn($query) => $query->where('registrants.phone', 'like', '%' . $phone . '%'))
            ->when($user_id_arr, fn($query) => $query->whereIn('attendees.user_id', $user_id))
            ->when($audit_status, function ($query, $audit_status) use ($type) {
                // Check if 1 is in audit_status array
                $query->whereIn('attendees.status', $audit_status);
                if ($type == 2 && in_array(1, $audit_status)) {
                    $query->where(function ($query) {
                        $query->where('attendees.status', '!=', 1)
                            ->orWhereNotNull('attendees.audit_user');
                    });
                }
            })
            ->when($created_time, function ($query) use ($created_time) {
                if (is_array($created_time) && count($created_time) === 2) {
                    $start_date = $created_time[0];
                    $end_date = $created_time[1];

                    // Adjust end_date to include the entire day
                    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));

                    $query->whereBetween('attendees.created_at', [$start_date, $end_date]);
                }
            });

        if ($is_audit_organization !== null) {
            $query->where(function ($query) use ($is_audit_organization) {
                if ($is_audit_organization == 1) {
                    $query->whereIn('registrants.organization', function ($subQuery) {
                        $subQuery->select('organization')
                            ->from('invite_organizations');
                    })->orWhereIn('registrants.organization_code', function ($subQuery) {
                        $subQuery->select('organization_code')
                            ->from('invite_organizations');
                    });
                } elseif ($is_audit_organization == 2) {
                    $query->whereNotIn('registrants.organization', function ($subQuery) {
                        $subQuery->select('organization')
                            ->from('invite_organizations');
                    })->orWhereNotIn('registrants.organization_code', function ($subQuery) {
                        $subQuery->select('organization_code')
                            ->from('invite_organizations');
                    });
                }
            });
        }

//        if ($type == 1) {
            $query = $this->getAuthFilterQuery($request, $query);
//        }
        if ($is_assigned != '') {
            //如果已分配，查询user_id不为空的数据
            $query->when($is_assigned == 1, fn($query) => $query->where('attendees.user_id', '<>', 0))
                //如果未分配，查询user_id为空的数据
                ->when($is_assigned == 2, fn($query) => $query->where('attendees.user_id', 0));
        }

        return $query;
    }

    public function registrantExportBuilder(Request $request, $type)
    {
        $event_id = $request->input('event_id');
        $organization = $request->input('organization');
        $organization_code = $request->input('organization_code');
        $organization_type = $request->input('organization_type');
        $organization_province = $request->input('organization_province');
        $position = $request->input('position');
        $identity = $request->input('identity');
        $name = $request->input('name');
        $phone = $request->input('phone');
        $channel_id_arr = $request->input('channel_id_arr');
        $channel_id = $this->getFilterChannelIds($channel_id_arr);
        $user_id_arr = $request->input('user_id_arr');
        $user_id = AttendeeRepository::getFilterUserIds($user_id_arr);
        $is_audit_organization = $request->input('is_audit_organization');

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);
        //是否已分配
        $is_assigned = $request->input('is_assigned');
        //审核状态
        $audit_status = $request->input('audit_status');
        //查询报名人及报名人下的参会人信息
        $query = Registrant::query()
            ->select('registrants.*', 'attendees.batch') // Ensure we are selecting registrant fields
            ->addSelect(DB::raw('MAX(attendees.id) as max_attendee_id'))
            ->with(['attendees' => function ($query) {
                $query->with('forums')->get()->map(function ($attendee) {
                    $attendee->forums_names = $attendee->forums->pluck('name')->toArray();
                    return $attendee;
                });
            },
                'attendees.channel:id,name,department_two_id',
                'user:id,real_name',
                'event:id,short_title,activity_type',
                'attendees.channel.department:id,name,parent_id,code',
                'attendees.channel.department.parent:id,name'])
            ->join('events', 'events.id', '=', 'registrants.event_id')
            ->join('attendees', 'attendees.registrant_id', '=', 'registrants.id') // Join with attendees
            ->orderBy('max_attendee_id', 'desc')
            ->groupBy('attendees.batch', 'registrants.id') // Group by batch and registrant ID
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('registrants.event_id', $event_id))
            ->when($organization, fn($query) => $query->where('registrants.organization', 'like', '%' . $organization . '%'))
            ->when($organization_code, fn($query) => $query->where('registrants.organization_code', 'like', '%' . $organization_code . '%'))
            ->when($organization_type, fn($query) => $query->where('registrants.organization_type', 'like', '%' . $organization_type . '%'))
            ->when($organization_province, fn($query) => $query->where('registrants.organization_province', 'like', '%' . $organization_province . '%'))
            ->when($identity, fn($query) => $query->where('registrants.identity', $identity))
            ->when($position, fn($query) => $query->where('attendees.position', 'like', '%' . $position . '%'))
            ->when($channel_id_arr, fn($query) => $query->whereIn('attendees.channel_id', $channel_id))
            ->when($name, fn($query) => $query->where('registrants.name', 'like', '%' . $name . '%'))
            ->when($phone, fn($query) => $query->where('registrants.phone', 'like', '%' . $phone . '%'))
            ->when($user_id_arr, fn($query) => $query->whereIn('attendees.user_id', $user_id))
            ->when($audit_status, function ($query, $audit_status) use ($type) {
                // Check if 1 is in audit_status array
                $query->whereIn('attendees.status', $audit_status);
                if ($type == 2 && in_array(1, $audit_status)) {
                    $query->where(function ($query) {
                        $query->where('attendees.status', '!=', 1)
                            ->orWhereNotNull('attendees.audit_user');
                    });
                }
            });

//        if ($type == 1) {
            $query = $this->getAuthFilterQuery($request, $query);
//        }

        if ($is_audit_organization !== null) {
            $query->where(function ($query) use ($is_audit_organization) {
                if ($is_audit_organization == 1) {
                    $query->whereIn('registrants.organization', function ($subQuery) {
                        $subQuery->select('organization')
                            ->from('invite_organizations');
                    })->orWhereIn('registrants.organization_code', function ($subQuery) {
                        $subQuery->select('organization_code')
                            ->from('invite_organizations');
                    });
                } elseif ($is_audit_organization == 2) {
                    $query->whereNotIn('registrants.organization', function ($subQuery) {
                        $subQuery->select('organization')
                            ->from('invite_organizations');
                    })->orWhereNotIn('registrants.organization_code', function ($subQuery) {
                        $subQuery->select('organization_code')
                            ->from('invite_organizations');
                    });
                }
            });
        }

        if ($is_assigned != '') {
            //如果已分配，查询user_id不为空的数据
            $query->when($is_assigned == 1, fn($query) => $query->where('attendees.user_id', '<>', 0))
                //如果未分配，查询user_id为空的数据
                ->when($is_assigned == 2, fn($query) => $query->where('attendees.user_id', 0));
        }
        return $query;
    }

    public function dropUserList(Request $request)
    {
        //查询所有的父级部门 value为部门id label为部门名称
        $departments = Department::where('status', 1)->where('parent_id', 0)->get();
        //查询父级部门下的子部门，并塞到$departments的children，把当前父级部门也放到children
        $departments->each(function ($department) {

            // 查询当前父级部门下的子部门
            $children = Department::where('status', 1)->where('parent_id', $department->id)->get();

            // 遍历子部门，为每个子部门查询并赋值其子用户
            $children = $children->map(function ($child) {
                $users = User::query()->where('department_two_id', $child->id)->get();
                // 将查询到的用户作为子部门的children
                $child->children = $users->map(function ($user) use ($child) {
                    return [
                        'value' => $child->id . '.' . $user->id,
                        'label' => $user->real_name,
                        // 这里可以添加更多用户字段
                    ];
                });
                return [
                    'value' => 'two.' . $child->id,
                    'label' => $child->name,
                    'children' => $child->children,
                    // 这里可以添加更多部门字段
                ];
            });

            // 查询当前父级部门的直接用户
            $users = User::query()
                ->where('department_one_id', $department->id)
                ->whereNull('department_two_id')
                ->get();
            // 将直接用户作为当前父级部门的children
            $department->children = $users->map(function ($user) use ($department) {
                return [
                    'value' => $department->id . '.' . $user->id,
                    'label' => $user->real_name,
                    // 这里可以添加更多用户字段
                ];
            })->concat($children->map(function ($child) {
                return [
                    'value' => $child['value'],
                    'label' => $child['label'],
                    'children' => $child['children'],
                ];
            }));
            // 将处理好的子部门列表赋值给父部门的children属性
//            $department->children = $children->map(function ($child) {
//                return [
//                    'value' => $child['value'],
//                    'label' => $child['label'],
//                    'children' => $child['children'],
//                ];
//            });
        });
        // 返回最终结果
        return $departments->map(function ($department) {
            return [
                'value' => 'one.' . $department->id,
                'label' => $department->name,
                'children' => $department->children,
            ];
        });
    }

    //分配对接人assignUser，传入ids和对接人user_id
    public function assignUser(Request $request)
    {
        $idsWithBatch = $request->input('ids');
        $attendees = collect();
        $user_id = $request->input('user_id');
        if (str_contains($user_id, '.')) {
            $user_id = explode('.', $user_id)[1];
        }

        $user = User::query()->find($user_id);

        // Loop through each id and batch pair
        foreach ($idsWithBatch as $item) {
            $id = $item['id'];
            $batch = $item['batch'];

            // 获取更新前的所有参会者
            $currentAttendees = Attendee::query()
                ->where('registrant_id', $id)
                ->where('batch', $batch)
                ->get();

            // Update registrant audit status
            $update_count =Attendee::query()
                ->where('registrant_id', $id)
                ->where('batch', $batch)
                ->update([
                    'user_id' => $user_id,
                    'assigner' => $request->user()->real_name,
                    'assign_at' => now(),
                    'updater' => $request->user()->real_name,
                    'updated_at' => now()
                ]);

            // 合并当前的参会者到总集合中
            $attendees = $attendees->merge($currentAttendees);
        }


        // 循环增加分配消息
        foreach ($attendees as $attendee) {
            $content = $attendee->user_id == 0 ? '新增' : '重新';
            $content .= "分配对接人为：" . $user->real_name;
            // 新增分配消息
            $message = new Message();
            $message->attendee_id = $attendee->id;
            $message->content = $content;
            $message->source = Message::$source_distribution_submit;
            $message->is_read = 2;
            $message->creator = $request->user()->real_name;
            $message->save();
        }

        $organizations = $attendees->map(function ($attendee) {
            return $attendee->organization;
        })->unique('organization')->join(', ');

        if($user->phone){
            $smsContent = "您好，real_name给您分配了 organizations 共count个客户，请及时前往后台跟进。";
            $params = [
                'real_name' => $request->user()->real_name,
                'organizations' => $organizations,
                'count' => $attendees->count()
            ];
            // 给商务发送短信
            singleSendJianzhouSms(Sign::SEEE, $user->phone, replaceParams($smsContent, $params));
        }

        return $update_count;
    }

    //auditRegistrant
    public function auditRegistrant(Request $request)
    {
        $idsWithBatch = $request->input('ids');
        $audit_status = $request->input('audit_status');
        $audit_remark = $request->input('audit_remark');

        // Initialize an empty array to store attendees
        $attendees = collect();
        $registrantPhones = collect();

        // Loop through each id and batch pair
        foreach ($idsWithBatch as $item) {
            $id = $item['id'];
            $batch = $item['batch'];

            // Update registrant audit status
            $update_count =Attendee::query()
                ->where('registrant_id', $id)
                ->where('batch', $batch)
                ->update([
                    'status' => $audit_status,
                    'audit_user' => $request->user()->real_name,
                    'audit_time' => now(),
                    'audit_remark' => $audit_remark,
                    'updater' => $request->user()->real_name,
                    'updated_at' => now()
                ]);
            // Collect attendees that match the conditions
            $attendees = $attendees->merge(
                Attendee::query()
                    ->where('registrant_id', $id)
                    ->where('batch', $batch)
                    ->get()
            );

            //推送微信通知
            //根据id查询报名人
            $registrant = Registrant::find($id);
            $registrantPhones->push($registrant->phone);

            $member = Member::find($registrant->member_id);
            $event = Event::find($registrant->event_id);
            $template_id = 'env-PfXWpE6sGJRMV7RDSCkbZBfuXcFrvyTlWSMiSsg';
            $formattedData = [
                "phrase1" => [
                    "value" => Attendee::convertAuditStatus($audit_status),
                ],
                "thing2" => [
                    "value" => $event -> title,
                ],
                "date3" => [
                    "value" => $event ->start_time,
                ],
                "thing4" => [
                    "value" => $event ->address
                ],
                "thing5" => [
                    "value" => $audit_status === 1 ? '您好，您报名的会议已审核通过' : '您好，您报名的会议已审核驳回，驳回原因：' . $audit_remark,
                ]
            ];
            $open_id = $member->open_id;
            $page = 'pages/customer/channel/channel';
            MiniAppRepository::sendMiniAppMessage($template_id,$formattedData,$open_id,$page);
        }

        if ($attendees->isNotEmpty()) {
            $event = Event::query()->where('id', $attendees->first()->event_id)->first();
            // Get attendees' phone numbers
            $attendee_phones = $attendees->pluck('phone')->toArray();
            // Merge with registrant phone numbers and remove duplicates
            $allPhones = collect($attendee_phones)->merge($registrantPhones)->unique()->toArray();

            $approvedSms = "您好，您报名的title已审核通过，将于startTime在address举行，点击前往：url";
            $rejectedSms = "您好，您报名的title已审核驳回，驳回原因：auditRemark，点击前往查看：url";
            $params = [
                'title' => $event->title,
                'startTime' => $event->start_time,
                'address' => $event->address,
                'auditRemark' => $audit_remark,
                'url' => getAppletUrlLink(null)
            ];

            // Select SMS content based on audit_status
            $smsContent = $audit_status === 1 ? $approvedSms : $rejectedSms;

            //判断$allPhones是否为空
            if (!empty($allPhones)) {
                batchSendJianzhouSms(Sign::SEEE, $allPhones, replaceParams($smsContent, $params));
            }
        }
        return $update_count;
    }

    //enrollInfo
    public function enrollInfo(Request $request)
    {
        $member_id = $request->user()->id;
        $registrant = Registrant::query()->where('member_id', $member_id)->first();
        $data = Registrant::query()
            ->with([
                'attendees',
                'attendees.forums',
                'channel:id,name,department_two_id',
                'channel.department:id,name,parent_id,code',
                'channel.department.parent:id,name',
                'user:id,real_name'
            ])->find($registrant->id);
        return $data;
    }
    //getRegistrantInfo
    public function getRegistrantInfo(Request $request, string $event_id)
    {
        $member_id = $request->user()->id;
        $registrant = Registrant::query()
            ->where('member_id', $member_id)
            ->where("event_id", $event_id)
            ->first();
        return $registrant;
    }


    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            //根据member_id、event_id查询是否已存在报名人，存在则更新，不存在新增
            $user = $request->user();
            $data = filterRequestData('registrants');
            Log::error('会议报名接口报名人: ' . json_encode($data));
            $event_id = $data['event_id'];
            $event = Event::findOrFail($event_id);
            $is_registration = $event->is_registration;
            if ($is_registration == 2) {
                return ERROR_RESPONSE_ARRAY('已超过会议报名时间，请联系相关对接人', NUMBER_LIMIT_CODE);
            }
            $registrant = Registrant::query()->where('member_id', $user->id)->where("event_id", $event_id)->first();

            $attendees = $request->input('attendees', []);
            Log::error('会议报名接口参会人: ' . json_encode($attendees));
            //检查参会人手机号是否有重复
//            $attendees = $this->checkAttendeePhone($attendees);

            $organization = $request->input('organization');
            $data['updater'] = $user->nick_name;
            $event_type = $event->type;
            if ($event_type == EventType::INVITE) {
                //查询当前邀约组织，是否在邀约名单中
                $invite_organization = InviteOrganization::query()->where('event_id', $event_id)->where('organization', $organization)->first();
                if (!$invite_organization) {
                    throw new Exception("当前邀约组织不在邀约名单中",NUMBER_LIMIT_CODE);
                }
            }

            //设置审核状态
            $status = $this->handleAuditStatus($data, $event, $organization, count($attendees));

            if ($registrant) {
                $registrant->fill($data)->update();
            }else{
                $data['creator'] = $user->nick_name;
                $data['member_id'] = $user->id;
                $registrant = Registrant::forceCreate($data);// 添加报名人信息
            }
            //查询当前报名人这个会议下的参会人批次，没有参会人则为首次报名 批次为1 否则为批次+1 批次在参会人表中
//            $batch = Attendee::query()->where('event_id', $event_id)->where('registrant_id', $registrant->id)->max('batch') ?? 1;
            // 查询当前报名人这个会议下的参会人批次，如果没有参会人则为首次报名，批次为1，否则为批次+1
            $batch = Attendee::query()
                ->where('event_id', $event_id)
                ->where('registrant_id', $registrant->id)
                ->max('batch');

            // 使用三元条件运算符来判断 $batch 是否为空，如果为空则设为1，否则加1
            $batch = $batch ? $batch + 1 : 1;

            $this->handleAttendees($attendees, $registrant, $data, $user->nick_name, $batch, $status);
            DB::commit();
            if (($event->type == EventType::LIMIT||$event->type == EventType::INVITE) && $status == AuditStatus::WAIT) {
                return AUDIT_RESPONSE_ARRAY("提交成功，待审核");
            }

            // 取$attendees 的手机号
            $phone_list = array_column($attendees, 'phone');
            $smsContent = "您好，您报名的title已成功，将于startTime在address举行，点击前往：url";
            $params = [
                'title' => $event->title,
                'startTime' => $event->start_time,
                'address' => $event->address,
                'url' => getAppletUrlLink(null)
            ];
            // 发送短信
            batchSendJianzhouSms(Sign::SEEE, $phone_list, replaceParams($smsContent, $params));

            //推送微信通知
            $template_id = 'mHXEthtQh2FPJtES1NU_950yJ6UDVxZ3X81BTFAk0Hk';
            $formattedData = [
                "thing2" => [
                    "value" => $event -> title,
                ],
                "date4" => [
                    "value" => $event ->start_time,
                ],
                "thing5" => [
                    "value" => $event ->address
                ],
                "phrase8" => [
                    "value" =>  Attendee::convertStatus($status)
                ]
            ];
            $open_id = $user->open_id;
            $page = 'pages/customer/channel/channel';
            MiniAppRepository::sendMiniAppMessage($template_id,$formattedData,$open_id,$page);

            return SUCCESS_RESPONSE_ARRAY("报名成功");
        } catch (\Exception $e) {
            DB::rollBack();
            return ERROR_RESPONSE_ARRAY($e->getMessage(), $e->getCode());
        }
    }
    //checkAttendeePhone
    private function checkAttendeePhone($attendees)
    {
        $phone_list = [];
        foreach ($attendees as $attendee) {
            if (in_array($attendee['phone'], $phone_list)) {
                throw new \Exception("参会人手机号重复，请修改数据，重新提交",REPEAT_DATA_CODE);
            }
            $phone_list[] = $attendee['phone'];
        }
        return $attendees;
    }

    private function handleAuditStatus(&$data, $event, $organization, $attendeesCount)
    {
        $status = AuditStatus::PASS;
        //针对限制类型的会议，判断当前单位是否在邀请单位列表中，如果当前单位不在邀请单位列表中，审核状态为待审核，否则根据当前单位参会人数和邀请单位人数判断审核状态
        if ($event->type == EventType::LIMIT||$event->type == EventType::INVITE) {
            // 获取邀请单位和当前单位的参会人数量
            $invite_units = InviteOrganization::query()->where('event_id', $data['event_id'])->get();
            $attendee_count = Attendee::query()->where('event_id', $data['event_id'])->whereIn('status', [1,3])->where('organization', $organization)->count();

            // 处理审核状态，如果当前单位不在邀请单位列表中，审核状态为待审核，否则根据当前单位参会人数和邀请单位人数判断审核状态
            if (!$invite_units->contains('organization', $organization)) {
//                $data['audit_status'] = AuditStatus::WAIT;
                return AuditStatus::WAIT;
            }
            $total_count = $attendee_count + $attendeesCount;
            $invite_count = $invite_units->where('organization', $organization)->first()->number;
            if ($total_count > $invite_count) {
                throw new Exception("超过参会人数限制",NUMBER_LIMIT_CODE);
            }
            //邀约单位报名是否需要审核
            if ($event->is_audit_organization == Need::NEED) {
                return AuditStatus::WAIT;
            }
//            $data['audit_status'] = $total_count <= $invite_count ? AuditStatus::PASS : AuditStatus::WAIT;
            $status = $total_count <= $invite_count ? AuditStatus::PASS : AuditStatus::WAIT;
        }
        return $status;
    }

    private function handleAttendees($attendees, $registrant, &$data, $user, $batch, $status)
    {
        foreach ($attendees as $attendee) {

            //todo 查询参会人是否已报名
//            $model = Attendee::query()->where('event_id', $data['event_id'])->whereNotIn('status', [2, 4])->where('phone', $attendee['phone'])->first();
//            if ($model) {
//                throw new Exception('参会人手机号：' . $attendee['phone'] . '，已成功报名，报名人：' . $attendee['name'] . '，请勿重复报名', REPEAT_DATA_CODE);
//            }

            $attendeeData = [
                'event_id' => $data['event_id'],
                'registrant_id' => $registrant->id,
                'name' => $attendee['name'],
                'phone' => $attendee['phone'],
                'organization' => $attendee['organization'] ?? '',
                'organization_code' => $attendee['organization_code'] ?? '',
                'organization_type' => $attendee['organization_type'] ?? '',
                'organization_province' => $attendee['organization_province'] ?? '',
                'position' => $attendee['position'],
                'gender' => $attendee['gender'],
                'creator' => $user,
                'updater' => $user,
//                'status' => $data['audit_status'], $registrant->audit_status == AuditStatus::PASS ? AuditStatus::PASS : AuditStatus::REJECT,
                'status' => $status,
                'check_in_status' => 2,
                'batch' => $batch,
                'channel_id' => $data['channel_id'] ?? 0,
            ];
            // 如果 $attendee['id'] 存在，则设置 'id' 键
            if (isset($attendee['id'])) {
                $attendeeData['id'] = $attendee['id'];
            }
            if ($attendee['phone']) {
                //查询当前手机号是否已有member
                $attendeeMember = Member::query()->where('phone', $attendee['phone'])->first();
                if ($attendeeMember) {
                    $attendeeData['member_id'] = $attendeeMember->id;
                }
            }

            // 创建参会人
            $newAttendee = Attendee::create($attendeeData);

            // 处理论坛
            $forums = $attendee['forums'];
            $this->handleForums($forums, $newAttendee->id, $data['event_id']);

            // 判断是否有酒店信息
            if (isset($attendee['hotel'])) {
                // 处理酒店
                $hotel = $attendee['hotel'];
                $this->handleHotel($hotel, $newAttendee);
            }
        }
    }

    // 报名页面添加参会人住宿酒店信息
    private function handleHotel($hotel, $attendee)
    {
        try {
            if($hotel && $hotel['is_need_hotel']==1) {
                $hotel['attendee_id'] = $attendee->id;
                $hotel['type'] = 1;
                $hotel['booking_room_number'] = 1;
                $hotel['creator'] = '报名添加';
                AttendeeHotel::forceCreate($hotel);
            }
        } catch (\Exception $e) {
            throw new Exception('参会人【' . $attendee->name . '】住宿酒店信息添加失败');
        }
    }

    private function handleForums($forums, $attendeeId, $eventId)
    {
        // 遍历 $forums，处理新增和删除逻辑
        foreach ($forums as $forum) {
            // 检查记录是否存在
            $existingForum = AttendeeForums::where('event_id', $eventId)
                ->where('forum_id', $forum['id'])
                ->where('attendee_id', $attendeeId)
                ->exists();

            if (!$existingForum) {
                // 获取论坛的最大参会人数
                $formObj = Forum::where('id', $forum['id'])->first();
                //获取number
                $maxAttendees = $formObj->number;

                // 计算当前论坛的参会人数
//                $currentAttendeesCount = AttendeeForums::where('forum_id', $forum['id'])->whereIn('audit_status', [1,2])->count();
                // 计算当前论坛的参会人数
                $currentAttendeesCount = AttendeeForums::where('forum_id', $forum['id'])
                    ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->where('type', 2)
                                ->where('audit_status', 1);
                        })->orWhere(function ($query) {
                            $query->where('type', 1)
                                ->where('intention', 1);
                        });
                    })
                    ->count();

                if ($currentAttendeesCount >= $maxAttendees){
                    throw new Exception($formObj['name'] . '论坛参会人数已超过限制，需修改后进行提交', NUMBER_LIMIT_CODE);
                }

                // 如果记录不存在，新增数据
                AttendeeForums::create([
                    'event_id' => $eventId,
                    'forum_id' => $forum['id'],
                    'attendee_id' => $attendeeId,
                    'audit_status' => $forum['is_audit'] == Need::NEED? 2 : 1,
                    'type' => 2,
                    'intention' => 0,
                ]);
            }
        }

        // 删除多余的数据
        AttendeeForums::where('event_id', $eventId)
            ->where('attendee_id', $attendeeId)
            ->whereNotIn('forum_id', array_column($forums, 'id'))
            ->delete();

    }

    /**
     * @param Request $request
     * @param \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder|\Illuminate\Support\HigherOrderWhenProxy|Registrant $query
     * @return Registrant|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder|\Illuminate\Support\HigherOrderWhenProxy
     */
    public static function getAuthFilterQuery(Request $request, \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder|\Illuminate\Support\HigherOrderWhenProxy|Registrant $query): Registrant|\Illuminate\Support\HigherOrderWhenProxy|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder
    {
        $user = $request->user();
        // 如果用户角色是事业部负责人，则查询所有数据；如果用户角色是销售，则只显示邀约渠道是本人的，以及对接人是本人的数据
        if ($user->hasRole('系统管理员')) {

        } elseif ($user->hasRole('事业部负责人') && $user->hasRole('无渠道分配人')) {
            //事业部负责人只能看到邀约渠道是所属事业部的数据，以及对接人是所属事业部的数据
            $department_id = $user->department_one_id;
            //查询channel表中department_one_id为$department_id
            $channel_ids = Channel::query()->where('department_one_id', $department_id)->pluck('id');
            $user_ids = User::query()->where('department_one_id', $department_id)->pluck('id');
            $query->where(function ($query) use ($channel_ids, $user_ids) {
                $query->whereIn('attendees.channel_id', $channel_ids)
                    ->orWhere('attendees.channel_id', 0)
                    ->orWhereIn('attendees.user_id', $user_ids);
            });
        } elseif ($user->hasRole('事业部负责人')) {
            //事业部负责人只能看到邀约渠道是所属事业部的数据，以及对接人是所属事业部的数据
            $department_id = $user->department_one_id;
            //查询channel表中department_one_id为$department_id
            $channel_ids = Channel::query()->where('department_one_id', $department_id)->pluck('id');
            $user_ids = User::query()->where('department_one_id', $department_id)->pluck('id');
            $query->where(function ($query) use ($channel_ids, $user_ids) {
                $query->whereIn('attendees.channel_id', $channel_ids)
                    ->orWhereIn('attendees.user_id', $user_ids);
            });
        }elseif ($user->hasRole('部门负责人') && $user->hasRole('无渠道分配人')) {
            //部门负责人只能看到邀约渠道是所属二级部门的数据，以及对接人是所属二级部门的数据
            $department_id = $user->department_two_id;
            //查询channel表中department_one_id为$department_id
            $channel_ids = Channel::query()->where('department_two_id', $department_id)->pluck('id');
            $user_ids = User::query()->where('department_two_id', $department_id)->pluck('id');
            $query->where(function ($query) use ($channel_ids, $user_ids) {
                $query->whereIn('attendees.channel_id', $channel_ids)
                    ->orWhere('attendees.channel_id', 0)
                    ->orWhereIn('attendees.user_id', $user_ids);
            });
        } elseif ($user->hasRole('部门负责人')) {
            //部门负责人只能看到邀约渠道是所属二级部门的数据，以及对接人是所属二级部门的数据
            $department_id = $user->department_two_id;
            //查询channel表中department_one_id为$department_id
            $channel_ids = Channel::query()->where('department_two_id', $department_id)->pluck('id');
            $user_ids = User::query()->where('department_two_id', $department_id)->pluck('id');
            $query->where(function ($query) use ($channel_ids, $user_ids) {
                $query->whereIn('attendees.channel_id', $channel_ids)
                    ->orWhereIn('attendees.user_id', $user_ids);
            });
        } elseif ($user->hasRole('商务')) {
            //如果用户id等于10，查询attendees.organization_type是省级考试机构或者attendees.user_id是自己的数据
            if ($user->id == 58) {
                $query = $query->where(function ($query) use ($user) {
                    $query->where('attendees.organization_type', "省级考试机构")
                        ->orWhere('attendees.user_id', $user->id);
                });
            }else{
                $query = $query->where('attendees.user_id', $user->id);
            }
        } else if ($user->hasRole( '无渠道分配人')) {// 登录用户是无渠道分配人角色
            $query = $query->where('attendees.channel_id', 0);
        }
        return $query;
    }

}
