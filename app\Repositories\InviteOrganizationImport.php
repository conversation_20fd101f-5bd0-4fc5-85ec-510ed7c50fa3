<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/9
 * Time 9:38
 */

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\InviteOrganization;
use App\Models\Registrant;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\ToModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class InviteOrganizationImport implements ToModel,WithMultipleSheets
{
    private $event_id;
    private $creator;
    private $updater;
    private $rowNumber = 0;
    private $importedData = [];
    private $failedData = [];
    private $updatedData = [];
    private $deletedData = [];
    private $sheetNameOrIndex;
    private $existingDatabaseData = [];

    public function __construct($event_id, Request $request, $sheetNameOrIndex = 0)
    {
        $this->event_id = $event_id;
        $this->creator = $request->user()->real_name;
        $this->updater = $request->user()->real_name;
        $this->sheetNameOrIndex = $sheetNameOrIndex; // 接收要导入的sheet名称或索引
        // 预先获取该事件ID下的所有组织信息
        $this->existingDatabaseData = InviteOrganization::where('event_id', $this->event_id)->get()->keyBy(function ($item) {
            return $item->organization;
        });
    }

    public function sheets(): array
    {
        return [
            $this->sheetNameOrIndex => $this
        ];
    }

    public function model(array $row)
    {
        // 忽略第一行（标题行）
        if ($this->rowNumber == 0) {
            $this->rowNumber++;
            return null;
        }

        $organization_code = $row[0];
        $organization = $row[1];
        $organization_type = $row[2];
        $number = $row[3];
        $province = $row[4];
        $key = $organization;

        // 检查当前组织是否已存在于数据库中
        if (isset($this->existingDatabaseData[$key])) {
            $existingData = $this->existingDatabaseData[$key];

            // 检查number是否变化
            if ($existingData->number != $number|| $existingData->organization_type != $organization_type|| $existingData->organization_code != $organization_code|| $existingData->province != $province) {
                if ($existingData->organization_type != $organization_type || $existingData->organization_code != $organization_code|| $existingData->province != $province) {
                    //更新以报名用户的数据
                    $this->handleTypeOrCodeChange($existingData, $organization_type, $organization_code, $province);
                }
                // 如果number发生变化，则更新数据
                $existingData->number = $number;
                $existingData->organization_type = $organization_type;
                $existingData->organization_code = $organization_code;
                $existingData->province = $province;
                $existingData->save();
                $this->updatedData[] = $existingData;
            } else {
                // 如果已经存在且没有变化，标记为重复数据
                $this->failedData[] = [
                    'organization_code' => $organization_code,
                    'organization' => $organization,
                    'organization_type' => $organization_type,
                    'number' => $number,
                    'province' => $province,
                    'created_at' => $existingData->created_at,
                    'reason' => '重复数据',
                ];
            }

            // 从预先加载的数据中移除，防止最后删除它
            unset($this->existingDatabaseData[$key]);

            return null; // 不插入新的数据
        }

        /*// 检查当前单位是否已经存在于数据库中
        $existingData = InviteOrganization::where('event_id', $this->event_id)->where('organization', $organization)->first();

        if ($existingData) {
            // 检查number是否变化
            if ($existingData->number != $number) {
                // 如果number发生变化，则更新数据
                $existingData->number = $number;
                $existingData->save();
                $this->updatedData[] = $existingData;
                return null; // 不插入数据库
            }
            // 如果已经存在，则将其添加到重复数据集合中
            $this->failedData[] = [
                'organization_code' => $organization_code,
                'organization' => $organization,
                'organization_type' => $organization_type,
                'number' => $number,
                'created_at' => $existingData->created_at,
                'reason' => '重复数据',
            ];
            return null; // 不插入数据库
        }*/

        // 创建新的 InviteOrganization 实例并插入数据库
        $imported = new InviteOrganization([
            'event_id' => $this->event_id,
            'organization_code' => $organization_code,
            'organization' => $organization,
            'organization_type' => $organization_type,
            'number' => $number,
            'province' => $province,
            'creator' => $this->creator,
            'updater' => $this->updater,
        ]);

        // 将导入成功的数据保存到数组中
        $this->importedData[] = $imported->toArray();

        // 从预先加载的数据中移除，表示该条记录不需要删除
        unset($this->existingDatabaseData[$key]);

        return $imported;
    }

    /**
     * 执行当 organization_type 或 organization_code 变化时的操作
     */
    private function handleTypeOrCodeChange($existingData, $newType, $newCode, $newProvince)
    {
        // 自定义的处理逻辑，例如记录日志、发送通知等
        // 这里可以添加任何你需要的操作，比如：
        // Log::info("组织 {$existingData->organization} 的类型或代码发生了变化");
        $organization = $existingData->organization;
        //查询报名人中的邀约单位，如果有邀请人，则更新其邀约单位信息
        $attendees = Attendee::where('event_id', $this->event_id)->where('organization', $organization)->get();
        $attendees->each(function ($attendee) use ($newType, $newCode, $newProvince) {
            $attendee->organization_type = $newType;
            $attendee->organization_code = $newCode;
            $attendee->organization_province = $newProvince;
            $attendee->save();
        });
        //更新Registrant
        $registrants = Registrant::where('event_id', $this->event_id)->where('organization', $organization)->get();
        $registrants->each(function ($registrant) use ($newType, $newCode,$newProvince) {
            $registrant->organization_type = $newType;
            $registrant->organization_code = $newCode;
            $registrant->organization_province = $newProvince;
            $registrant->save();
        });
    }

    public function getImportedData(): Collection
    {
        // 返回导入成功的数据集合
        return collect($this->importedData);
    }
    public function getFailedData(): Collection
    {
        // 返回重复数据集合
        return collect($this->failedData);
    }
    public function getUpdatedData(): Collection
    {
        // 返回更新成功的数据集合
        return collect($this->updatedData);
    }
    public function getDeletedData(): Collection
    {
        // 返回已删除的数据集合
        return collect($this->deletedData);
    }
    public function removeMissingData()
    {
        // 删除导入文件中不存在的数据库记录
        foreach ($this->existingDatabaseData as $data) {
            $this->deletedData[] = $data;  // 将要删除的数据保存到deletedData中
            $data->delete();
        }
    }
}
