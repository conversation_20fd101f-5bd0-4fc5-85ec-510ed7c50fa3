<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * php artisan make:seed UsersTableSeeder
     * php artisan db:seed --class=UsersTableSeeder
     */
    public function run(): void
    {
        //
        $user = User::forceCreate([
            'username' => '<EMAIL>',
            'real_name' => '周云峰',
            'password' => bcrypt('114study'),
            'api_token' => bcrypt(time()),
            'state' => 1,
//            'guard_name' => 'api',
            'creator' => '系统管理员',
            'updater' => '系统管理员',
        ]);

        User::forceCreate([
            'username' => '<EMAIL>',
            'real_name' => '潘庆强',
            'password' => bcrypt('114study'),
            'api_token' => bcrypt(time()),
            'state' => 1,
//            'guard_name' => 'api',
            'creator' => '系统管理员',
            'updater' => '系统管理员',
        ]);

        dump($user->toArray());
    }
}
