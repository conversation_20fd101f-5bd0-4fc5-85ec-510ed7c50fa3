<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Permission\PermissionRegistrar;

class Role extends Model
{
    use HasFactory;

    const LEVEL1 = [
        'system' => '系统管理',
        'data' => '基础数据管理',
        'serve' => '会务服务管理',
        'event' => '会务管理',
        'stat' => '数据看板',
        'orders' => '订单管理',
        'website' => 'SEEE官网管理',
    ];
    const LEVEL2 = [
        'users' => '用户管理',
        'roles' => '角色管理',

        'dep' => '部门管理',
        'channels' => '邀约渠道管理',
        'kpis' => '邀约指标管理',
        'dine' => '就餐管理',
        'hotel' => '酒店住宿管理',

        'attendee' => '参会人对接服务列表',
        'dines' => '就餐名单服务列表',
        'hotels' => '酒店住宿名单服务列表',
        'interviews' => '采访名单服务列表',
        'pickUps' => '接站名单服务列表',
        'dropOffs' => '送站名单服务列表',
        'notices' => '会务通知列表',

        'events' => '会务列表',
        'registrants' => '会务报名列表',
        'attendee_forums' => '论坛报名列表',
        'registrants_audit' => '非邀约单位报名审核列表',
        'invite_registrants_audit' => '定向邀约单位报名审核列表',

        'allDep' => '数据看板-总板',
        'oneDep' => '数据看板-事业部',
        'twoDep' => '数据看板-部门',
        'business' => '数据看板-商务',

        'order' => '费用支付列表',
        'refundOrder' => '退款申请列表',
        'invoice' => '发票审核列表',
        'payment_record' => '会议缴费记录',

        'posters' => '海报审核',
    ];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    function menus()
    {
        return $this->belongsToMany(Menu::class, 'role_has_menus', 'role_id', 'menu_id');
    }

    public function users(): BelongsToMany
    {
        return $this->morphedByMany(
            "App\Models\User",
            'model',
            'model_has_roles',
            'role_id',
            'model_id'
        );
    }
}
