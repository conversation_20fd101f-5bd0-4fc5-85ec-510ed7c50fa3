<?php

namespace App\Http\Controllers\Admin\Data;

use App\Http\Controllers\Controller;
use App\Models\AttendeeDine;
use App\Models\Dine;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class DineController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // 用餐列表
        $event_id = $request->input('event_id');
        $dine_date = $request->input('dine_date');
        $location = $request->input('location');

        // 获取当前登录用户部门关联的会议活动类型
        $activityTypes = (new UserRepository)->getUserHasEventActivityTypes($request);

        $builder = Dine::join('events', 'events.id', '=', 'dines.event_id')
            ->select('dines.*', 'events.title as event_title', 'events.short_title as event_short_title')
            ->whereIn('events.activity_type', $activityTypes)
            ->when($event_id, fn($query) => $query->whereIn('event_id', $event_id))
            ->when($dine_date, fn($query) => $query->where('dine_date', $dine_date))
            ->when($location, fn($query) => $query->where('location', $location));
        $cnt = $builder->count();
        $list = $builder->pagination()->orderBy('id', 'desc')->get();
        return SUCCESS_RESPONSE_ARRAY(compact('list', 'cnt'));
    }

    /**
     * 会议下的餐饮信息
     */
    public function dinesByEventId(string $event_id)
    {
        $builder = Dine::where('event_id', $event_id)
            ->orderBy('id', 'desc')->get();

        return SUCCESS_RESPONSE_ARRAY($builder);
    }

    /**
     * 会议下的餐饮信息
     */
    public function dropDineDatesByEventId(Request $request)
    {
        $event_id = $request->input('event_id');
        $builder = Dine::where('event_id', $event_id)
            ->select('dine_date')
            ->groupBy('dine_date')
            ->orderBy('dine_date')->get();

        return SUCCESS_RESPONSE_ARRAY($builder);
    }

    /**
     * 会议下的餐饮信息
     */
    public function dropDineLocationsByEventId(Request $request)
    {
        $event_id = $request->input('event_id');
        $builder = Dine::where('event_id', $event_id)
            ->select('location')
            ->groupBy('location')
            ->get();

        return SUCCESS_RESPONSE_ARRAY($builder);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 新增用餐
        $data = filterRequestData('dines');
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $dine = Dine::forceCreate($data);
        return SUCCESS_RESPONSE_ARRAY(compact('dine'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // 修改用餐
        $dine = Dine::find($id);
        if (!$dine) {
            return FAIL_RESPONSE_ARRAY('用餐不存在');
        }
        $data = filterRequestData('dines');
        $data['updater'] = $request->user()->real_name;
        $dine->fill($data)->update();
        return SUCCESS_RESPONSE_ARRAY(compact('dine'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // 删除用餐
        $dine = Dine::find($id);
        if (!$dine) {
            return FAIL_RESPONSE_ARRAY('用餐不存在');
        }
        $dine->delete();
        return SUCCESS_RESPONSE_ARRAY(compact('dine'));
    }
}
