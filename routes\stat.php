<?php

use App\Http\Controllers\Admin\Stat\BusinessDataStatController;
use App\Http\Controllers\Admin\Stat\DepAllDataStatController;
use App\Http\Controllers\Admin\Stat\DepOneDataStatController;
use App\Http\Controllers\Admin\Stat\DepTwoDataStatController;
use Illuminate\Support\Facades\Route;

// 数据看板
Route::group(['prefix' => 'stat'], function () {

    // 各事业部会议指标数据统计
    Route::get('allDep/eventKpi', [DepAllDataStatController::class, 'getAllDeptEventKpiDataStat'])->name('stat.allDep.eventKpi');
    // 各事业部论坛指标数据统计
    Route::get('allDep/forumKpi', [DepAllDataStatController::class, 'getAllDepForumKpiDataStat'])->name('stat.allDep.forumKpi');
    // 各事业部论坛总指标数据统计
    Route::get('allDep/forumAllKpi', [DepAllDataStatController::class, 'getAllDepForumAllKpiDataStat'])->name('stat.allDep.forumAllKpi');
    // 各事业部参会人员身份统计
    Route::get('allDep/attendeeIdentity', [DepAllDataStatController::class, 'getAllDepAttendeeIdentityStat'])->name('stat.allDep.attendeeIdentity');
    // 各事业部近七天邀约参会人人数统计
    Route::get('allDep/near7DaysAttendee', [DepAllDataStatController::class, 'getAllDepNear7DaysAttendeeStat'])->name('stat.allDep.near7DaysAttendee');
    // 各事业部酒店预定数据统计
    Route::get('allDep/hotel/booking', [DepAllDataStatController::class, 'getAllDepHotelBookingDataStat'])->name('stat.allDep.hotel.booking');
    // 各事业部某个酒店各事业部入住人数统计
    Route::get('allDep/hotel/checkInNum', [DepAllDataStatController::class, 'getAllDepHotelCheckInNumStat'])->name('stat.allDep.hotel.checkInNum');
    // 各事业部用餐数据统计
    Route::get('allDep/dine', [DepAllDataStatController::class, 'getAllDepDineDataStat'])->name('stat.allDep.dine');


    // 事业部下各二级部门部会议指标数据统计
    Route::get('oneDep/eventKpi', [DepOneDataStatController::class, 'getOneDeptEventKpiDataStat'])->name('stat.oneDep.eventKpi');
    // 事业部下各二级部门论坛指标数据统计
    Route::get('oneDep/forumKpi', [DepOneDataStatController::class, 'getOneDepForumKpiDataStat'])->name('stat.oneDep.forumKpi');
    // 事业部下各二级部门论坛总指标数据统计
    Route::get('oneDep/forumOneKpi', [DepOneDataStatController::class, 'getOneDepForumAllKpiDataStat'])->name('stat.oneDep.forumAllKpi');
    // 事业部下各二级部门参会人员身份统计
    Route::get('oneDep/attendeeIdentity', [DepOneDataStatController::class, 'getOneDepAttendeeIdentityStat'])->name('stat.oneDep.attendeeIdentity');
    // 事业部下各二级部门近七天邀约参会人人数统计
    Route::get('oneDep/near7DaysAttendee', [DepOneDataStatController::class, 'getOneDepNear7DaysAttendeeStat'])->name('stat.oneDep.near7DaysAttendee');
    // 事业部下各二级部门酒店预定数据统计
    Route::get('oneDep/hotel/booking', [DepOneDataStatController::class, 'getOneDepHotelBookingDataStat'])->name('stat.oneDep.hotel.booking');
    // 事业部下各二级部门某个酒店各事业部入住人数统计
    Route::get('oneDep/hotel/checkInNum', [DepOneDataStatController::class, 'getOneDepHotelCheckInNumStat'])->name('stat.oneDep.hotel.checkInNum');
    // 事业部下各二级部门用餐数据统计
    Route::get('oneDep/dine', [DepOneDataStatController::class, 'getOneDepDineDataStat'])->name('stat.oneDep.dine');
    // 事业部下各二级部门客户跟进数据统计
    Route::get('oneDep/followNum', [DepOneDataStatController::class, 'getOneDepFollowDataStat'])->name('stat.oneDep.followNum');
    // 事业部下各商务的客户跟进数据统计
    Route::get('oneDep/businessFollowNum', [DepOneDataStatController::class, 'getOneDepBusinessFollowDataStat'])->name('stat.oneDep.businessFollowNum');


    // 部门会议指标数据统计
    Route::get('twoDep/eventKpi', [DepTwoDataStatController::class, 'getTwoDeptEventKpiDataStat'])->name('stat.twoDep.eventKpi');
    // 部门论坛指标数据统计
    Route::get('twoDep/forumKpi', [DepTwoDataStatController::class, 'getTwoDepForumKpiDataStat'])->name('stat.twoDep.forumKpi');
    // 部门论坛总指标数据统计
    Route::get('twoDep/forumTwoKpi', [DepTwoDataStatController::class, 'getTwoDepForumAllKpiDataStat'])->name('stat.twoDep.forumAllKpi');
    // 部门参会人员身份统计
    Route::get('twoDep/attendeeIdentity', [DepTwoDataStatController::class, 'getTwoDepAttendeeIdentityStat'])->name('stat.twoDep.attendeeIdentity');
    // 部门近七天邀约参会人人数统计
    Route::get('twoDep/near7DaysAttendee', [DepTwoDataStatController::class, 'getTwoDepNear7DaysAttendeeStat'])->name('stat.twoDep.near7DaysAttendee');
    // 部门酒店预定数据统计
    Route::get('twoDep/hotel/booking', [DepTwoDataStatController::class, 'getTwoDepHotelBookingDataStat'])->name('stat.twoDep.hotel.booking');
    // 部门某个酒店各事业部入住人数统计
    Route::get('twoDep/hotel/checkInNum', [DepTwoDataStatController::class, 'getTwoDepHotelCheckInNumStat'])->name('stat.twoDep.hotel.checkInNum');
    // 获取当前二级部门下的参会人用餐数据
    Route::get('twoDep/attendeeDineList', [DepTwoDataStatController::class, 'getAttendeeDineListByTwoDepBusiness'])->name('stat.twoDep.attendeeDineList');
    // 部门下各商务的客户跟进数据统计
    Route::get('twoDep/businessFollowNum', [DepTwoDataStatController::class, 'getTwoDepBusinessFollowDataStat'])->name('stat.twoDep.businessFollowNum');


    // 商务对接客户的住宿数据统计
    Route::get('business/attendeeHotel', [BusinessDataStatController::class, 'getAttendeeHotelStatByBusiness'])->name('stat.business.attendeeHotel');
    // 商务对接客户住宿信息填写不完整的参会人列表
    Route::get('business/attendeeHotel/notFullList', [BusinessDataStatController::class, 'getAttendeeHotelInfoNotFullListByBusiness'])->name('stat.business.attendeeHotel.notFullList');
    // 获取商务对接的参会人的用餐信息
    Route::get('business/attendeeDine', [BusinessDataStatController::class, 'getAttendeeDineListByBusiness'])->name('stat.business.attendeeDine');
    // 获取商务跟进信息统计
    Route::get('business/followDataStat', [BusinessDataStatController::class, 'getFollowDataStatByBusiness'])->name('stat.business.followDataStat');
    // 获取某商务近七天的跟进数
    Route::get('business/near7DaysFollowNum', [BusinessDataStatController::class, 'getNear7DaysFollowNumByBusiness'])->name('stat.business.near7DaysFollowNum');


});
