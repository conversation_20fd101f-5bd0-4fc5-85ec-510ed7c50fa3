<?php

namespace App\Http\Controllers\Applet;

use App\Http\Controllers\Controller;
use App\Repositories\EventWayRepository;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Http\Request;

class EventWayController extends Controller
{
    // 获取用户所有已报名的会议
    public function getMemberCheckInEvents(Request $request, EventWayRepository $repository)
    {
        return $repository->getMemberCheckInEvents($request);
    }

    // 获取用户当前会议的参会通道信息
    public function getMemberEventWayInfo(Request $request, EventWayRepository $repository)
    {
        return $repository->getMemberEventWayInfo($request);
    }

    public function updatePickup(Request $request, string $id, EventWayRepository $repository)
    {
        // 提交客户更新接站信息消息
        return $repository->updatePickup($request, $id);
    }

    public function updateDropOff(Request $request, string $id, EventWayRepository $repository)
    {
        // 提交客户更新送站信息消息
        return $repository->updateDropOff($request, $id);
    }

    public function updateHotel(Request $request, string $id, EventWayRepository $repository)
    {
        // 提交客户更新酒店住宿信息消息
        return $repository->updateHotel($request, $id);
    }

    public function updateDine(Request $request, EventWayRepository $repository)
    {
        // 提交客户更新就餐信息消息
        return $repository->updateDine($request);
    }

    // 获取所有酒店信息（客户修改酒店信息页：酒店下拉列表）
    public function getDropHotels(Request $request, EventWayRepository $repository)
    {
        return $repository->getDropHotels($request);
    }

    // 获取酒店订单待支付详情页信息
    public function getHotelOrderInfo(Request $request, EventWayRepository $repository)
    {
        return $repository->getHotelOrderInfo($request);
    }

    // 会议签到
    public function eventCheckIn(Request $request, EventWayRepository $repository)
    {
        return $repository->eventCheckIn($request);
    }

    // 用餐签到
    public function dineCheckIn(Request $request, EventWayRepository $repository)
    {
        return $repository->dineCheckIn($request);
    }

    // 获取顶部提示消息
    public function getTopTips(Request $request, EventWayRepository $repository)
    {
        return $repository->getTopTips($request);
    }

}
