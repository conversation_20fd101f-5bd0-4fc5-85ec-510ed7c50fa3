<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_notices', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('type')->default(0)->comment('通知类型，1:短信通知 2:小程序通知');
            $table->unsignedInteger('event_id')->default(0)->comment('会议id');
            $table->string('name')->default('')->comment('通知名称');
            $table->string('sign')->default('')->comment('短信签名');
            $table->string('filter_json')->default('')->comment('筛选条件Json字符串');
            $table->text('content')->nullable()->comment('发送内容');
            $table->tinyInteger('send_type')->default(1)->comment('发送类型，1:延时发送 2:立即发送');
            $table->timestamp('delay_time')->nullable()->comment('延时发送时间');
            $table->tinyInteger('status')->default(2)->comment('状态：1:已执行 2:未执行');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `sms_notices` comment '会议短信通知表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notices');
    }
};
