<?php

namespace App\Console\Commands;

use App\Models\Attendee;
use App\Models\Event;
use App\Repositories\NoticeRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Sign;

class SmsSendEveryDay18Command extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sms-send-every-day18-command';

    /**
     * The console command description.
     *
     * @var string
     *
     */
    protected $description = '创建每天18点定时短信发送任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 给会议将要开始的参会人发送短信
        $this->sendEventStartSms();

        return 0;
    }

    // 给参加的会议将要开始的参会人发送短信
    function sendEventStartSms()
    {
        $query = Attendee::join('events', 'events.id', '=', 'attendees.event_id')
            ->select('attendees.phone', 'attendees.event_id')
            ->where('attendees.status', Attendee::$status_ok)
            ->where('events.is_on', 1)
            // 查找明天开始的会议数据
            ->whereRaw("DATE(events.start_time) = ?", Carbon::now()->addDay()->toDateString());

        // 获取符合条件数据的event_id数组
        $commonQuery = $query->pluck('event_id')->toArray();

        if (!empty($commonQuery)) {
            // 使用array_unique在查询阶段就避免重复，而不是在后续处理中
            $uniqueEventIds = array_unique($commonQuery);
            foreach ($uniqueEventIds as $event_id) {
                $event = Event::find($event_id);
                $phones = $query->where('attendees.event_id', $event_id)->pluck('phone')->toArray();
                $smsContent = "您好，您报名的title即将于time在address举行，请及时前往。";
                $params = [
                    'title' => $event->title,
                    'time' => $event->start_time,
                    'address' => $event->address
                ];
                // 发送短信
                batchSendJianzhouSms(Sign::SEEE, $phones, replaceParams($smsContent, $params));
            }
        }

        // 测试定时任务消息
        NoticeRepository::insertSmsSendLog('下午18点定时任务消息', ['18611134083'], date('Y-m-d H:i:s') . "给参加的会议将要开始的参会人发送短信，共有" . count($commonQuery) . "条数据", "");
    }


}
