<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('departments', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('parent_id')->default(0)->comment('父级部门ID');
            $table->string('name')->default('')->comment('部门名称');
            $table->string('code')->default('')->comment('部门编号');
            $table->string('manager', 20)->default('')->comment('部门负责人');
            $table->tinyInteger('status')->default(1)->comment('是否启用：1启用，2禁用');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->json('activity_types')->nullable()->comment('关联会议活动类型（当前一级部门能看哪些类型的会议数据）');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `departments` comment '部门管理表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
