<?php

namespace App\Models\Website;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $title 标题
 * @property int $year 年份
 * @property string|null $content 详情
 * @property string $url 跳转地址
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereUpdater($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventHistory whereYear($value)
 * @mixin \Eloquent
 */
class EventHistory extends Model
{
    use HasFactory,PaginationTrait;

    protected $connection = 'mysql_website';

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }
}
