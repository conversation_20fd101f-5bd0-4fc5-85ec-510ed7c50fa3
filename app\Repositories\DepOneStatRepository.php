<?php

namespace App\Repositories;

use App\Models\Attendee;
use App\Models\Department;
use App\Models\HotelBooking;
use App\Models\Kpi;
use App\Models\Message;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DepOneStatRepository
{
    // 一级部门会议指标数据统计
    public function getOneDepEventKpiDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        // 根据检索条件查询获取列表数据
        $builder = Kpi::join('departments as d1', 'kpis.department_two_id', '=', 'd1.id')
            ->selectRaw('kpis.department_two_id, sum(kpis.invitation_kpi) as invitation_kpi, sum(kpis.visit_kpi) as visit_kpi, d1.name, d1.manager')
            ->where('forum_id', '=', 0)
            ->where('event_id', $event_id)
            ->where('kpis.department_one_id', $one_dep_id)
            ->groupByRaw('kpis.department_two_id, d1.name, d1.manager')
            ->get();

        $list = $builder->each(function (&$item) use ($event_id, $dates) {
            // 实际邀约数
            $item->realistic_invitation_kpi = $this->getEventInvitationKpi($event_id, $item->department_two_id, 1, $dates);
            // 实际到访数
            $item->realistic_visit_kpi = $this->getEventInvitationKpi($event_id, $item->department_two_id, 2, $dates);
            // 邀约指标完成率
            $item->invitation_kpi_completion_rate = $item->realistic_invitation_kpi ? round($item->realistic_invitation_kpi / $item->invitation_kpi * 100, 2) . '%' : '0%';
            // 到访指标完成率
            $item->visit_kpi_completion_rate = $item->realistic_visit_kpi ? round($item->realistic_visit_kpi / $item->visit_kpi * 100, 2) . '%' : '0%';
            // 邀约到访率
            $item->invitation_visit_rate = $item->realistic_visit_kpi && $item->realistic_invitation_kpi ? round($item->realistic_visit_kpi / $item->realistic_invitation_kpi * 100, 2) . '%' : '0%';
        });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取会议实际邀约指标
    private function getEventInvitationKpi($event_id, $department_id, $type, $dates)
    {
        $count = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->where('attendees.event_id', $event_id)
            ->where('channels.department_two_id', $department_id)
            ->where(function ($query) use ($type, $dates) {
                if($type == 1){
                    $query->where('attendees.status', 1);
                    if($dates){
                        $query->whereBetween('attendees.created_at', $dates);
                    }
                }
                if($type == 2){
                    $query->where('attendees.check_in_status', 1);
                    if($dates){
                        $query->whereBetween('attendees.check_in_time', $dates);
                    }
                }
            })
            ->count();
        return $count;
    }

    // 一级部门论坛指标数据统计
    public function getOneDepForumKpiDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');
        $forum_id = $request->input('forum_id');
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        // 根据检索条件查询获取列表数据
        $builder = Kpi::join('departments as d1', 'kpis.department_two_id', '=', 'd1.id')
            ->join('forums', 'kpis.forum_id', '=', 'forums.id')
            ->selectRaw('kpis.department_two_id, d1.name as department_name, d1.manager as department_manager, kpis.forum_id, forums.name as forum_name,
            sum(kpis.invitation_kpi) as invitation_kpi, sum(kpis.visit_kpi) as visit_kpi')
            ->where('kpis.event_id', $event_id)
            ->where(function ($query) use ($forum_id) {
                if($forum_id){
                    $query->where('kpis.forum_id', $forum_id);
                } else {
                    $query->where('kpis.forum_id', '>', 0);
                }
            })
            ->where('kpis.department_one_id', $one_dep_id)
            ->groupByRaw('kpis.department_two_id, d1.name, d1.manager, kpis.forum_id, forums.name')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id, $dates) {
                // 实际邀约数
                $item->realistic_invitation_kpi = $this->getForumInvitationKpi($item->forum_id, $item->department_two_id, 1, $dates);
                // 实际到访数
                $item->realistic_visit_kpi = $this->getForumInvitationKpi($item->forum_id, $item->department_two_id, 2, $dates);
                // 邀约指标完成率
                $item->invitation_kpi_completion_rate = $item->realistic_invitation_kpi ? round($item->realistic_invitation_kpi / $item->invitation_kpi * 100, 2) . '%' : '0%';
                // 到访指标完成率
                $item->visit_kpi_completion_rate = $item->realistic_visit_kpi ? round($item->realistic_visit_kpi / $item->visit_kpi * 100, 2) . '%' : '0%';
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 论坛总指标数据统计
    public function getOneDepForumAllKpiDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        // 根据检索条件查询获取列表数据
        $builder = Kpi::join('forums', 'kpis.forum_id', '=', 'forums.id')
            ->selectRaw('kpis.forum_id, forums.name as forum_name, sum(kpis.invitation_kpi) as invitation_kpi, sum(kpis.visit_kpi) as visit_kpi')
            ->where('kpis.event_id', $event_id)
            ->where('kpis.forum_id', '>', 0)
            ->where('kpis.department_one_id', $one_dep_id)
            ->groupByRaw('kpis.forum_id, forums.name')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id, $dates) {
//                // 实际邀约数
//                $item->realistic_invitation_kpi = $this->getForumInvitationKpi($item->forum_id, null, 1, $dates);
//                // 邀约指标完成率
//                $item->invitation_kpi_completion_rate = $item->realistic_invitation_kpi ? round($item->realistic_invitation_kpi / $item->invitation_kpi * 100, 2) . '%' : '0%';
                // 实际到访数
                $item->realistic_visit_kpi = $this->getForumInvitationKpi($item->forum_id, null, 2, $dates);
                // 到访指标完成率
                $item->visit_kpi_completion_rate = $item->realistic_visit_kpi ? round($item->realistic_visit_kpi / $item->visit_kpi * 100, 2) . '%' : '0%';
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取论坛实际邀约指标
    private function getForumInvitationKpi($forum_id, $department_id, $type, $dates)
    {
        $count = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('attendee_forums', 'attendees.id', '=', 'attendee_forums.attendee_id')
            ->where('attendee_forums.forum_id', $forum_id)
            ->when($department_id, fn($query) => $query->where('channels.department_two_id', $department_id))
            ->where(function ($query) use ($type, $dates) {
                if($type == 1){
                    $query->where('attendees.status', 1);
                    if($dates){
                        $query->whereBetween('attendees.created_at', $dates);
                    }
                }
                if($type == 2){
                    $query->where('attendees.check_in_status', 1);
                    if($dates){
                        $query->whereBetween('attendees.check_in_time', $dates);
                    }
                }
            })
            ->count();

        return $count;
    }

    // 获取参会人员身份统计
    public function getOneDepAttendeeIdentityStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $dates = $request->input('dates');
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        $list = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->selectRaw('identity, count(*) num')
            ->where('attendees.status', 1)
            ->where('attendees.event_id', $event_id)
            ->where('channels.department_one_id', $one_dep_id)
            ->when($dates, function ($query) use ($dates) {
                $query->whereBetween('attendees.created_at', $dates);
            })
            ->groupBy('identity')
            ->get()
        ;
        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取所有事业部近七天邀约参会人人数统计
    public function getOneDepNear7DaysAttendeeStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        // 获取近七天入库的参会人的所有邀约事业部
        $builder = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('departments', 'channels.department_two_id', '=', 'departments.id')
            ->selectRaw('departments.id,departments.name')
            ->where('attendees.status', 1)
            ->where('attendees.event_id', $event_id)
            ->where('channels.department_one_id', $one_dep_id)
            ->whereRaw("DATE(attendees.created_at) > ?", Carbon::now()->subDays(7)->toDateString())
            ->groupBy('departments.id', 'departments.name')
            ->get();

        $list = $builder->each(function (&$item) use ($event_id) {
            $item->near_7_days_stat = $this->getAttendeeNear7DaysStatByDep($item->id, $event_id);
        });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取某个事业部近七天的邀约数
    private function getAttendeeNear7DaysStatByDep($department_id, $event_id)
    {
        $sql = "WITH DateRange AS (
                SELECT CURDATE() - INTERVAL 6 DAY AS `date`
                UNION ALL
                SELECT CURDATE() - INTERVAL 5 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 4 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 3 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 2 DAY
                UNION ALL
                SELECT CURDATE() - INTERVAL 1 DAY
                UNION ALL
                SELECT CURDATE()
            ),
            AttendeeCounts AS (
                SELECT
                    DATE(a.created_at) AS `date`,
                    COUNT(*) AS num
                FROM attendees AS a
                JOIN channels AS c ON a.channel_id = c.id
                WHERE a.`status`=1 AND a.event_id = " . $event_id . " AND c.department_two_id=" . $department_id . "
                GROUP BY DATE(a.created_at)
            )
            SELECT
                d.`date`,
                COALESCE(ac.num, 0) AS num
            FROM DateRange d
            LEFT JOIN AttendeeCounts ac ON d.`date` = ac.`date`
            ORDER BY d.`date`;";
        $res = \DB::select($sql);
        return $res;
    }



    // 酒店预定数据统计
    public function getOneDepHotelBookingDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        // 根据检索条件查询获取列表数据
        $builder = HotelBooking::join('hotels', 'hotel_bookings.hotel_id', '=', 'hotels.id')
            ->selectRaw('hotels.id, hotels.name, hotel_bookings.booking_date,
            sum(hotel_bookings.person_number) as person_number, sum(hotel_bookings.room_number) as room_number,
            sum(hotel_bookings.standard_room_number) as standard_room_number, sum(hotel_bookings.large_bed_room_number) as large_bed_room_number')
            ->where('hotels.event_id', $event_id)
            ->groupByRaw('hotels.id, hotels.name, hotel_bookings.booking_date')
            ->orderBy('hotels.id')
            ->orderBy('hotel_bookings.booking_date')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id, $one_dep_id) {
                // 标间 实际入住人数
                $item->actual_standard_room_number = $this->getHotelActualCheckInNum($event_id, $item->id, $item->booking_date, 1, $one_dep_id);
                // 大床房 实际入住人数
                $item->actual_large_bed_room_number = $this->getHotelActualCheckInNum($event_id, $item->id, $item->booking_date, 2, $one_dep_id);
                // 到访指标完成率
                $item->room_rate = round(($item->actual_standard_room_number + $item->actual_large_bed_room_number) / $item->room_number * 100, 2) . '%';
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取酒店实际入住人数
    private function getHotelActualCheckInNum($event_id, $hotel_id, $booking_date, $room_type, $one_dep_id)
    {
        $count = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->leftJoin('channels', 'attendees.channel_id', '=', 'channels.id')
            ->leftJoin('users', 'attendees.user_id', '=', 'users.id')
            ->selectRaw('sum(attendee_hotels.booking_room_number) as booking_room_number')
            ->where('attendees.status', 1)
            ->where('attendees.event_id', $event_id)
            // 渠道和对接人部门都为当前一级部门
            ->where(function ($query) use ($one_dep_id) {
                $query->where('channels.department_one_id', $one_dep_id)
                    ->orWhere('users.department_one_id', $one_dep_id);
            })
            ->where('attendee_hotels.hotel_id', $hotel_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            ->where('attendee_hotels.check_in_date', $booking_date)
            ->where('attendee_hotels.room_type', $room_type)
            ->first()
            ->booking_room_number ?? 0;

        return $count;
    }

    // 某个酒店各事业部入住人数统计
    public function getOneDepHotelCheckInNumStat(Request $request)
    {
        $event_id = $request->input('event_id');
        $hotel_id = $request->input('hotel_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        if(empty($hotel_id)){
            return FAIL_RESPONSE_ARRAY('请选择酒店');
        }
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        $list = Attendee::join('attendee_hotels', 'attendees.id', '=', 'attendee_hotels.attendee_id')
            ->leftJoin('channels', 'attendees.channel_id', '=', 'channels.id')
            ->leftJoin('users', 'attendees.user_id', '=', 'users.id')
            // 渠道和对接人部门都为当前一级部门
            ->join('departments', function($join) {
                $join->on('channels.department_one_id', '=', 'departments.id')
                    ->orOn('users.department_one_id', '=', 'departments.id');
            })
            ->selectRaw('departments.id, departments.name, attendee_hotels.check_in_date, count(*) as num')
            ->where('attendees.status', 1)
            // 渠道和对接人部门都为当前一级部门
            ->where(function ($query) use ($one_dep_id) {
                $query->where('channels.department_one_id', $one_dep_id)
                    ->orWhere('users.department_one_id', $one_dep_id);
            })
            ->where('attendees.event_id', $event_id)
            ->where('attendee_hotels.hotel_id', $hotel_id)
            ->where('attendee_hotels.is_need_hotel', 1)
            ->groupByRaw('departments.id, departments.name, attendee_hotels.check_in_date')
            ->orderBy('departments.id')
            ->orderBy('attendee_hotels.check_in_date')
            ->get();

        return SUCCESS_RESPONSE_ARRAY($list);
    }


    // 获取用餐统计
    public function getOneDepDineDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        $dine_date = $request->input('dine_date');
        $time_type = $request->input('time_type');
        $type = $request->input('type');
        $location = $request->input('location');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        $builder = Attendee::leftJoin('channels', 'attendees.channel_id', '=', 'channels.id')
            ->leftJoin('users', 'attendees.user_id', '=', 'users.id')
            // 渠道和对接人部门都为当前一级部门
            ->join('departments', function($join) {
                $join->on('channels.department_one_id', '=', 'departments.id')
                    ->orOn('users.department_one_id', '=', 'departments.id');
            })
            ->join('attendee_dines', 'attendees.id', '=', 'attendee_dines.attendee_id')
            ->join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
            ->selectRaw('departments.id, departments.name, count(*) AS num')
            ->where('attendees.event_id', $event_id)
            ->where('attendee_dines.is_need_dine', 1)
            // 渠道和对接人部门都为当前一级部门
            ->where(function ($query) use ($one_dep_id) {
                $query->where('channels.department_one_id', $one_dep_id)
                    ->orWhere('users.department_one_id', $one_dep_id);
            })
            ->when($dine_date, fn($query) => $query->where('dines.dine_date', $dine_date))
            ->when($time_type, fn($query) => $query->where('dines.time_type', $time_type))
            ->when($type, fn($query) => $query->where('dines.type', $type))
            ->when($location, fn($query) => $query->where('dines.location', $location))
            ->groupBy('departments.id')
            ->get();

        $list = $builder->each(function ($item) use ($event_id, $dine_date, $time_type, $type, $location) {
            $item->sign_in_num = $this->getDineSignInNum($event_id, $item->id, $dine_date, $time_type, $type, $location);
            // 签到率
            $item->room_rate = round($item->sign_in_num / $item->num * 100, 2) . '%';
        });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取用餐签到人数
    private function getDineSignInNum($event_id, $one_dep_id, $dine_date, $time_type, $type, $location)
    {
        $count = Attendee::leftJoin('channels', 'attendees.channel_id', '=', 'channels.id')
            ->leftJoin('users', 'attendees.user_id', '=', 'users.id')
            ->join('attendee_dines', 'attendees.id', '=', 'attendee_dines.attendee_id')
            ->join('dines', 'attendee_dines.dine_id', '=', 'dines.id')
            ->selectRaw('count(*) AS num')
            ->where('attendees.event_id', $event_id)
            ->where('attendee_dines.is_need_dine', 1)
            ->where('attendee_dines.check_in_status', 1)
            // 渠道和对接人部门都为当前一级部门
            ->where(function ($query) use ($one_dep_id) {
                $query->where('channels.department_one_id', $one_dep_id)
                    ->orWhere('users.department_one_id', $one_dep_id);
            })
            ->when($dine_date, fn($query) => $query->where('dines.dine_date', $dine_date))
            ->when($time_type, fn($query) => $query->where('dines.time_type', $time_type))
            ->when($type, fn($query) => $query->where('dines.type', $type))
            ->when($location, fn($query) => $query->where('dines.location', $location))
            ->first()
            ->num ?? 0;

        return $count;
    }


    // 获取事业部下各部门跟进信息统计
    public function getOneDepFollowDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        $builder = Attendee::leftJoin('channels', 'attendees.channel_id', '=', 'channels.id')
            ->leftJoin('users', 'attendees.user_id', '=', 'users.id')
            // 渠道和对接人部门都为当前一级部门
            ->join('departments', function($join) {
                $join->on('channels.department_one_id', '=', 'departments.id')
                    ->orOn('users.department_one_id', '=', 'departments.id');
            })
            ->selectRaw('departments.id, departments.name, count(*) AS customer_num')
            ->where('attendees.event_id', $event_id)
            // 渠道和对接人部门都为当前一级部门
            ->where(function ($query) use ($one_dep_id) {
                $query->where('channels.department_one_id', $one_dep_id)
                    ->orWhere('users.department_one_id', $one_dep_id);
            })
            ->groupBy('departments.id')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id) {
                $item->message_num = $this->getDepFollowNum($event_id, $item->id);
                // 平均跟进次数
                $item->message_avg_num = $item->message_num / $item->customer_num;
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取跟进消息数量
    private function getDepFollowNum($event_id, $one_dep_id)
    {
        $count = Attendee::leftJoin('channels', 'attendees.channel_id', '=', 'channels.id')
            ->leftJoin('users', 'attendees.user_id', '=', 'users.id')
            ->join('messages', 'attendees.id', '=', 'messages.attendee_id')
            ->selectRaw('count(*) AS num')
            ->where('attendees.event_id', $event_id)
            // 渠道和对接人部门都为当前一级部门
            ->where(function ($query) use ($one_dep_id) {
                $query->where('channels.department_one_id', $one_dep_id)
                    ->orWhere('users.department_one_id', $one_dep_id);
            })
            ->whereIn('messages.source', [Message::$source_user_submit, Message::$source_other_submit])
            ->first()
            ->num ?? 0;

        return $count;
    }

    // 获取事业部下商务跟进信息统计
    public function getOneDepBusinessFollowDataStat(Request $request)
    {
        $event_id = $request->input('event_id');
        if(empty($event_id)){
            return FAIL_RESPONSE_ARRAY('请选择会议');
        }
        $one_dep_id = $request->user()->department_one_id;
        if (empty($one_dep_id))
            return FAIL_RESPONSE_ARRAY('当前登录用户无一级部门信息，请先设置一级部门');

        $builder = User::join('departments', 'users.department_one_id', '=', 'departments.id')
            ->join('departments AS departments_two', 'users.department_two_id', '=', 'departments_two.id')
            ->join('attendees', 'users.id', '=', 'attendees.user_id')
            ->selectRaw('users.id, users.real_name, departments_two.name AS department_two_name, count(*) AS customer_num')
            ->where('attendees.event_id', $event_id)
            ->where('users.department_one_id', $one_dep_id)
            ->groupBy('users.id', 'departments_two.name')
            ->get();

        $list = $builder
            ->each(function (&$item) use ($event_id) {
                $item->message_num = $this->getBusinessFollowNum($event_id, $item->id);
                // 平均跟进次数
                $item->message_avg_num = $item->message_num / $item->customer_num;
            });

        return SUCCESS_RESPONSE_ARRAY($list);
    }

    // 获取商务跟进消息数量
    private function getBusinessFollowNum($event_id, $user_id)
    {
        $count = Attendee::join('channels', 'attendees.channel_id', '=', 'channels.id')
            ->join('messages', 'attendees.id', '=', 'messages.attendee_id')
            ->join('users', 'attendees.user_id', '=', 'users.id')
            ->selectRaw('count(*) AS num')
            ->where('attendees.event_id', $event_id)
            ->where('users.id', $user_id)
            ->whereIn('messages.source', [Message::$source_user_submit, Message::$source_other_submit])
            ->first()
            ->num ?? 0;

        return $count;
    }
}
