<?php

namespace App\Http\Controllers\Applet;
use App\Http\Controllers\Controller;
use App\Models\Attendee;
use App\Models\Event;
use App\Models\Order;
use App\Models\RefundOrder;
use App\Models\Registrant;
use App\Models\User;
use App\Repositories\MiniAppRepository;
use EasyWeChat\Kernel\Exceptions\Exception;
use EasyWeChat\Pay\Application;
use EasyWeChat\Pay\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class MiniAppPayController extends Controller
{
    //转账transfer
    function transfer(Request $request)
    {
        //
        $member_id = $request->user()->id;
        $id = $request->input('id');
        //获取pay_certificate_arr数组并转换为逗号分隔的字符串
        $pay_certificate_arr = $request->input('pay_certificate_arr', []);
        $pay_certificate = implode(',', $pay_certificate_arr);
//        $pay_certificate= $request->input('pay_certificate');
        Order::find($id)->update([
            'member_id' => $member_id,
            'pay_type' => 2,
            'pay_status' => 3,
            'pay_certificate' => $pay_certificate,
        ]);
        return SUCCESS_RESPONSE_ARRAY();
    }

    //微信小程序支付
    function payUrl(Request $request)
    {
        $user = $request->user();
        $openid = $user->open_id;
        //获取订单号
        $outTradeNo = $request->input('order_no');
        $amount = intval($request->input('amount') * 100);
        $fee_info = $request->input('fee_info');
        try {
            $app = new Application(config('pay.wechat'));
            $response = $app->getClient()->postJson("v3/pay/transactions/jsapi", [
                "mchid" => config('pay.wechat.mch_id'), // <---- 商户号
                "out_trade_no" => $outTradeNo,
                "appid" => config('pay.wechat.app_id'), // <----小程序appid
                "description" => $fee_info,
                "notify_url" => "https://conference-api.114study.com/api/applet/payment_notify",
                "amount" => [
                    "total" => $amount,
                    "currency" => "CNY"
                ],
                "payer" => [
                    "openid" => $openid // <---- 下单用户的 openid
                ]
            ])->toArray(false);
            $appId = config('pay.wechat.app_id');
            $signType = 'RSA'; // 默认RSA，v2要传MD5
            $config = $app->getUtils()->buildMiniAppConfig($response['prepay_id'], $appId, $signType); // 返回数组
            //更新order表的member_id
            Order::where('order_no', $outTradeNo)->update(['member_id' => $user->id]);
            return SUCCESS_RESPONSE_ARRAY($config);
        } catch (TransportExceptionInterface $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }
    }



    //退款refund
    public function refund(Request $request)
    {
        $order_id = $request->input('order_id');
        $order = Order::query()->where('id', $order_id)->first();

        $refund_no = uniqid();
        $member_id = $request->user()->id;
        $amount = intval($request->input('amount') * 100);
        $remark = $request->input('remark');
        $creator = "客户id：" . $request->user()->id . ' 手机号：' . $request->user()->phone;

        //添加退费记录
        $refund_data = [
            'order_id' => $order_id,
            'member_id' => $member_id,
            'amount' => $request->input('amount'),
            'remark' => $remark,
            'status' => 2,
            'creator' => $creator,
            'refund_no' => $refund_no,
        ];
        RefundOrder::forceCreate($refund_data);

        //查询该笔订单的所有退费金额 判断是否和支付金额相等
        $refund_amount = RefundOrder::query()->where('order_id', $order_id)->sum('amount');
        if ($refund_amount == $order->amount) {
            //更新订单状态
            Order::query()->where('id', $order_id)->update(['pay_status' => 6]);
        }
        //如果是转账支付则添加退费记录，如果是微信支付则走微信退费流程
        if ($order->pay_type == 2) {
            return SUCCESS_RESPONSE_ARRAY();
        }
        //todo 微信退费
        try {
            $outTradeNo = $order->order_no;
            $allAmount = intval($order->amount * 100);
            $app = new Application(config('pay.wechat'));
            $response = $app->getClient()->postJson("v3/refund/domestic/refunds", [
                "out_trade_no" => $outTradeNo,
                "out_refund_no" => $refund_no,
                "reason" => $remark,
                "notify_url" => "https://conference-api.114study.com/api/applet/refund_notify",
                "amount" => [
                    "refund" => $amount,
                    "total" => $allAmount,
                    "currency" => "CNY"
                ],
            ])->toArray(false);
            return SUCCESS_RESPONSE_ARRAY($response);
        } catch (TransportExceptionInterface $e) {
            return FAIL_RESPONSE_ARRAY($e->getMessage());
        }

    }

    //微信支付回调
    function paymentNotify(Request $request)
    {
        $app = new Application(config('pay.wechat'));
        $server = $app->getServer();

        // 处理支付结果事件
        $server->handlePaid(function (Message $message, \Closure $next) use ($app) {
            try{
                Log::error('微信支付回调 $message: '.$message);
//                $app->getValidator()->validate($app->getRequest());
                // $message 为微信推送的通知结果，详看微信官方文档
                // 微信支付订单号 $message['transaction_id']
                // 商户订单号 $message['out_trade_no']
                // 商户号 $message['mchid']
                // 具体看微信官方文档...
                // 进行业务处理，如存数据库等...

                // 验证通过，业务处理
                $out_trade_no = $message['out_trade_no'];
                //根据订单号查询订单信息
                $order = Order::where('order_no', $out_trade_no)->first();
                //获取交易状态trade_state
                $trade_state = $message['trade_state'];

                //如果交易状态为SUCCESS，则更新订单状态
                if($trade_state == 'SUCCESS'){
                    //更新订单状态
                    $order->pay_status = 2;
                    $order->pay_time = date('Y-m-d H:i:s');
                    //更新时间
                    $order->updated_at = date('Y-m-d H:i:s');
                    $order->save();
                    //查询订单状态
                    $response = $app->getClient()->get("v3/pay/transactions/out-trade-no/".$out_trade_no, [
                        'query' => [
                            'mchid' => $app->getMerchant()->getMerchantId()
                        ]
                    ]);
                    Log::error('微信支付回调查询订单状态响应 $response: '.$response);
                    Log::error('微信支付回调查询订单状态响应 $response_trade_state: '.$response->toArray()['trade_state']);

                    //推送微信通知
                    //根据id查询报名人
                    /*$user = User::find($order->member_id);
                    $template_id = 'Tz1trE_39Bhno40Ov_hKGrnYyWP8Say-vn4TC0Nqv8U';
                    $formattedData = [
                        "character_string2" => [
                            "value" => $order->order_no,
                        ],
                        "amount3" => [
                            "value" => $order->amount,
                        ],
                        "time8" => [
                            "value" => $order->pay_time,
                        ],
                        "thing9" => [
                            "value" => '支付成功'
                        ],
                        "thing14" => [
                            "value" => '微信支付',
                        ]
                    ];
                    $open_id = $user->open_id;
                    $page = 'pages/customer/mine/mine';
                    MiniAppRepository::sendMiniAppMessage($template_id,$formattedData,$open_id,$page);*/
                }
                //查询订单状态
                /*$response = $app->getClient()->get("v3/pay/transactions/out-trade-no/{$out_trade_no}", [
                    'query'=>[
                        'mchid' =>  $app->getMerchant()->getMerchantId()
                    ]
                ]);
                print_r($response->toArray());*/
                return $next($message);

            } catch(Exception $e){
                // 验证失败
                Log::error('微信支付回调验证失败 $e: '.$e);
                Log::error('微信支付回调验证失败 $message: '.$message);
            }
        });
        return $server->serve();
    }

    function refundNotify(Request $request)
    {
        $app = new Application(config('pay.wechat'));
        $server = $app->getServer();
        // 处理退款结果事件
        $server->handleRefunded(function ($message, \Closure $next) {
            try {
                Log::error('微信退款回调 $message: '. json_encode($message));
                // 同上，$message 详看微信官方文档
                // 进行业务处理，如存数据库等...
                // 验证通过，业务处理
                $out_trade_no = $message['out_trade_no'];//根据订单号查询订单信息
                $order = Order::where('order_no', $out_trade_no)->first();//获取交易状态trade_state
                $trade_state = $message['refund_status'];//如果交易状态为SUCCESS，则更新订单状态
                if ($trade_state == 'SUCCESS') {
                    //判断是否全额退款
                    $amount = $message['amount'];
                    Log::error('微信退款回调 $amount: '. json_encode($amount));
                    // 获取 $amount 数组中的 total 和 refund
                    $total = $amount['total'];
                    $refund = $amount['refund'];
                    //更新订单状态 TODO 判断是否全额退款
                    if ($total == $refund) {
                        $order->pay_status = 4;
                    }
                    //更新时间
                    $order->updated_at = date('Y-m-d H:i:s');
                    $order->save();
                    //更新退款订单状态
                    $refund_no = $message['out_refund_no'];
                    RefundOrder::where('refund_no', $refund_no)->update(['status' => 1]);
                }
                return $next($message);
            } catch (\Exception $e) {
                // 验证失败
                Log::error('微信退款回调验证失败 $e: '.$e);
                Log::error('微信退款回调验证失败 $message: '. json_encode($message));
            }
        });
        return $server->serve();

    }
}
