<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        //
        '/api/applet/payment_notify',
        'api/applet/payment_notify',
        'applet/payment_notify',
        'payment_notify',
        '/api/applet/refund_notify',
        'api/applet/refund_notify',
        'applet/refund_notify',
        'refund_notify',
        'wechat',
    ];
}
