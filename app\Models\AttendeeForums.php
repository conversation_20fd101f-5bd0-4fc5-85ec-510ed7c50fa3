<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/3/28
 * Time 15:54
 */

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AttendeeForums extends Model
{
    use HasFactory, PaginationTrait;

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function attendee()
    {
        return $this->belongsTo(Attendee::class);
    }

    public function forum()
    {
        return $this->belongsTo(Forum::class);
    }

    //会议信息
    public function event()
    {
        return $this->hasOne(Event::class, 'id', 'event_id');
    }

    public static function convertAuditStatus($status)
    {
        switch ($status) {
            case 1:
                return '审核通过';
            case 3:
                return '审核驳回';
            default:
                return '待确认';
        }
    }
}
