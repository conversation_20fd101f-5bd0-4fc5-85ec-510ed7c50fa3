<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 *
 *
 * @property int $id
 * @property int $event_id 会议id
 * @property string $title 付费内容标题
 * @property string $cover 付费内容封面
 * @property string $introduction 付费内容介绍
 * @property string $price 付费内容价格
 * @property int $is_on 内容状态（1:上架 2:下架）
 * @property string $video_url 视频VideoID
 * @property string|null $creator 添加人
 * @property string|null $updater 最后更新人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Event|null $event
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent query()
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereCover($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereIntroduction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereIsOn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereUpdater($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaidContent whereVideoUrl($value)
 * @mixin \Eloquent
 */
class PaidContent extends Model
{
    use HasFactory, PaginationTrait;

    protected $guarded = [];

    /**
     * 为数组 / JSON 序列化准备日期。
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }


    // 定义一个访问器，处理 cover 字段的前缀添加逻辑
    public function getCoverAttribute($value)
    {
        return config('filesystems.disks.sftp.domain') . $value;
    }

    public function setCoverAttribute($value)
    {
        // 假设 config('filesystems.disks.sftp.domain') 返回的是前缀
        $prefix = config('filesystems.disks.sftp.domain');

        // 检查 $value 是否以前缀开头
        if (strpos($value, $prefix) === 0) {
            // 如果是，则去掉前缀
            $value = substr($value, strlen($prefix));
        }

        // 设置 cover 属性
        $this->attributes['cover'] = $value;
    }

    //付费内容属于会议
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class, 'event_id');
    }
}
