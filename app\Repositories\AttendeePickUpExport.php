<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/7
 * Time 9:20
 */

namespace App\Repositories;

use App\Models\Attendee;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AttendeePickUpExport implements FromCollection, WithTitle, WithStyles, WithHeadings, WithColumnWidths
{
    use Exportable, RegistersEventListeners;

    protected $searchResults;
    public $count;

    public function __construct(Collection $searchResults)
    {
        $this->searchResults = $searchResults;
        $this->count = $searchResults->count();
    }

    public function collection()
    {
        return $this->searchResults->map(function ($result) {

            try {
                return [
                    $result['id'],
                    $result['event_short_title'],
                    $result['attendee_name'],
                    RegistrantExport::hidePhoneNumber($result['attendee_phone']),
                    Attendee::convertIdentityType($result['attendee_identity']),
                    $result['attendee_organization'],
                    $result['attendee_position'],
                    $result['plan_arrive_time'],
                    $result['station_info'],
                    $result['place'],
                    $result['to_place'],
                    $result['person'],
                    $result['tel'],
                    $result['user_real_name'],
                    $result['updater'],
                    $result['updated_at'],
                    $result['is_need_pick_up'] === 1 ? '需要' : '不需要',
                    $result['hotel_contact'],
                    $result['standard'],
                    $result['car_one'],
                    $result['car_two'],
                    $result['is_car_following'] === 1 ? '需要' : '不需要',
                ];
            } catch (\Exception $e) {
                return [];
            }
        });
    }

    public function title(): string
    {
        return '参会人接站名单服务列表';
    }

    public function headings(): array
    {
        // 返回Excel表头
        return [
            ['参会人接站名单服务列表'],
            [
                '编号',
                '会议标题',
                '参会人姓名',
                '参会人手机号',
                '身份类别',
                '所在单位',
                '职称职务',
                '预计到达时间',
                '高铁车次/飞机航班',
                '接站地点',
                '送达地点',
                '接站人',
                '接站人联系方式',
                '对接人',
                '最后更新人',
                '最后更新时间',
                '是否需要接站',
                '酒店对接人',
                '用车标准',
                '接待车辆1',
                '接待车辆2',
                '是否跟车',
            ]
        ];
    }

    // 设置宽度
    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 30,
            'C' => 15,
            'D' => 15,
            'E' => 15,
            'F' => 15,
            'G' => 15,
            'H' => 25,
            'I' => 15,
            'J' => 10,
            'K' => 10,
            'L' => 10,
            'M' => 10,
            'N' => 10,
            'O' => 10,
            'P' => 18,
            'Q' => 18,
            'R' => 18,
            'S' => 18,
            'T' => 18,
            'U' => 18,
            'V' => 18,
        ];
    }

    //设置单元格样式

    public function styles(Worksheet $sheet)
    {
        // 合并单元格
        $sheet->mergeCells('A1:V1');
        // 设置字体颜色
        $sheet->getStyle('A1')->getFont()->getColor()->setRGB('000000');
        $sheet->getStyle('A1:V2')->applyFromArray([
            //设置水平居中
            'alignment' => ['horizontal' => 'center'],
            //设置字体加粗、大小
            'font'      => ['bold' => true, 'size' => 18],
            // 设置单元格背景颜色
            'fill'      => ['fillType' => 'solid', 'startColor' => ['rgb' => 'e9e9eb']],
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);
        $sheet->getStyle('A2:V2')->applyFromArray([
            //设置字体大小
            'font'      => ['size' => 12],
        ]);

        // 设置其他单元格边框
        $sheet->getStyle('A3:V' . ($this->count + 2))->applyFromArray([
            // 设置单元格边框
            'borders'   => ['allBorders' => ['borderStyle' => 'thin', 'color' => ['rgb' => '909399']]],
        ]);
    }



}
