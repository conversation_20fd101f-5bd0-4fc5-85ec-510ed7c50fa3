<?php

namespace App\Console\Commands;

use App\Models\Attendee;
use App\Models\AttendeeDropOff;
use App\Models\AttendeeHotel;
use App\Models\AttendeePickUp;
use App\Models\Event;
use App\Repositories\NoticeRepository;
use AppletUrl;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Sign;

class SmsSendTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sms-send-test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建每天10点定时短信发送任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->sentTest();

        return 0;
    }

    // 给需要接站但接站信息未填写全的参会人发送短信
    function sentTest()
    {
        // 测试定时任务消息
        NoticeRepository::insertSmsSendLog('测试定时任务消息', ['18611134083'], date('Y-m-d H:i:s') . "测试定时任务消息", "");
    }


}
