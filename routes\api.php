<?php

use App\Http\Controllers\Admin\Event\EventController;
use App\Http\Controllers\Admin\CheckInController;
use App\Http\Controllers\Admin\System\UserController;
use App\Http\Controllers\Tool\UploadController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group([], function () {
    require base_path('routes/admin.php');
    require base_path('routes/website.php');
    require base_path('routes/applet.php');
});

// 工具类 ，不需要授权即可访问
Route::group([], function () {
    Route::post('upload_image', [UploadController::class, 'uploadImage'])->name('upload_image');
    Route::post('upload_file', [UploadController::class, 'uploadFile'])->name('upload_file');
    Route::post('upload_tinymce_picture', [UploadController::class, 'uploadTinyMceImage'])->name('upload_tinymce_picture');
    Route::post('system/users/modify_password', [UserController::class, 'modifyPassword'])->name('system.users.modify_password'); //修改密码
});

Route::get('/info', function (Request $request) {
    echo phpinfo();
});

Route::get('/check_email/{token}', [UserController::class, 'checkEmail'])->name('users.check_email');


// SEEE PC签到
Route::group(['prefix' => 'checkIn'], function () {
    Route::get('event/{id}', [EventController::class, 'show'])->name('checkIn.events.show')->where('id', '[0-9]+');
    Route::post('/index', [CheckInController::class, 'index'])->name('checkIn.index');
    Route::get('/byName/organizations', [CheckInController::class, 'getOrganizationList'])->name('checkIn.byName.organizations');
    Route::post('byName', [CheckInController::class, 'checkInByName'])->name('checkIn.byName');
});
