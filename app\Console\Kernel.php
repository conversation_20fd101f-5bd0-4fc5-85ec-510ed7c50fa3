<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        if (env('APP_DEBUG')) $schedule->command('telescope:prune')->daily();

        // 每分钟执行
        $schedule->command('app:sms-send-every-minute-command')->everyMinute()->onOneServer();

        // 每天上午10点执行
        $schedule->command('app:sms-send-every-day10-command')->dailyAt('10:00')->onOneServer();

        // 每天下午18点执行
        $schedule->command('app:sms-send-every-day18-command')->dailyAt('17:30')->onOneServer();

    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
