<?php
/**
 * Created by conference_api
 * User wwt
 * Date 2024/4/9
 * Time 9:38
 */

namespace App\Repositories;

use App\Models\PayRecord;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\ToModel;
use Illuminate\Support\Collection;

class PayRecordImport implements ToModel
{
    private $creator;
    private $updater;
    private $rowNumber = 0;
    private $importedData = [];
    private $failedData = [];

    public function __construct( Request $request)
    {
        $this->creator = $request->user()->real_name;
        $this->updater = $request->user()->real_name;
    }

    public function model(array $row)
    {
        // 忽略第一行（标题行）
        if ($this->rowNumber == 0) {
            $this->rowNumber++;
            return null;
        }

        $event_title = $row[0];
        $pay_user = $row[1];
        $phone = $row[2];
        $organization = $row[3];
        $amount = $row[4];
        $status = $row[5];
        $pay_time = $row[6];

        $status = $this->convertStatusType($status);
        $imported = new PayRecord([
            'event_title' => $event_title,
            'pay_user' => $pay_user,
            'phone' => $phone,
            'organization' => $organization,
            'amount' => $amount,
            'status' => $status,
            'pay_time' => $pay_time,
            'creator' => $this->creator,
            'updater' => $this->updater,
        ]);

        // 将导入成功的数据保存到数组中
        $this->importedData[] = $imported->toArray();

        return $imported;
    }
    public function getImportedData(): Collection
    {
        // 返回导入成功的数据集合
        return collect($this->importedData);
    }
    public function getFailedData(): Collection
    {
        // 返回重复数据集合
        return collect($this->failedData);
    }

    protected function convertStatusType($status)
    {
        // 缴费状态 1.待缴费 2.已缴费 3.已退款
        switch ($status) {
            case '待缴费':
                return 1;
            case '已缴费':
                return 2;
            case '已退款':
                return 3;
            default:
                return 1;
        }
    }

}
